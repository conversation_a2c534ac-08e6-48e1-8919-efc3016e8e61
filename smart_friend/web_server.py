# -*- coding: utf-8 -*-
"""
智能学习伙伴Web服务器
提供完整的Web API和静态文件服务
"""

import asyncio
import logging
import json
import base64
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# 导入主工作流程
from mainlyk import SmartFriendWorkflow, process_text_task, process_voice_task, process_image_task

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="智能学习伙伴API",
    description="多模态输入的智能学习任务处理和计划生成系统",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局工作流程实例
workflow = SmartFriendWorkflow()

# 请求模型
class TextTaskRequest(BaseModel):
    child_id: int
    text_content: str
    enable_tts: bool = False

class VoiceTaskRequest(BaseModel):
    child_id: int
    audio_data: str  # base64编码的音频数据
    enable_tts: bool = False

class ImageTaskRequest(BaseModel):
    child_id: int
    image_data: str  # base64编码的图像数据
    use_multimodal: bool = True
    enable_tts: bool = False

class TaskStatusResponse(BaseModel):
    success: bool
    message: str
    child_id: Optional[int] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    steps: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页面"""
    try:
        html_file = Path("smart_friend.html")
        if html_file.exists():
            return HTMLResponse(content=html_file.read_text(encoding='utf-8'))
        else:
            return HTMLResponse(content="""
            <html>
                <head><title>智能学习伙伴</title></head>
                <body>
                    <h1>智能学习伙伴系统</h1>
                    <p>smart_friend.html 文件未找到</p>
                    <p>请确保文件在正确的位置</p>
                </body>
            </html>
            """)
    except Exception as e:
        logger.error(f"读取主页面失败: {e}")
        return HTMLResponse(content=f"<h1>错误</h1><p>{str(e)}</p>", status_code=500)


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.post("/api/process/text", response_model=TaskStatusResponse)
async def process_text_input(request: TextTaskRequest):
    """处理文本输入"""
    try:
        logger.info(f"收到文本处理请求: 学生ID={request.child_id}")
        
        result = await process_text_task(
            text_content=request.text_content,
            child_id=request.child_id,
            enable_tts=request.enable_tts
        )
        
        return TaskStatusResponse(**result)
        
    except Exception as e:
        logger.error(f"处理文本输入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/process/voice", response_model=TaskStatusResponse)
async def process_voice_input(request: VoiceTaskRequest):
    """处理语音输入"""
    try:
        logger.info(f"收到语音处理请求: 学生ID={request.child_id}")
        
        # 解码base64音频数据
        try:
            audio_data = base64.b64decode(request.audio_data)
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"音频数据解码失败: {str(e)}")
        
        result = await process_voice_task(
            audio_data=audio_data,
            child_id=request.child_id,
            enable_tts=request.enable_tts
        )
        
        return TaskStatusResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理语音输入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/process/image", response_model=TaskStatusResponse)
async def process_image_input(request: ImageTaskRequest):
    """处理图像输入"""
    try:
        logger.info(f"收到图像处理请求: 学生ID={request.child_id}")
        
        result = await process_image_task(
            image_data=request.image_data,
            child_id=request.child_id,
            use_multimodal=request.use_multimodal,
            enable_tts=request.enable_tts
        )
        
        return TaskStatusResponse(**result)
        
    except Exception as e:
        logger.error(f"处理图像输入失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload/voice")
async def upload_voice_file(
    child_id: int = Form(...),
    enable_tts: bool = Form(False),
    audio_file: UploadFile = File(...)
):
    """上传语音文件处理"""
    try:
        logger.info(f"收到语音文件上传: 学生ID={child_id}, 文件={audio_file.filename}")
        
        # 读取音频文件
        audio_data = await audio_file.read()
        
        result = await process_voice_task(
            audio_data=audio_data,
            child_id=child_id,
            enable_tts=enable_tts
        )
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"处理语音文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload/image")
async def upload_image_file(
    child_id: int = Form(...),
    use_multimodal: bool = Form(True),
    enable_tts: bool = Form(False),
    image_file: UploadFile = File(...)
):
    """上传图像文件处理"""
    try:
        logger.info(f"收到图像文件上传: 学生ID={child_id}, 文件={image_file.filename}")
        
        # 读取图像文件并转换为base64
        image_data = await image_file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        result = await process_image_task(
            image_data=image_base64,
            child_id=child_id,
            use_multimodal=use_multimodal,
            enable_tts=enable_tts
        )
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"处理图像文件失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/children")
async def get_children():
    """获取学生列表"""
    # 这里可以从数据库获取真实的学生列表
    # 目前返回模拟数据
    return {
        "children": [
            {"id": 1, "name": "小明", "grade": "三年级"},
            {"id": 2, "name": "小红", "grade": "四年级"},
            {"id": 3, "name": "小刚", "grade": "五年级"}
        ]
    }


@app.get("/api/status/{child_id}")
async def get_child_status(child_id: int):
    """获取学生状态信息"""
    try:
        # 这里可以从数据库获取学生的详细信息
        return {
            "child_id": child_id,
            "status": "active",
            "last_activity": datetime.now().isoformat(),
            "total_tasks": 0,
            "completed_tasks": 0
        }
    except Exception as e:
        logger.error(f"获取学生状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.exception_handler(404)
async def not_found_handler(request, exc):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={"message": "页面未找到", "path": str(request.url)}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    """500错误处理"""
    logger.error(f"内部服务器错误: {exc}")
    return JSONResponse(
        status_code=500,
        content={"message": "内部服务器错误", "detail": str(exc)}
    )


def create_app():
    """创建应用实例"""
    return app


async def main():
    """主函数"""
    logger.info("启动智能学习伙伴Web服务器...")
    
    # 检查必要文件
    html_file = Path("smart_friend.html")
    if not html_file.exists():
        logger.warning("smart_friend.html 文件未找到，将使用默认页面")
    
    # 启动服务器
    config = uvicorn.Config(
        app=app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
    
    server = uvicorn.Server(config)
    
    logger.info("智能学习伙伴Web服务器启动成功!")
    logger.info("访问地址: http://localhost:8000")
    logger.info("API文档: http://localhost:8000/docs")
    
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main())
