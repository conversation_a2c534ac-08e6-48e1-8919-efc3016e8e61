import os
import requests
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import sqlite3
from dataclasses import dataclass, asdict
from sklearn.metrics.pairwise import cosine_similarity

# ===== Configuration =====
DOUBAO_API_KEY: str = os.getenv("DOUBAO_API_KEY", "945a7413-a966-4215-bdbc-80ed84e92555")
DOUBAO_BASE_URL: str = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
DOUBAO_MODEL_NAME: str = os.getenv("DOUBAO_MODEL_NAME", "doubao-1-5-vision-pro-32k-250115")
DOUBAO_TEMPERATURE: float = float(os.getenv("DOUBAO_TEMPERATURE", "0.7"))
DOUBAO_TOP_P: float = float(os.getenv("DOUBAO_TOP_P", "0.9"))
DOUBAO_MAX_TOKENS: int = int(os.getenv("DOUBAO_MAX_TOKENS", "4000"))
DOUBAO_TIMEOUT: int = int(os.getenv("DOUBAO_TIMEOUT", "180"))

# Jina Embedding Configuration
JINA_API_KEY: str = os.getenv("JINA_API_KEY", "jina_your_api_key_here")
JINA_BASE_URL: str = "https://api.jina.ai/v1/embeddings"
JINA_MODEL: str = "jina-embeddings-v3"  # Latest Jina embedding model

# Data paths
DATA_DIR = Path(__file__).parent
INTENT_DATA_PATH = DATA_DIR / "Intent_classification_100_data.json"
SUMMARY_REPORT_PATH = DATA_DIR / "config" / "Summary_report_final.json"
DATABASE_PATH = DATA_DIR / "smart_friend.db"

# Try different possible endpoints (the correct one depends on your specific setup)
DOUBAO_API_ENDPOINTS = [
    f"{DOUBAO_BASE_URL}/chat/completions",
    "https://open.volcengineapi.com/api/v3/chat/completions",
    "https://ark.cn-beijing.volces.com/api/v1/chat/completions"
]

USE_MOCK_MODE = False  # Set to False to use real Doubao API
FALLBACK_TO_MOCK = False  # If API fails, fallback to mock responses

# ===== Data Classes =====
@dataclass
class IntentData:
    content: str
    class_id: int
    intention: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None

@dataclass
class SummaryReport:
    child_profile: Dict[str, str]
    task_completion: Dict[str, str]
    focus_insights: Dict[str, str]
    homework_quality: Dict[str, str]
    time_usage: Dict[str, str]
    plan_adjustments: Dict[str, str]
    suggestions: Dict[str, str]
    encouragement: Dict[str, str]
    timestamp: str = ""

@dataclass
class DatasetEntry:
    id: str
    content: str
    embedding: List[float]
    metadata: Dict[str, Any]
    created_at: str

# ===== Jina Embedding Client =====
class JinaEmbeddingClient:
    def __init__(self):
        self.api_url = JINA_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {JINA_API_KEY}",
            "Content-Type": "application/json"
        }

    def get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a list of texts using Jina API"""
        if USE_MOCK_MODE:
            print(f"[MOCK MODE] Getting embeddings for {len(texts)} texts")
            # Return mock embeddings (768 dimensions for Jina v3)
            return [[0.1] * 768 for _ in texts]

        payload = {
            "model": JINA_MODEL,
            "input": texts,
            "encoding_format": "float"
        }

        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()

            data = response.json()
            embeddings = [item["embedding"] for item in data["data"]]
            print(f"✅ Generated {len(embeddings)} embeddings")
            return embeddings

        except Exception as e:
            print(f"❌ Jina embedding error: {e}")
            # Fallback to mock embeddings
            return [[0.1] * 768 for _ in texts]

    def get_single_embedding(self, text: str) -> List[float]:
        """Get embedding for a single text"""
        return self.get_embeddings([text])[0]

# ===== API Client =====
class DoubaoClient:
    def __init__(self):
        self.api_url = f"{DOUBAO_BASE_URL}/chat/completions"
        self.headers = {
            "Authorization": f"Bearer {DOUBAO_API_KEY}",
            "Content-Type": "application/json"
        }
        self.current_endpoint_index = 0

    def try_api_endpoints(self, base_payload: dict) -> Tuple[Optional[requests.Response], Optional[str]]:
        """Try different API endpoints and models until one works"""
        models_to_try = [
            DOUBAO_MODEL_NAME,
            "doubao-pro-4k",
            "doubao-lite-4k",
            "doubao-pro-32k",
            "doubao-lite-32k"
        ]

        for i, endpoint in enumerate(DOUBAO_API_ENDPOINTS):
            for model in models_to_try:
                try:
                    payload = base_payload.copy()
                    payload["model"] = model

                    print(f"Trying endpoint {i+1}/{len(DOUBAO_API_ENDPOINTS)} with model '{model}': {endpoint}")
                    response = requests.post(
                        endpoint,
                        headers=self.headers,
                        json=payload,
                        timeout=DOUBAO_TIMEOUT
                    )

                    if response.status_code == 200:
                        print(f"✅ Success with endpoint: {endpoint} and model: {model}")
                        self.current_endpoint_index = i
                        return response, None
                    else:
                        error_text = response.text[:300]
                        print(f"❌ Failed with status {response.status_code}: {error_text}")

                        # If it's a model access issue, try next model
                        if "does not exist or you do not have access" in error_text:
                            continue

                except Exception as e:
                    print(f"Error with endpoint {endpoint} and model {model}: {e}")
                    continue

        return None, "All endpoints and models failed"

    def chat(self, prompt: str) -> Dict[str, Any]:
        if USE_MOCK_MODE:
            print(f"[MOCK MODE] Prompt: {prompt[:100]}...")
            if "summarize" in prompt.lower():
                return {"choices": [{"message": {"content": "Mock Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
            return {"choices": [{"message": {"content": "Mock Response: This is a comprehensive answer based on the provided information."}}]}

        payload = {
            "model": DOUBAO_MODEL_NAME,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": DOUBAO_TEMPERATURE,
            "top_p": DOUBAO_TOP_P,
            "max_tokens": DOUBAO_MAX_TOKENS
        }

        try:
            print(f"Payload: {json.dumps(payload, indent=2)}")

            # Try different endpoints
            response, error = self.try_api_endpoints(payload)

            if response is None:
                print(f"All API endpoints failed: {error}")
                print("\n" + "="*60)
                print("🔧 TROUBLESHOOTING GUIDE:")
                print("="*60)
                print("1. Check if your API key is valid and active")
                print("2. Verify you have access to Doubao models in your Volcengine account")
                print("3. Go to https://console.volcengine.com/ark to:")
                print("   - Enable the Doubao models you want to use")
                print("   - Check your account balance/credits")
                print("   - Verify your endpoint configuration")
                print("4. The API key might be for a different service/region")
                print("5. Try using mock mode by setting USE_MOCK_MODE = True")
                print("="*60)

                if FALLBACK_TO_MOCK:
                    print("Falling back to mock response...")
                    if "summarize" in prompt.lower():
                        return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                    return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
                return {"error": error}

            data = response.json()
            print("Raw Doubao response:", json.dumps(data, indent=2))
            return data

        except requests.exceptions.HTTPError as e:
            print(f"HTTP Error: {e}")
            print(f"Response content: {e.response.text if e.response else 'No response content'}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": f"HTTP {e.response.status_code if e.response else 'Unknown'} - {e}"}
        except requests.exceptions.ConnectionError as e:
            print(f"Connection Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": "Could not connect to the API. Please check your internet connection."}
        except requests.exceptions.Timeout as e:
            print(f"Timeout Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": "API request timed out."}
        except Exception as e:
            print(f"Unexpected Error: {e}")
            if FALLBACK_TO_MOCK:
                print("Falling back to mock response...")
                if "summarize" in prompt.lower():
                    return {"choices": [{"message": {"content": "Fallback Summary: Key points include mathematics fundamentals, scientific methodology, and their interconnected applications."}}]}
                return {"choices": [{"message": {"content": "Fallback Response: This is a comprehensive answer based on the provided information."}}]}
            return {"error": str(e)}

# ===== Dataset Manager =====
class DatasetManager:
    def __init__(self):
        self.embedding_client = JinaEmbeddingClient()
        self.init_database()

    def init_database(self):
        """Initialize SQLite database for storing embeddings and data"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Create tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS intent_data (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                class_id INTEGER,
                intention TEXT,
                metadata TEXT,
                embedding BLOB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS dataset_entries (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                embedding BLOB,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS summary_reports (
                id TEXT PRIMARY KEY,
                report_data TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()
        conn.close()

    def load_intent_data(self) -> List[IntentData]:
        """Load intent classification data from JSON file"""
        try:
            with open(INTENT_DATA_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)

            intent_data = []
            for item in data:
                intent_item = IntentData(
                    content=item['content'],
                    class_id=item['class_id'],
                    intention=item['intention'],
                    metadata=item['metadata']
                )
                intent_data.append(intent_item)

            print(f"✅ Loaded {len(intent_data)} intent classification entries")
            return intent_data

        except Exception as e:
            print(f"❌ Error loading intent data: {e}")
            return []

    def generate_embeddings_for_intent_data(self):
        """Generate and store embeddings for intent classification data"""
        intent_data = self.load_intent_data()
        if not intent_data:
            return

        # Extract texts for embedding
        texts = [item.content for item in intent_data]

        # Generate embeddings
        embeddings = self.embedding_client.get_embeddings(texts)

        # Store in database
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        for item, embedding in zip(intent_data, embeddings):
            item_id = f"intent_{item.class_id}_{hash(item.content) % 10000}"
            cursor.execute('''
                INSERT OR REPLACE INTO intent_data
                (id, content, class_id, intention, metadata, embedding)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                item_id,
                item.content,
                item.class_id,
                item.intention,
                json.dumps(item.metadata),
                json.dumps(embedding).encode('utf-8')
            ))

        conn.commit()
        conn.close()
        print(f"✅ Stored embeddings for {len(intent_data)} intent entries")

    def add_dataset_entry(self, content: str, metadata: Dict[str, Any]) -> str:
        """Add a new entry to the dataset with embedding"""
        embedding = self.embedding_client.get_single_embedding(content)
        entry_id = f"entry_{hash(content) % 100000}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            INSERT INTO dataset_entries (id, content, embedding, metadata)
            VALUES (?, ?, ?, ?)
        ''', (
            entry_id,
            content,
            json.dumps(embedding).encode('utf-8'),
            json.dumps(metadata)
        ))

        conn.commit()
        conn.close()

        print(f"✅ Added dataset entry: {entry_id}")
        return entry_id

    def search_similar_entries(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for similar entries using embedding similarity"""
        query_embedding = self.embedding_client.get_single_embedding(query)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get all entries with embeddings
        cursor.execute('SELECT id, content, embedding, metadata FROM dataset_entries')
        entries = cursor.fetchall()
        conn.close()

        if not entries:
            return []

        # Calculate similarities
        similarities = []
        for entry_id, content, embedding_blob, metadata in entries:
            try:
                stored_embedding = json.loads(embedding_blob.decode('utf-8'))
                similarity = cosine_similarity([query_embedding], [stored_embedding])[0][0]
                similarities.append({
                    'id': entry_id,
                    'content': content,
                    'metadata': json.loads(metadata),
                    'similarity': float(similarity)
                })
            except Exception as e:
                print(f"Error processing entry {entry_id}: {e}")
                continue

        # Sort by similarity and return top_k
        similarities.sort(key=lambda x: x['similarity'], reverse=True)
        return similarities[:top_k]

# ===== Summary Report Manager =====
class SummaryReportManager:
    def __init__(self):
        self.template_path = SUMMARY_REPORT_PATH
        self.load_template()

    def load_template(self) -> Dict[str, Any]:
        """Load summary report template"""
        try:
            with open(self.template_path, 'r', encoding='utf-8') as f:
                self.template = json.load(f)
            print("✅ Loaded summary report template")
            return self.template
        except Exception as e:
            print(f"❌ Error loading summary template: {e}")
            self.template = {}
            return {}

    def create_summary_report(self, data: Dict[str, Any]) -> SummaryReport:
        """Create a structured summary report from data"""
        template = self.template.get('summary_report_draft', {})

        report = SummaryReport(
            child_profile=self._extract_section(data, template.get('1_child_profile_and_past_performance', {})),
            task_completion=self._extract_section(data, template.get('2_task_plan_and_completion', {})),
            focus_insights=self._extract_section(data, template.get('3_focus_and_behaviour_insights', {})),
            homework_quality=self._extract_section(data, template.get('4_quality_of_homework', {})),
            time_usage=self._extract_section(data, template.get('5_time_and_activity_usage', {})),
            plan_adjustments=self._extract_section(data, template.get('6_homework_plan_adjustments', {})),
            suggestions=self._extract_section(data, template.get('7_personalized_suggestions_from_past_performance', {})),
            encouragement=self._extract_section(data, template.get('8_encouragement_and_motivation', {})),
            timestamp=datetime.now().isoformat()
        )

        return report

    def _extract_section(self, data: Dict[str, Any], template_section: Dict[str, str]) -> Dict[str, str]:
        """Extract and populate a section of the report"""
        section = {}
        for key, template_value in template_section.items():
            # Try to get value from data, fallback to template or empty string
            section[key] = data.get(key, template_value or "")
        return section

    def save_report(self, report: SummaryReport) -> str:
        """Save summary report to database"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        report_id = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute('''
            INSERT INTO summary_reports (id, report_data)
            VALUES (?, ?)
        ''', (report_id, json.dumps(asdict(report))))

        conn.commit()
        conn.close()

        print(f"✅ Saved summary report: {report_id}")
        return report_id

    def get_recent_reports(self, limit: int = 10) -> List[SummaryReport]:
        """Get recent summary reports"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT report_data FROM summary_reports
            ORDER BY created_at DESC LIMIT ?
        ''', (limit,))

        reports = []
        for (report_data,) in cursor.fetchall():
            try:
                data = json.loads(report_data)
                report = SummaryReport(**data)
                reports.append(report)
            except Exception as e:
                print(f"Error loading report: {e}")
                continue

        conn.close()
        return reports

    def generate_ai_summary(self, raw_data: Dict[str, Any], doubao_client) -> str:
        """Generate AI-powered summary using Doubao"""
        prompt = f"""
        Based on the following learning data, create a comprehensive summary report for a child's learning progress:

        Data: {json.dumps(raw_data, indent=2)}

        Please provide insights on:
        1. Learning performance and completion rates
        2. Focus and behavior patterns
        3. Areas of strength and improvement
        4. Personalized recommendations
        5. Encouraging feedback

        Format the response as a friendly, encouraging report suitable for parents and educators.
        """

        response = doubao_client.chat(prompt)
        if "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]
        return "Unable to generate AI summary at this time."

# ===== Intent Classification System =====
class IntentClassifier:
    def __init__(self):
        self.embedding_client = JinaEmbeddingClient()
        self.dataset_manager = DatasetManager()
        self.intent_classes = {
            0: "daily_chat",
            1: "study_create_plan",
            2: "study_modify_plan",
            3: "study_execute_task",
            4: "study_review_progress"
        }

    def classify_intent(self, text: str, threshold: float = 0.7) -> Dict[str, Any]:
        """Classify user intent using embedding similarity"""
        query_embedding = self.embedding_client.get_single_embedding(text)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Get all intent data with embeddings
        cursor.execute('''
            SELECT content, class_id, intention, embedding
            FROM intent_data
        ''')

        intent_entries = cursor.fetchall()
        conn.close()

        if not intent_entries:
            print("⚠️ No intent data found. Please run generate_embeddings_for_intent_data() first.")
            return {
                "predicted_class": 0,
                "predicted_intention": "daily_chat",
                "confidence": 0.0,
                "similar_examples": []
            }

        # Calculate similarities
        similarities = []
        for content, class_id, intention, embedding_blob in intent_entries:
            try:
                stored_embedding = json.loads(embedding_blob.decode('utf-8'))
                similarity = cosine_similarity([query_embedding], [stored_embedding])[0][0]
                similarities.append({
                    'content': content,
                    'class_id': class_id,
                    'intention': intention,
                    'similarity': float(similarity)
                })
            except Exception as e:
                print(f"Error processing intent entry: {e}")
                continue

        # Sort by similarity
        similarities.sort(key=lambda x: x['similarity'], reverse=True)

        # Get top match
        top_match = similarities[0] if similarities else None

        if top_match and top_match['similarity'] >= threshold:
            predicted_class = top_match['class_id']
            predicted_intention = top_match['intention']
            confidence = top_match['similarity']
        else:
            # Default to daily_chat if confidence is low
            predicted_class = 0
            predicted_intention = "daily_chat"
            confidence = top_match['similarity'] if top_match else 0.0

        return {
            "predicted_class": predicted_class,
            "predicted_intention": predicted_intention,
            "confidence": confidence,
            "similar_examples": similarities[:3]  # Top 3 similar examples
        }

    def add_training_example(self, text: str, class_id: int, intention: str, metadata: Dict[str, Any] = None):
        """Add a new training example to the intent classification dataset"""
        if metadata is None:
            metadata = {}

        embedding = self.embedding_client.get_single_embedding(text)

        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        example_id = f"intent_{class_id}_{hash(text) % 10000}"
        cursor.execute('''
            INSERT OR REPLACE INTO intent_data
            (id, content, class_id, intention, metadata, embedding)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            example_id,
            text,
            class_id,
            intention,
            json.dumps(metadata),
            json.dumps(embedding).encode('utf-8')
        ))

        conn.commit()
        conn.close()

        print(f"✅ Added training example: {example_id}")
        return example_id

    def get_intent_statistics(self) -> Dict[str, Any]:
        """Get statistics about the intent classification dataset"""
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT class_id, intention, COUNT(*) as count
            FROM intent_data
            GROUP BY class_id, intention
            ORDER BY class_id
        ''')

        stats = {}
        total_examples = 0

        for class_id, intention, count in cursor.fetchall():
            stats[intention] = {
                'class_id': class_id,
                'count': count
            }
            total_examples += count

        conn.close()

        return {
            'total_examples': total_examples,
            'classes': stats,
            'class_distribution': {k: v['count']/total_examples for k, v in stats.items()}
        }

# ===== Enhanced OpenManus Planner =====
class OpenManusPlanner:
    def __init__(self):
        self.client = DoubaoClient()
        self.embedding_client = JinaEmbeddingClient()
        self.dataset_manager = DatasetManager()
        self.summary_manager = SummaryReportManager()
        self.intent_classifier = IntentClassifier()

        self.tools = {
            "retriever": self.retrieve_information,
            "summarizer": self.summarize_content,
            "response_generator": self.generate_response,
            "intent_classifier": self.classify_user_intent,
            "dataset_search": self.search_dataset,
            "summary_generator": self.generate_summary_report,
            "embedding_generator": self.generate_embeddings
        }

    def call_doubao_api(self, prompt: str) -> str:
        response = self.client.chat(prompt)
        if "error" in response:
            return f"Error: {response['error']}"
        if "choices" in response and len(response["choices"]) > 0:
            return response["choices"][0]["message"]["content"]
        else:
            print(f"Unexpected response format: {response}")
            return "Error: Unexpected API response format."

    def call_openmanus(self, task: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        print(f"Calling OpenManus planner with task: {task}")

        if task == "research_and_summarize":
            return {
                "plan": [
                    {"tool": "intent_classifier", "parameters": {"text": parameters["query"]}},
                    {"tool": "retriever", "parameters": {"query": parameters["query"]}},
                    {"tool": "dataset_search", "parameters": {"query": parameters["query"], "top_k": 3}},
                    {"tool": "summarizer", "parameters": {"content": "{{retriever.output}}", "length": "medium"}},
                    {"tool": "response_generator", "parameters": {"summary": "{{summarizer.output}}", "question": parameters["query"]}}
                ]
            }

        elif task == "intent_classification":
            return {
                "plan": [
                    {"tool": "intent_classifier", "parameters": {"text": parameters["text"]}},
                    {"tool": "response_generator", "parameters": {"question": f"Based on the intent classification, respond to: {parameters['text']}"}}
                ]
            }

        elif task == "generate_summary_report":
            return {
                "plan": [
                    {"tool": "summary_generator", "parameters": {"data": parameters.get("data", {})}},
                    {"tool": "response_generator", "parameters": {"question": "Provide a brief overview of the generated summary report"}}
                ]
            }

        elif task == "semantic_search":
            return {
                "plan": [
                    {"tool": "dataset_search", "parameters": {"query": parameters["query"], "top_k": parameters.get("top_k", 5)}},
                    {"tool": "response_generator", "parameters": {"question": f"Based on the search results, answer: {parameters['query']}"}}
                ]
            }

        elif task == "embedding_generation":
            return {
                "plan": [
                    {"tool": "embedding_generator", "parameters": {"texts": parameters["texts"]}},
                    {"tool": "response_generator", "parameters": {"question": "Embeddings have been generated successfully"}}
                ]
            }

        # Default plan
        return {"plan": [{"tool": "response_generator", "parameters": parameters}]}

    def retrieve_information(self, query: str) -> Dict[str, Any]:
        print(f"Retrieving info for: {query}")
        content = f"Information about {query}: This is a detailed explanation covering key aspects."
        return {"output": content, "status": "success"}

    def summarize_content(self, content: str, length: str = "medium") -> Dict[str, Any]:
        print(f"Summarizing content ({length})")
        prompt = f"Summarize the following content in {length} length:\n\n{content}"
        summary = self.call_doubao_api(prompt)
        return {"output": summary, "status": "success"}

    def generate_response(self, question: str, summary: str = None) -> Dict[str, Any]:
        print("Generating final response")
        if summary:
            prompt = f"Based on the following summary:\n{summary}\n\nAnswer this question: {question}"
        else:
            prompt = f"Answer this question: {question}"
        answer = self.call_doubao_api(prompt)
        return {"output": answer, "status": "success"}

    def classify_user_intent(self, text: str) -> Dict[str, Any]:
        """Classify user intent using the intent classification system"""
        print(f"Classifying intent for: {text[:50]}...")
        result = self.intent_classifier.classify_intent(text)
        return {"output": result, "status": "success"}

    def search_dataset(self, query: str, top_k: int = 5) -> Dict[str, Any]:
        """Search for similar entries in the dataset"""
        print(f"Searching dataset for: {query[:50]}...")
        results = self.dataset_manager.search_similar_entries(query, top_k)
        return {"output": results, "status": "success"}

    def generate_summary_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a structured summary report"""
        print("Generating summary report...")
        report = self.summary_manager.create_summary_report(data)
        report_id = self.summary_manager.save_report(report)

        # Also generate AI-powered summary
        ai_summary = self.summary_manager.generate_ai_summary(data, self.client)

        return {
            "output": {
                "report": asdict(report),
                "report_id": report_id,
                "ai_summary": ai_summary
            },
            "status": "success"
        }

    def generate_embeddings(self, texts: List[str]) -> Dict[str, Any]:
        """Generate embeddings for a list of texts"""
        print(f"Generating embeddings for {len(texts)} texts...")
        embeddings = self.embedding_client.get_embeddings(texts)
        return {"output": embeddings, "status": "success"}

    def execute_plan(self, plan: List[Dict[str, Any]]) -> Dict[str, Any]:
        results = {}
        for step in plan:
            tool = step["tool"]
            params = step["parameters"]
            resolved = {}

            for k, v in params.items():
                if isinstance(v, str) and v.startswith("{{") and v.endswith("}}"):
                    ref = v[2:-2].split(".")[0]
                    resolved[k] = results[ref]["output"]
                else:
                    resolved[k] = v

            if tool in self.tools:
                print(f"Executing {tool} with params: {resolved}")
                results[tool] = self.tools[tool](**resolved)
            else:
                raise Exception(f"Unknown tool: {tool}")

        return results

    def process_task(self, task: str, parameters: Dict[str, Any]) -> str:
        plan = self.call_openmanus(task, parameters).get("plan", [])
        results = self.execute_plan(plan)
        return results.get("response_generator", {}).get("output", "No response generated.")

# ===== Initialization and Setup Functions =====
def initialize_system():
    """Initialize the enhanced OpenManus system"""
    print("🚀 Initializing Enhanced OpenManus System...")

    # Initialize components
    planner = OpenManusPlanner()

    # Generate embeddings for intent data if not already done
    print("📊 Setting up intent classification data...")
    planner.dataset_manager.generate_embeddings_for_intent_data()

    # Show intent statistics
    stats = planner.intent_classifier.get_intent_statistics()
    print(f"✅ Intent classification ready with {stats['total_examples']} examples")

    return planner

def demo_features(planner):
    """Demonstrate the new features"""
    print("\n🎯 Demonstrating Enhanced Features:")

    # 1. Intent Classification Demo
    print("\n1. Intent Classification:")
    test_texts = [
        "Can you help me with my math homework?",
        "Let's chat about something fun!",
        "I need to modify my study schedule"
    ]

    for text in test_texts:
        result = planner.intent_classifier.classify_intent(text)
        print(f"   Text: '{text}'")
        print(f"   Intent: {result['predicted_intention']} (confidence: {result['confidence']:.2f})")

    # 2. Dataset Search Demo
    print("\n2. Semantic Search:")
    search_results = planner.dataset_manager.search_similar_entries("homework help", top_k=3)
    print(f"   Found {len(search_results)} similar entries for 'homework help'")

    # 3. Add new dataset entry
    print("\n3. Adding Dataset Entry:")
    entry_id = planner.dataset_manager.add_dataset_entry(
        "How to solve quadratic equations step by step",
        {"subject": "mathematics", "grade": 9, "difficulty": "medium"}
    )
    print(f"   Added entry: {entry_id}")

    # 4. Summary Report Demo
    print("\n4. Summary Report Generation:")
    sample_data = {
        "completion_rate_percentage": "85%",
        "most_focused_times_or_tasks": "Morning math sessions",
        "total_study_time_and_active_engagement": "2.5 hours with 90% engagement"
    }

    report_result = planner.generate_summary_report(sample_data)
    print(f"   Generated report ID: {report_result['output']['report_id']}")

# ===== Main Execution =====
if __name__ == "__main__":
    try:
        # Initialize the enhanced system
        planner = initialize_system()

        # Test connection
        print("\n🔗 Testing API Connection...")
        test_response = planner.client.chat("Say 'Hello' if you're working")
        connection_status = test_response.get("choices", [{}])[0].get("message", {}).get("content", "Failed")
        print(f"Connection Test: {connection_status}")

        # Demo features
        demo_features(planner)

        # Interactive chat with enhanced features
        print("\n💬 Enhanced Interactive Chat")
        print("Available commands:")
        print("  - Normal chat: Just type your message")
        print("  - /intent <text>: Classify intent of text")
        print("  - /search <query>: Search dataset")
        print("  - /report: Generate sample summary report")
        print("  - /stats: Show intent classification statistics")
        print("  - quit/exit/q: Exit")

        while True:
            user_input = input("\nYou: ").strip()

            if user_input.lower() in ('exit', 'quit', 'q'):
                print("Exiting. Goodbye!")
                break

            # Handle special commands
            if user_input.startswith('/intent '):
                text = user_input[8:]
                result = planner.process_task("intent_classification", {"text": text})
                print(f"\nIntent Classification: {result}")

            elif user_input.startswith('/search '):
                query = user_input[8:]
                result = planner.process_task("semantic_search", {"query": query})
                print(f"\nSearch Results: {result}")

            elif user_input == '/report':
                sample_data = {
                    "completion_rate_percentage": "90%",
                    "most_focused_times_or_tasks": "Afternoon study sessions",
                    "total_study_time_and_active_engagement": "3 hours with 95% engagement"
                }
                result = planner.process_task("generate_summary_report", {"data": sample_data})
                print(f"\nSummary Report Generated: {result}")

            elif user_input == '/stats':
                stats = planner.intent_classifier.get_intent_statistics()
                print(f"\nIntent Classification Statistics:")
                print(f"Total Examples: {stats['total_examples']}")
                for intent, info in stats['classes'].items():
                    print(f"  {intent}: {info['count']} examples ({stats['class_distribution'][intent]:.1%})")

            else:
                # Regular chat with intent classification
                intent_result = planner.intent_classifier.classify_intent(user_input)
                print(f"\n[Intent: {intent_result['predicted_intention']} ({intent_result['confidence']:.2f})]")

                # Process using appropriate task based on intent
                if intent_result['predicted_intention'] == 'study_create_plan':
                    task = "research_and_summarize"
                elif intent_result['predicted_intention'] == 'study_modify_plan':
                    task = "semantic_search"
                else:
                    task = "research_and_summarize"

                response = planner.process_task(task, {"query": user_input})
                print(f"\nAssistant: {response}")

    except KeyboardInterrupt:
        print("\nInterrupted. Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Please check your configuration and try again.")