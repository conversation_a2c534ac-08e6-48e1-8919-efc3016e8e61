# SQLAlchemy ORM 模型 - 每日任务信息
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, date, timedelta
from enum import Enum as PyEnum
from typing import Optional, List, Dict, Any
import logging

# 使用现有的Base
from core.user_management.models.user_models import Base

# 设置日志
logger = logging.getLogger(__name__)


class TaskStatusEnum(PyEnum):
    """任务状态枚举"""
    PENDING = "pending"          # 待执行
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"      # 已完成
    CANCELLED = "cancelled"      # 已取消
    OVERDUE = "overdue"         # 已逾期


class TaskTypeEnum(PyEnum):
    """任务类型枚举"""
    HOMEWORK = "homework"        # 作业
    READING = "reading"          # 阅读
    EXERCISE = "exercise"        # 练习
    PROJECT = "project"          # 项目
    REVIEW = "review"           # 复习
    PREVIEW = "preview"         # 预习
    OTHER = "other"             # 其他


class DailyTask(Base):
    """每日任务主表"""
    __tablename__ = "daily_tasks"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="任务ID")
    child_id = Column(Integer, ForeignKey("children.id"), nullable=False, comment="学生ID")
    task_name = Column(String(200), nullable=False, comment="任务名称")
    task_date = Column(DateTime, nullable=False, comment="任务日期")
    
    # 时间安排
    time_slot = Column(String(50), nullable=True, comment="时段，格式：HH:MM-HH:MM")
    estimated_duration = Column(Integer, nullable=True, comment="预计用时(分钟)")
    actual_duration = Column(Integer, nullable=True, comment="实际用时(分钟)")
    
    # 学科信息
    subject = Column(String(50), nullable=False, comment="学科")
    task_type = Column(String(20), nullable=True, default="homework", comment="任务类型")
    
    # 任务详情
    description = Column(Text, nullable=True, comment="任务描述")
    customization = Column(Text, nullable=True, comment="定制说明")
    difficulty = Column(Text, nullable=True, comment="预期难点")
    solution = Column(Text, nullable=True, comment="解决方案")
    
    # 执行情况
    confidence_index = Column(Integer, nullable=True, comment="执行信心指数(1-5)")
    status = Column(String(20), nullable=False, default="pending", comment="任务状态")
    completion_percentage = Column(Float, nullable=True, default=0.0, comment="完成百分比")
    
    # 评价反馈
    difficulty_rating = Column(Integer, nullable=True, comment="实际难度评分(1-5)")
    satisfaction_rating = Column(Integer, nullable=True, comment="满意度评分(1-5)")
    notes = Column(Text, nullable=True, comment="备注")

    # 积分奖励
    total_points = Column(Integer, nullable=True, default=0, comment="总积分")
    bonus_points = Column(Integer, nullable=True, default=0, comment="奖励积分")
    points_reason = Column(Text, nullable=True, comment="积分说明")
    
    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    child = relationship("Child", backref="daily_tasks")
    task_items = relationship("TaskItem", back_populates="daily_task", cascade="all, delete-orphan")

    @classmethod
    def create_task(cls, session, child_id: int, task_name: str, description: str = None, **kwargs) -> Optional['DailyTask']:
        """创建每日任务记录（简化版）"""
        try:
            # 设置默认值
            task_data = {
                'child_id': child_id,
                'task_name': task_name,
                'description': description or '',
                'task_date': kwargs.get('task_date', datetime.now()),
                'subject': kwargs.get('subject', '其他'),
                'task_type': kwargs.get('task_type', 'homework'),
                'status': kwargs.get('status', 'pending'),
                'completion_percentage': kwargs.get('completion_percentage', 0.0),
                **kwargs
            }

            task = cls(**task_data)
            session.add(task)
            session.flush()  # 获取ID但不提交
            return task
        except SQLAlchemyError as e:
            logger.error(f"创建每日任务失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_id(cls, session, task_id: int) -> Optional['DailyTask']:
        """根据ID获取任务"""
        try:
            return session.query(cls).filter(cls.id == task_id, cls.is_active == True).first()
        except SQLAlchemyError as e:
            logger.error(f"获取任务失败: {e}")
            return None

    @classmethod
    def get_by_child_and_date(cls, session, child_id: int, task_date: date) -> List['DailyTask']:
        """获取指定学生指定日期的任务"""
        try:
            start_date = datetime.combine(task_date, datetime.min.time())
            end_date = datetime.combine(task_date, datetime.max.time())

            return session.query(cls).filter(
                cls.child_id == child_id,
                cls.task_date >= start_date,
                cls.task_date <= end_date,
                cls.is_active == True
            ).order_by(cls.time_slot).all()
        except SQLAlchemyError as e:
            logger.error(f"获取任务列表失败: {e}")
            return []

    @classmethod
    def get_by_date_range(cls, session, child_id: int, start_date: date, end_date: date,
                         subject: Optional[str] = None, status: Optional[str] = None) -> List['DailyTask']:
        """获取指定日期范围的任务"""
        try:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())

            query = session.query(cls).filter(
                cls.child_id == child_id,
                cls.task_date >= start_datetime,
                cls.task_date <= end_datetime,
                cls.is_active == True
            )

            if subject:
                query = query.filter(cls.subject == subject)
            if status:
                query = query.filter(cls.status == status)

            return query.order_by(cls.task_date, cls.time_slot).all()
        except SQLAlchemyError as e:
            logger.error(f"获取任务列表失败: {e}")
            return []

    @classmethod
    def get_yesterday_tasks(cls, session, child_id: int, subject: Optional[str] = None) -> List['DailyTask']:
        """获取昨日任务"""
        try:
            yesterday = date.today() - timedelta(days=1)
            tasks = cls.get_by_child_and_date(session, child_id, yesterday)

            if subject:
                tasks = [task for task in tasks if task.subject == subject]

            return tasks
        except Exception as e:
            logger.error(f"获取昨日任务失败: {e}")
            return []

    def update_task(self, session, **kwargs) -> bool:
        """更新任务信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新任务失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除任务"""
        try:
            self.is_active = False
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"删除任务失败: {e}")
            session.rollback()
            return False

    def add_sub_task(self, session, task_content: str, **kwargs) -> Optional['TaskItem']:
        """添加子任务"""
        try:
            sub_task_data = {
                'daily_task_id': self.id,
                'task_content': task_content,
                **kwargs
            }
            sub_task = TaskItem.create_item(session, **sub_task_data)
            return sub_task
        except Exception as e:
            logger.error(f"添加子任务失败: {e}")
            return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'child_id': self.child_id,
            'task_name': self.task_name,
            'task_date': self.task_date.isoformat() if self.task_date else None,
            'time_slot': self.time_slot,
            'estimated_duration': self.estimated_duration,
            'actual_duration': self.actual_duration,
            'subject': self.subject,
            'task_type': self.task_type,
            'description': self.description,
            'customization': self.customization,
            'difficulty': self.difficulty,
            'solution': self.solution,
            'confidence_index': self.confidence_index,
            'status': self.status,
            'completion_percentage': self.completion_percentage,
            'difficulty_rating': self.difficulty_rating,
            'satisfaction_rating': self.satisfaction_rating,
            'notes': self.notes,
            'total_points': self.total_points,
            'bonus_points': self.bonus_points,
            'points_reason': self.points_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'task_items': [item.to_dict() for item in self.task_items] if self.task_items else []
        }


class TaskItem(Base):
    """任务子项表"""
    __tablename__ = "task_items"

    # 基本信息
    id = Column(Integer, primary_key=True, autoincrement=True, comment="子任务ID")
    daily_task_id = Column(Integer, ForeignKey("daily_tasks.id"), nullable=False, comment="主任务ID")
    task_content = Column(Text, nullable=False, comment="子任务内容")
    task_source = Column(String(100), nullable=True, comment="任务来源")

    # 时间规划
    time_slot = Column(String(50), nullable=True, comment="时间段，格式：HH:MM-HH:MM")
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    order_index = Column(Integer, nullable=True, comment="执行顺序")

    # 执行情况
    is_completed = Column(Boolean, default=False, comment="是否完成")
    completion_time = Column(DateTime, nullable=True, comment="完成时间")
    estimated_minutes = Column(Integer, nullable=True, comment="预计用时(分钟)")
    actual_minutes = Column(Integer, nullable=True, comment="实际用时(分钟)")
    
    # 评价
    difficulty_rating = Column(Integer, nullable=True, comment="难度评分(1-5)")
    quality_rating = Column(Integer, nullable=True, comment="质量评分(1-5)")
    notes = Column(Text, nullable=True, comment="备注")

    # 积分奖励
    points_earned = Column(Integer, nullable=True, default=0, comment="获得积分")
    bonus_points = Column(Integer, nullable=True, default=0, comment="奖励积分")
    points_reason = Column(Text, nullable=True, comment="积分说明")
    
    # 系统信息
    is_active = Column(Boolean, default=True, comment="是否激活")
    created_at = Column(DateTime, default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    daily_task = relationship("DailyTask", back_populates="task_items")

    @classmethod
    def create_item(cls, session, daily_task_id: int, task_content: str, **kwargs) -> Optional['TaskItem']:
        """创建子任务记录"""
        try:
            item_data = {
                'daily_task_id': daily_task_id,
                'task_content': task_content,
                **kwargs
            }
            item = cls(**item_data)
            session.add(item)
            session.flush()
            return item
        except SQLAlchemyError as e:
            logger.error(f"创建子任务失败: {e}")
            session.rollback()
            return None

    @classmethod
    def get_by_task_id(cls, session, daily_task_id: int) -> List['TaskItem']:
        """获取指定任务的所有子任务"""
        try:
            return session.query(cls).filter(
                cls.daily_task_id == daily_task_id,
                cls.is_active == True
            ).order_by(cls.created_at).all()
        except SQLAlchemyError as e:
            logger.error(f"获取子任务列表失败: {e}")
            return []

    def update_item(self, session, **kwargs) -> bool:
        """更新子任务信息"""
        try:
            for key, value in kwargs.items():
                if hasattr(self, key):
                    setattr(self, key, value)
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"更新子任务失败: {e}")
            session.rollback()
            return False

    def complete_item(self, session) -> bool:
        """完成子任务"""
        try:
            self.is_completed = True
            self.completion_time = datetime.now()
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"完成子任务失败: {e}")
            session.rollback()
            return False

    def soft_delete(self, session) -> bool:
        """软删除子任务"""
        try:
            self.is_active = False
            session.flush()
            return True
        except SQLAlchemyError as e:
            logger.error(f"删除子任务失败: {e}")
            session.rollback()
            return False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'daily_task_id': self.daily_task_id,
            'task_content': self.task_content,
            'task_source': self.task_source,
            'time_slot': self.time_slot,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'order_index': self.order_index,
            'is_completed': self.is_completed,
            'completion_time': self.completion_time.isoformat() if self.completion_time else None,
            'estimated_minutes': self.estimated_minutes,
            'actual_minutes': self.actual_minutes,
            'difficulty_rating': self.difficulty_rating,
            'quality_rating': self.quality_rating,
            'notes': self.notes,
            'points_earned': self.points_earned,
            'bonus_points': self.bonus_points,
            'points_reason': self.points_reason,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
