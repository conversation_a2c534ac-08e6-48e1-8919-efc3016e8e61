# Prompt生成相关的数据模型
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class ChildProfileSummary(BaseModel):
    """儿童个人档案摘要"""
    id: int = Field(..., description="儿童ID")
    name: str = Field(..., description="姓名")
    nickname: Optional[str] = Field(None, description="昵称")
    age: Optional[int] = Field(None, description="年龄")
    academic_level: Optional[str] = Field(None, description="学业等级")
    school_name: Optional[str] = Field(None, description="学校名称")
    
    # 学习特征
    learning_style: Optional[str] = Field(None, description="学习风格")
    attention_span_minutes: Optional[int] = Field(None, description="注意力持续时间(分钟)")
    personality_traits: Optional[str] = Field(None, description="性格特点")
    
    # 学科偏好
    favorite_subjects: Optional[str] = Field(None, description="喜欢的学科")
    disliked_subjects: Optional[str] = Field(None, description="不喜欢的学科")
    good_at_subjects: Optional[str] = Field(None, description="擅长的科目")
    weak_at_subjects: Optional[str] = Field(None, description="不擅长的科目")

class SubTaskDetail(BaseModel):
    """子任务详情"""
    task: str = Field(..., description="任务内容")
    source: Optional[str] = Field(None, description="任务来源")


class TodayHomeworkSummary(BaseModel):
    """今日作业/学习计划摘要"""
    plan_id: Optional[str] = Field(None, description="计划ID")
    subject: str = Field(..., description="学科")
    task_name: Optional[str] = Field(None, description="任务名称")
    time_slot: Optional[str] = Field(None, description="时间段")
    sub_tasks: Optional[List[str]] = Field(None, description="子任务列表")
    difficulty: Optional[str] = Field(None, description="难点")
    status: Optional[str] = Field(None, description="状态")
    confidence_index: Optional[int] = Field(None, description="执行信心指数")


class RecentHomeworkCompletion(BaseModel):
    """近期作业完成情况"""
    date: str = Field(..., description="日期")
    subject: str = Field(..., description="学科")
    activity_type: Optional[str] = Field(None, description="活动类型")
    
    # 完成情况
    completion_rate: Optional[float] = Field(None, description="完成率(%)")
    accuracy_rate: Optional[float] = Field(None, description="正确率(%)")
    homework_completion_rate: Optional[float] = Field(None, description="作业完成率(%)")
    
    # 时间管理
    total_duration_minutes: Optional[float] = Field(None, description="总耗时(分钟)")
    subject_duration_minutes: Optional[float] = Field(None, description="单科耗时(分钟)")
    is_behind_schedule: Optional[bool] = Field(None, description="是否延迟")
    
    # 专注度
    concentration_level: Optional[int] = Field(None, description="专注程度(1-5)")
    internal_interruptions: Optional[int] = Field(None, description="内部中断次数")
    desk_leaving_times: Optional[int] = Field(None, description="离开课桌次数")
    
    # 情感态度
    enjoyment_rating: Optional[int] = Field(None, description="享受程度(1-5)")
    difficulty_rating: Optional[int] = Field(None, description="难度评价(1-5)")
    motivation_level: Optional[int] = Field(None, description="动机水平(1-5)")


class PromptGenerationRequest(BaseModel):
    """生成prompt的请求模型"""
    child_id: int = Field(..., description="儿童ID")
    days_back: int = Field(default=7, ge=1, le=30, description="回溯天数(1-30天)")
    include_today_homework: bool = Field(default=True, description="是否包含今日作业")
    include_recent_completion: bool = Field(default=True, description="是否包含近期完成情况")
    subject_filter: Optional[str] = Field(None, description="学科筛选")
    prompt_template: Optional[str] = Field(None, description="自定义prompt模板")


class GeneratedPrompt(BaseModel):
    """生成的prompt响应"""
    child_id: int = Field(..., description="儿童ID")
    generated_at: datetime = Field(..., description="生成时间")
    
    # 原始数据
    child_profile: ChildProfileSummary = Field(..., description="儿童档案摘要")
    today_homework: List[TodayHomeworkSummary] = Field(..., description="今日作业列表")
    recent_completion: List[RecentHomeworkCompletion] = Field(..., description="近期完成情况")
    
    # 生成的prompt
    structured_prompt: str = Field(..., description="结构化prompt文本")
    prompt_sections: Dict[str, Any] = Field(..., description="prompt各部分内容")
    
    # 统计信息
    data_summary: Dict[str, Any] = Field(..., description="数据统计摘要")

class TaskPromptRequest(BaseModel):
    """任务计划表prompt生成请求"""
    child_id: int = Field(..., description="儿童ID")
    days_back: int = Field(default=7, ge=1, le=30, description="回溯天数(1-30天)")
    include_yesterday_tasks: bool = Field(default=True, description="是否包含昨日任务情况")
    template_type: str = Field(default="task_prompt", description="模板类型")
    subject_filter: Optional[str] = Field(None, description="学科筛选")   

class TaskPromptResponse(BaseModel):
    """任务计划表prompt响应"""
    child_id: int = Field(..., description="儿童ID")
    generated_at: datetime = Field(..., description="生成时间")
    template_type: str = Field(..., description="使用的模板类型")

    # 四个prompt部分
    prompt1: str = Field(..., description="孩子个人肖像")
    prompt2: str = Field(..., description="历史学习情况")
    prompt3: str = Field(..., description="当日任务要求")
    prompt4: str = Field(..., description="昨日任务情况")

    # 最终生成的完整prompt
    final_prompt: str = Field(..., description="最终格式化的prompt")

    # 原始数据
    child_profile: ChildProfileSummary = Field(..., description="儿童档案摘要")
    today_homework: List[TodayHomeworkSummary] = Field(..., description="今日作业列表")
    recent_completion: List[RecentHomeworkCompletion] = Field(..., description="近期完成情况")
    yesterday_tasks: Optional[List[Dict[str, Any]]] = Field(None, description="昨日任务情况")


class TemplateInfo(BaseModel):
    """模板信息"""
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    variables: Dict[str, str] = Field(..., description="模板变量说明")

    
class PromptTemplate(BaseModel):
    """Prompt模板"""
    template_name: str = Field(..., description="模板名称")
    template_content: str = Field(..., description="模板内容")
    variables: List[str] = Field(..., description="模板变量列表")
    description: Optional[str] = Field(None, description="模板描述")





class PlanModificationRequest(BaseModel):
    """计划表修改请求"""
    child_id: int = Field(..., description="儿童ID")
    original_plan: List[Dict[str, Any]] = Field(..., description="原有任务计划表JSON数据")
    modification_request: str = Field(..., description="用户的修改意见和要求")
    modification_type: str = Field(
        default="other",
        description="修改类型：add_task(添加任务)、remove_task(删除任务)、modify_task(修改任务)、adjust_time(调整时间)、other(其他修改)"
    )
    plan_date: Optional[datetime] = Field(None, description="计划日期")


class PlanModificationResponse(BaseModel):
    """计划表修改响应"""
    child_id: int = Field(..., description="儿童ID")
    generated_at: datetime = Field(..., description="生成时间")
    modification_type: str = Field(..., description="修改类型")

    # 修改相关信息
    original_plan: List[Dict[str, Any]] = Field(..., description="原有计划表")
    modification_request: str = Field(..., description="修改要求")
    modified_plan: List[Dict[str, Any]] = Field(..., description="修改后的计划表")

    # 生成的prompt
    modification_prompt: str = Field(..., description="用于修改的完整prompt")

    # AI响应
    ai_response: Optional[str] = Field(None, description="AI原始响应")
    success: bool = Field(..., description="修改是否成功")
    message: str = Field(..., description="处理结果消息")
    error_details: Optional[str] = Field(None, description="错误详情")
