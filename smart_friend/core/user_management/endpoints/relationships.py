# 家长-小孩关系管理API端点
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
import logging

from ..service import user_management_service, UserManagementService
from ..schemas import (
    ChildParentRelationshipCreate, 
    ChildParentRelationshipResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


def get_user_management_service() -> UserManagementService:
    """获取用户管理服务依赖"""
    return user_management_service


@router.post("/", response_model=ChildParentRelationshipResponse, status_code=status.HTTP_201_CREATED)
async def create_relationship(
    relationship_data: ChildParentRelationshipCreate,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    创建家长-小孩关系

    - **child_id**: 小孩ID（必填）
    - **parent_id**: 家长ID（必填）
    - **relationship_type**: 关系类型（必填）：father, mother, grandfather, grandmother, other
    - **is_primary_contact**: 是否为主要联系人（可选，默认False）
    - **notes**: 关系备注（可选）
    """
    try:
        relationship = service.create_relationship(relationship_data)
        if not relationship:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="创建关系失败，请检查小孩和家长是否存在，或关系是否已存在"
            )

        return relationship

    except HTTPException:
        raise
    except Exception as e:
        error_msg = str(e)
        logger.error(f"创建家长-小孩关系时发生错误: {e}")

        # 检查是否是外键约束错误或实体不存在错误
        if ("FOREIGN KEY constraint failed" in error_msg or
            "Child not found" in error_msg or
            "Parent not found" in error_msg):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="家长或小孩不存在，请检查ID是否正确"
            )

        # 检查是否是唯一约束错误（重复关系）
        if ("UNIQUE constraint failed" in error_msg or
            "Relationship already exists" in error_msg):
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="该家长和小孩的关系已存在"
            )

        # 其他错误
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建关系失败，请稍后重试"
        )


@router.delete("/{relationship_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_relationship(
    relationship_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    软删除家长-小孩关系（将is_active设为False）
    
    - **relationship_id**: 关系ID
    """
    try:
        success = service.delete_relationship(relationship_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到ID为{relationship_id}的关系或删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除关系时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除关系失败: {str(e)}"
        )


@router.get("/child/{child_id}/parents", response_model=List[dict])
async def get_child_parents(
    child_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取指定小孩的所有家长
    
    - **child_id**: 小孩ID
    """
    try:
        parents = service.get_child_parents(child_id)
        return parents
        
    except Exception as e:
        logger.error(f"获取小孩家长时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取小孩家长失败: {str(e)}"
        )


@router.get("/parent/{parent_id}/children", response_model=List[dict])
async def get_parent_children(
    parent_id: int,
    service: UserManagementService = Depends(get_user_management_service)
):
    """
    获取指定家长的所有小孩
    
    - **parent_id**: 家长ID
    """
    try:
        children = service.get_parent_children(parent_id)
        return children
        
    except Exception as e:
        logger.error(f"获取家长小孩时发生错误: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取家长小孩失败: {str(e)}"
        )
