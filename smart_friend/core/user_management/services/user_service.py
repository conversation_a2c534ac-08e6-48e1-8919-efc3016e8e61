# 小孩用户信息服务类 - 提供CRUD操作
import logging
from typing import Optional, List
from sqlalchemy.orm import joinedload
from sqlalchemy.exc import SQLAlchemyError

from ..database.connection import get_sqlite_manager, get_db_session_context
from ..models.user_models import (
    Parent, Child, ChildParentRelationship,
    ChildAcademicRecord
)
from ..schemas import (
    ParentCreate, ParentUpdate,
    ChildCreate, ChildUpdate,
    ChildParentRelationshipCreate,
    ChildAcademicRecordCreate, ChildAcademicRecordUpdate
)

logger = logging.getLogger(__name__)


class ChildService:
    """小孩用户信息服务类"""
    
    def __init__(self):
        self.sqlite_manager = get_sqlite_manager()
    
    # ==================== 家长相关操作 ====================
    
    def create_parent(self, parent_data: ParentCreate) -> Optional[Parent]:
        """创建家长"""
        try:
            with get_db_session_context() as session:
                parent = Parent.create(session, **parent_data.model_dump(exclude_unset=True))
                if parent:
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    parent = Parent.get_by_id(session, parent.id)
                    session.expunge(parent)
                    logger.info(f"成功创建家长: {parent.name} (ID: {parent.id})")
                return parent
        except SQLAlchemyError as e:
            logger.error(f"创建家长失败: {e}")
            # 对于唯一约束错误，重新抛出异常让上层处理
            if "UNIQUE constraint failed" in str(e):
                raise e
            # 对于其他错误也抛出，让上层决定如何处理
            raise e
    
    def get_parent(self, parent_id: int) -> Optional[Parent]:
        """获取家长信息"""
        try:
            with get_db_session_context() as session:
                parent = Parent.get_by_id(session, parent_id)
                if parent:
                    session.expunge(parent)
                return parent
        except SQLAlchemyError as e:
            logger.error(f"获取家长信息失败: {e}")
            return None
    
    def get_parents(self, skip: int = 0, limit: int = 100, is_active: bool = True) -> List[Parent]:
        """获取家长列表"""
        try:
            with get_db_session_context() as session:
                parents = Parent.get_all(session, skip, limit, is_active)
                # 分离对象以便在会话外使用
                for parent in parents:
                    session.expunge(parent)
                return parents
        except SQLAlchemyError as e:
            logger.error(f"获取家长列表失败: {e}")
            return []
    
    def update_parent(self, parent_id: int, parent_data: ParentUpdate) -> Optional[Parent]:
        """更新家长信息"""
        try:
            with get_db_session_context() as session:
                parent = Parent.get_by_id(session, parent_id)
                if not parent:
                    logger.warning(f"家长不存在: {parent_id}")
                    return None

                update_data = parent_data.model_dump(exclude_unset=True)
                if parent.update(session, **update_data):
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    parent = Parent.get_by_id(session, parent.id)
                    session.expunge(parent)
                    logger.info(f"成功更新家长: {parent.name} (ID: {parent.id})")
                    return parent
                return None
        except SQLAlchemyError as e:
            logger.error(f"更新家长信息失败: {e}")
            return None
    
    def delete_parent(self, parent_id: int) -> bool:
        """软删除家长"""
        try:
            with get_db_session_context() as session:
                parent = Parent.get_by_id(session, parent_id)
                if not parent:
                    logger.warning(f"家长不存在: {parent_id}")
                    return False

                if parent.soft_delete(session):
                    session.commit()
                    logger.info(f"成功软删除家长: {parent.name} (ID: {parent.id})")
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"软删除家长失败: {e}")
            return False
    
    # ==================== 小孩相关操作 ====================
    
    def create_child(self, child_data: ChildCreate) -> Optional[Child]:
        """创建小孩"""
        try:
            with get_db_session_context() as session:
                child = Child.create(session, **child_data.model_dump(exclude_unset=True))
                if child:
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    child = Child.get_by_id(session, child.id)
                    session.expunge(child)
                    logger.info(f"成功创建小孩: {child.name} (ID: {child.id})")
                return child
        except SQLAlchemyError as e:
            logger.error(f"创建小孩失败: {e}")
            return None
    
    def get_child(self, child_id: int, include_relations: bool = False) -> Optional[Child]:
        """获取小孩信息"""
        try:
            with get_db_session_context() as session:
                if include_relations:
                    # 对于复杂查询，保留原有逻辑
                    query = session.query(Child).options(
                        joinedload(Child.parents),
                        joinedload(Child.academic_records)
                    )
                    child = query.filter(Child.id == child_id).first()
                else:
                    # 使用模型方法进行简单查询
                    child = Child.get_by_id(session, child_id)

                if child:
                    session.expunge(child)
                return child
        except SQLAlchemyError as e:
            logger.error(f"获取小孩信息失败: {e}")
            return None
    
    def get_children(self, skip: int = 0, limit: int = 100, is_active: bool = True) -> List[Child]:
        """获取小孩列表"""
        try:
            with get_db_session_context() as session:
                children = Child.get_all(session, skip, limit, is_active)
                # 分离对象以便在会话外使用
                for child in children:
                    session.expunge(child)
                return children
        except SQLAlchemyError as e:
            logger.error(f"获取小孩列表失败: {e}")
            return []
    
    def update_child(self, child_id: int, child_data: ChildUpdate) -> Optional[Child]:
        """更新小孩信息"""
        try:
            with get_db_session_context() as session:
                child = Child.get_by_id(session, child_id)
                if not child:
                    logger.warning(f"小孩不存在: {child_id}")
                    return None

                update_data = child_data.model_dump(exclude_unset=True)
                if child.update(session, **update_data):
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    child = Child.get_by_id(session, child.id)
                    session.expunge(child)
                    logger.info(f"成功更新小孩: {child.name} (ID: {child.id})")
                    return child
                return None
        except SQLAlchemyError as e:
            logger.error(f"更新小孩信息失败: {e}")
            return None
    
    def delete_child(self, child_id: int) -> bool:
        """软删除小孩"""
        try:
            with get_db_session_context() as session:
                child = Child.get_by_id(session, child_id)
                if not child:
                    logger.warning(f"小孩不存在: {child_id}")
                    return False

                if child.soft_delete(session):
                    session.commit()
                    logger.info(f"成功软删除小孩: {child.name} (ID: {child.id})")
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"软删除小孩失败: {e}")
            return False

    # ==================== 关系相关操作 ====================

    def create_child_parent_relationship(self, relationship_data: ChildParentRelationshipCreate) -> Optional[ChildParentRelationship]:
        """创建小孩-家长关系"""
        try:
            with get_db_session_context() as session:
                # 检查小孩和家长是否存在
                child = Child.get_by_id(session, relationship_data.child_id)
                parent = Parent.get_by_id(session, relationship_data.parent_id)

                if not child:
                    logger.warning(f"小孩不存在: {relationship_data.child_id}")
                    # 抛出自定义异常，让上层处理
                    from sqlalchemy.exc import IntegrityError
                    raise IntegrityError("Child not found", None, None)
                if not parent:
                    logger.warning(f"家长不存在: {relationship_data.parent_id}")
                    # 抛出自定义异常，让上层处理
                    from sqlalchemy.exc import IntegrityError
                    raise IntegrityError("Parent not found", None, None)

                # 检查关系是否已存在
                existing_relationships = ChildParentRelationship.get_by_child_id(session, relationship_data.child_id)
                for rel in existing_relationships:
                    if rel.parent_id == relationship_data.parent_id:
                        logger.warning(f"关系已存在: 小孩{relationship_data.child_id} - 家长{relationship_data.parent_id}")
                        # 抛出自定义异常，让上层处理
                        from sqlalchemy.exc import IntegrityError
                        raise IntegrityError("Relationship already exists", None, None)

                relationship = ChildParentRelationship.create(session, **relationship_data.model_dump(exclude_unset=True))
                if relationship:
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    relationship = ChildParentRelationship.get_by_id(session, relationship.id)
                    session.expunge(relationship)
                    logger.info(f"成功创建关系: 小孩{relationship.child_id} - 家长{relationship.parent_id} ({relationship.relationship_type})")
                return relationship
        except SQLAlchemyError as e:
            logger.error(f"创建关系失败: {e}")
            # 重新抛出异常让上层处理
            raise e

    def delete_child_parent_relationship(self, relationship_id: int) -> bool:
        """软删除小孩-家长关系"""
        try:
            with get_db_session_context() as session:
                relationship = ChildParentRelationship.get_by_id(session, relationship_id)
                if not relationship:
                    logger.warning(f"关系不存在: {relationship_id}")
                    return False

                if relationship.soft_delete(session):
                    session.commit()
                    logger.info(f"成功软删除关系: ID {relationship_id}")
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"软删除关系失败: {e}")
            return False

    def get_child_parents(self, child_id: int) -> List[Parent]:
        """获取小孩的所有家长"""
        try:
            with get_db_session_context() as session:
                relationships = ChildParentRelationship.get_by_child_id(session, child_id)
                parents = []
                for rel in relationships:
                    parent = Parent.get_by_id(session, rel.parent_id)
                    if parent:
                        session.expunge(parent)
                        parents.append(parent)
                return parents
        except SQLAlchemyError as e:
            logger.error(f"获取小孩家长失败: {e}")
            return []

    def get_parent_children(self, parent_id: int) -> List[Child]:
        """获取家长的所有小孩"""
        try:
            with get_db_session_context() as session:
                relationships = ChildParentRelationship.get_by_parent_id(session, parent_id)
                children = []
                for rel in relationships:
                    child = Child.get_by_id(session, rel.child_id)
                    if child:
                        session.expunge(child)
                        children.append(child)
                return children
        except SQLAlchemyError as e:
            logger.error(f"获取家长小孩失败: {e}")
            return []

    # ==================== 学业记录相关操作 ====================

    def create_academic_record(self, record_data: ChildAcademicRecordCreate) -> Optional[ChildAcademicRecord]:
        """创建学业记录"""
        try:
            with get_db_session_context() as session:
                # 检查小孩是否存在
                child = Child.get_by_id(session, record_data.child_id)
                if not child:
                    logger.warning(f"小孩不存在: {record_data.child_id}")
                    return None

                record = ChildAcademicRecord.create(session, **record_data.model_dump(exclude_unset=True))
                if record:
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    record = ChildAcademicRecord.get_by_id(session, record.id)
                    session.expunge(record)
                    logger.info(f"成功创建学业记录: 小孩{record.child_id} - {record.subject} (ID: {record.id})")
                return record
        except SQLAlchemyError as e:
            logger.error(f"创建学业记录失败: {e}")
            return None

    def get_academic_record(self, record_id: int) -> Optional[ChildAcademicRecord]:
        """获取学业记录"""
        try:
            with get_db_session_context() as session:
                record = ChildAcademicRecord.get_by_id(session, record_id)
                if record:
                    session.expunge(record)
                return record
        except SQLAlchemyError as e:
            logger.error(f"获取学业记录失败: {e}")
            return None

    def get_child_academic_records(self, child_id: int, subject: Optional[str] = None,
                                 academic_year: Optional[str] = None, limit: int = 100) -> List[ChildAcademicRecord]:
        """获取小孩的学业记录"""
        try:
            with get_db_session_context() as session:
                records = ChildAcademicRecord.get_by_child_id(session, child_id, subject, academic_year, limit)
                # 分离对象以便在会话外使用
                for record in records:
                    session.expunge(record)
                return records
        except SQLAlchemyError as e:
            logger.error(f"获取小孩学业记录失败: {e}")
            return []

    def update_academic_record(self, record_id: int, record_data: ChildAcademicRecordUpdate) -> Optional[ChildAcademicRecord]:
        """更新学业记录"""
        try:
            with get_db_session_context() as session:
                record = ChildAcademicRecord.get_by_id(session, record_id)
                if not record:
                    logger.warning(f"学业记录不存在: {record_id}")
                    return None

                update_data = record_data.model_dump(exclude_unset=True)
                if record.update(session, **update_data):
                    session.commit()
                    # 重新查询以获取完整的对象状态
                    record = ChildAcademicRecord.get_by_id(session, record.id)
                    session.expunge(record)
                    logger.info(f"成功更新学业记录: ID {record.id}")
                    return record
                return None
        except SQLAlchemyError as e:
            logger.error(f"更新学业记录失败: {e}")
            return None

    def delete_academic_record(self, record_id: int) -> bool:
        """软删除学业记录"""
        try:
            with get_db_session_context() as session:
                record = ChildAcademicRecord.get_by_id(session, record_id)
                if not record:
                    logger.warning(f"学业记录不存在: {record_id}")
                    return False

                if record.soft_delete(session):
                    session.commit()
                    logger.info(f"成功软删除学业记录: ID {record_id}")
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"软删除学业记录失败: {e}")
            return False
