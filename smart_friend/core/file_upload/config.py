# 文件上传配置
import os
from typing import Dict, List, Set
from pathlib import Path

class FileUploadConfig:
    """文件上传配置类"""
    
    # 允许的文件扩展名白名单
    ALLOWED_EXTENSIONS: Set[str] = {
        # 图片文件
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg',
        # 文档文件
        '.pdf', '.doc', '.docx', '.txt', '.md', '.rtf',
        # 表格文件
        '.xls', '.xlsx', '.csv',
        # 压缩文件
        '.zip', '.rar', '.7z',
        # 音频文件
        '.mp3', '.wav', '.m4a', '.aac',
        # 视频文件
        '.mp4', '.avi', '.mov', '.wmv', '.flv',
        # 其他
        '.json', '.xml'
    }
    
    # 允许的MIME类型白名单
    ALLOWED_MIME_TYPES: Set[str] = {
        # 图片
        'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 
        'image/webp', 'image/svg+xml',
        # 文档
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain', 'text/markdown', 'application/rtf',
        # 表格
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/csv',
        # 压缩
        'application/zip', 'application/x-rar-compressed',
        'application/x-7z-compressed',
        # 音频
        'audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/aac',
        # 视频
        'video/mp4', 'video/x-msvideo', 'video/quicktime',
        'video/x-ms-wmv', 'video/x-flv',
        # 其他
        'application/json', 'application/xml', 'text/xml'
    }
    
    # 文件大小限制 (字节)
    MAX_FILE_SIZE: Dict[str, int] = {
        'image': 10 * 1024 * 1024,      # 10MB
        'document': 50 * 1024 * 1024,   # 50MB
        'video': 500 * 1024 * 1024,     # 500MB
        'audio': 100 * 1024 * 1024,     # 100MB
        'archive': 100 * 1024 * 1024,   # 100MB
        'default': 10 * 1024 * 1024     # 10MB
    }
    
    # 允许的存储路径白名单
    ALLOWED_UPLOAD_PATHS: List[str] = [
        'uploads/images',
        'uploads/documents', 
        'uploads/videos',
        'uploads/audio',
        'uploads/archives',
        'uploads/temp',
        'uploads/user_files',
        'uploads/learning_materials'
    ]
    
    # 文件类型分类
    FILE_TYPE_CATEGORIES: Dict[str, List[str]] = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
        'document': ['.pdf', '.doc', '.docx', '.txt', '.md', '.rtf'],
        'spreadsheet': ['.xls', '.xlsx', '.csv'],
        'archive': ['.zip', '.rar', '.7z'],
        'audio': ['.mp3', '.wav', '.m4a', '.aac'],
        'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv'],
        'data': ['.json', '.xml']
    }
    
    # 危险文件扩展名黑名单
    DANGEROUS_EXTENSIONS: Set[str] = {
        '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js',
        '.jar', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl',
        '.sh', '.bash', '.ps1', '.msi', '.deb', '.rpm', '.dmg'
    }
    
    # 基础上传目录
    BASE_UPLOAD_DIR: str = os.getenv('UPLOAD_DIR', 'uploads')
    
    # 是否启用文件内容检查
    ENABLE_CONTENT_VALIDATION: bool = True
    
    # 是否启用病毒扫描（需要额外配置）
    ENABLE_VIRUS_SCAN: bool = False
    
    @classmethod
    def get_file_category(cls, extension: str) -> str:
        """根据文件扩展名获取文件类别"""
        extension = extension.lower()
        for category, extensions in cls.FILE_TYPE_CATEGORIES.items():
            if extension in extensions:
                return category
        return 'unknown'
    
    @classmethod
    def get_max_size_for_extension(cls, extension: str) -> int:
        """根据文件扩展名获取最大文件大小限制"""
        category = cls.get_file_category(extension)
        return cls.MAX_FILE_SIZE.get(category, cls.MAX_FILE_SIZE['default'])
    
    @classmethod
    def is_path_allowed(cls, path: str) -> bool:
        """检查路径是否在白名单中"""
        # 规范化路径
        normalized_path = os.path.normpath(path).replace('\\', '/')
        
        # 检查是否在允许的路径列表中
        for allowed_path in cls.ALLOWED_UPLOAD_PATHS:
            if normalized_path.startswith(allowed_path):
                return True
        return False
    
    @classmethod
    def get_safe_filename(cls, filename: str) -> str:
        """生成安全的文件名"""
        import re
        import uuid
        
        # 移除危险字符
        safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # 限制文件名长度
        name, ext = os.path.splitext(safe_filename)
        if len(name) > 100:
            name = name[:100]
        
        # 如果文件名为空或只有扩展名，生成随机名称
        if not name or name == '_':
            name = str(uuid.uuid4())[:8]
        
        return f"{name}{ext}"
