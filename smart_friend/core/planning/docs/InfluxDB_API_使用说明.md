# InfluxDB每日学习数据API使用说明

## 概述

本API提供了基于InfluxDB的每日学习数据管理功能，支持学习记录的增删查操作和统计分析。

## 配置信息

### InfluxDB配置
```python
INFLUXDB_ENABLED = True
INFLUXDB_URL = "http://**************:8086"
INFLUXDB_TOKEN = "gIS-8--WRvRSsnLllLqstWtGMxxiEEg6KbPGv6UeV07jgjoR65Dpp9Z5mGaRafOcNMjawbsQw-bZi3Rru1xvlg=="
INFLUXDB_ORG = "1"
INFLUXDB_BUCKET = "daily_learning"
```

## API端点

### 基础URL
```
http://localhost:8000/api/v1/daily-learning
```

## 1. 健康检查

**端点**: `GET /health`

**描述**: 检查InfluxDB连接状态

**响应示例**:
```json
{
  "influxdb_status": "healthy",
  "connected": true,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 2. 创建学习记录

**端点**: `POST /learning-records`

**描述**: 添加新的每日学习记录

**请求体**:
```json
{
  "child_id": 1,
  "subject": "数学",
  "activity_type": "exercise",
  "difficulty_level": 3,
  "study_duration_minutes": 45.0,
  "completion_rate": 85.5,
  "accuracy_rate": 92.0,
  "score": 88.0,
  "max_score": 100.0,
  "enjoyment_rating": 4,
  "difficulty_rating": 3,
  "concentration_level": 4,
  "questions_asked": 2,
  "help_requests": 1,
  "breaks_taken": 1,
  "notes": "今天的数学练习做得不错",
  "feedback": "继续保持，可以尝试更难的题目"
}
```

**字段说明**:
- `child_id` (必填): 孩子ID
- `subject` (必填): 学科名称
- `activity_type` (可选): 活动类型 (lesson, exercise, test, project, game)
- `difficulty_level` (可选): 难度等级 1-5
- `study_duration_minutes` (可选): 学习时长（分钟）
- `completion_rate` (可选): 完成率 0-100
- `accuracy_rate` (可选): 正确率 0-100
- `score` (可选): 得分
- `max_score` (可选): 满分
- `enjoyment_rating` (可选): 享受程度 1-5
- `difficulty_rating` (可选): 难度感受 1-5
- `concentration_level` (可选): 专注程度 1-5
- `questions_asked` (可选): 提问次数
- `help_requests` (可选): 求助次数
- `breaks_taken` (可选): 休息次数
- `notes` (可选): 学习笔记
- `feedback` (可选): 反馈信息
- `timestamp` (可选): 记录时间戳

**响应示例**:
```json
{
  "success": true,
  "message": "成功添加孩子1的数学学习记录",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 3. 获取学习记录

**端点**: `GET /learning-records/{child_id}`

**描述**: 获取指定孩子的学习记录

**路径参数**:
- `child_id`: 孩子ID

**查询参数**:
- `subject` (可选): 学科筛选
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期
- `limit` (可选): 返回记录数限制 (1-1000，默认100)

**示例请求**:
```
GET /learning-records/1?subject=数学&limit=50
```

**响应示例**:
```json
[
  {
    "timestamp": "2024-01-15T10:30:00Z",
    "child_id": "1",
    "subject": "数学",
    "date": "2024-01-15",
    "activity_type": "exercise",
    "difficulty_level": "3",
    "study_duration_minutes": 45.0,
    "completion_rate": 85.5,
    "accuracy_rate": 92.0,
    "score": 88.0,
    "max_score": 100.0,
    "enjoyment_rating": 4,
    "difficulty_rating": 3,
    "concentration_level": 4,
    "questions_asked": 2,
    "help_requests": 1,
    "breaks_taken": 1,
    "notes": "今天的数学练习做得不错",
    "feedback": "继续保持，可以尝试更难的题目"
  }
]
```

## 4. 删除学习记录

**端点**: `DELETE /learning-records`

**描述**: 删除指定时间范围的学习记录

**请求体**:
```json
{
  "child_id": 1,
  "start_date": "2024-01-10T00:00:00Z",
  "end_date": "2024-01-15T23:59:59Z",
  "subject": "数学"
}
```

**字段说明**:
- `child_id` (必填): 孩子ID
- `start_date` (必填): 开始日期
- `end_date` (必填): 结束日期
- `subject` (可选): 学科筛选

**响应示例**:
```json
{
  "success": true,
  "message": "成功删除孩子1在指定时间范围内的学习记录",
  "deleted_period": {
    "start_date": "2024-01-10T00:00:00Z",
    "end_date": "2024-01-15T23:59:59Z",
    "subject": "数学"
  }
}
```

## 5. 获取学习统计

**端点**: `GET /learning-statistics/{child_id}`

**描述**: 获取指定孩子的学习统计信息

**路径参数**:
- `child_id`: 孩子ID

**查询参数**:
- `days` (可选): 统计天数 (1-365，默认7)
- `subject` (可选): 学科筛选

**示例请求**:
```
GET /learning-statistics/1?days=30&subject=数学
```

**响应示例**:
```json
{
  "total_records": 15,
  "total_study_time": 675.0,
  "average_completion_rate": 87.5,
  "average_accuracy_rate": 91.2,
  "average_enjoyment": 4.1,
  "subjects_studied": ["数学", "语文", "英语"],
  "period_days": 30
}
```

## 使用示例

### Python示例

```python
import requests
import json
from datetime import datetime, timezone

# 基础URL
BASE_URL = "http://localhost:8000/api/v1/daily-learning"

# 1. 创建学习记录
learning_data = {
    "child_id": 1,
    "subject": "数学",
    "activity_type": "exercise",
    "study_duration_minutes": 45.0,
    "completion_rate": 85.5,
    "accuracy_rate": 92.0,
    "enjoyment_rating": 4,
    "notes": "今天学习了分数运算"
}

response = requests.post(
    f"{BASE_URL}/learning-records",
    json=learning_data
)
print(response.json())

# 2. 获取学习记录
response = requests.get(f"{BASE_URL}/learning-records/1?limit=10")
records = response.json()
print(f"获取到 {len(records)} 条记录")

# 3. 获取统计信息
response = requests.get(f"{BASE_URL}/learning-statistics/1?days=7")
stats = response.json()
print(f"7天总学习时间: {stats['total_study_time']} 分钟")
```

### curl示例

```bash
# 创建学习记录
curl -X POST "http://localhost:8000/api/v1/daily-learning/learning-records" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "subject": "数学",
    "study_duration_minutes": 45.0,
    "completion_rate": 85.5,
    "enjoyment_rating": 4
  }'

# 获取学习记录
curl "http://localhost:8000/api/v1/daily-learning/learning-records/1?limit=10"

# 获取统计信息
curl "http://localhost:8000/api/v1/daily-learning/learning-statistics/1?days=7"
```

## 错误处理

API使用标准HTTP状态码：

- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源未找到
- `500`: 服务器内部错误
- `503`: InfluxDB服务不可用

错误响应格式：
```json
{
  "detail": "错误描述信息"
}
```

## 注意事项

1. 确保InfluxDB服务正常运行
2. 检查网络连接和防火墙设置
3. 时间戳使用ISO 8601格式，建议使用UTC时区
4. 数值字段支持小数，评分字段为整数
5. 删除操作不可逆，请谨慎使用
6. 建议定期备份重要数据
