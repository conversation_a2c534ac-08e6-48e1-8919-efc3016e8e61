# 学习计划InfluxDB操作接口
from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Optional
from datetime import datetime, timezone, timedelta
import logging

from core.planning.schemas import (
    StudyPlanCreate, 
    StudyPlanUpdate,
    StudyPlanResponse, 
    PlanStatistics,
    DeletePlanRequest
)
from service.planning_service import PlanningService

logger = logging.getLogger(__name__)

router = APIRouter()


def get_planning_service() -> PlanningService:
    """获取计划服务实例"""
    return PlanningService()


@router.post("/plans", response_model=dict)
async def create_plan(
    plan_data: StudyPlanCreate,
    service: PlanningService = Depends(get_planning_service)
):
    """
    创建学习计划

    - **child_id**: 学生ID
    - **task_name**: 任务名称
    - **time_slot**: 时段（格式：HH:MM - HH:MM）
    - **subject**: 学科
    - **sub_tasks**: 子任务列表
    - **customization**: 定制说明（可选）
    - **difficulty**: 难点（可选）
    - **solution**: 解决方案（可选）
    - **confidence_index**: 执行信心指数1-5（可选）
    - **plan_date**: 计划日期（可选）
    - **status**: 计划状态（可选，默认pending）
    - **notes**: 备注（可选）
    """
    logger.info(f"🎯 API请求: 创建学习计划 - 学生ID: {plan_data.child_id}, 学科: {plan_data.subject}, 任务: {plan_data.task_name}")
    logger.debug(f"请求数据: {plan_data.model_dump()}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 转换为字典格式
        plan_dict = plan_data.model_dump(exclude_unset=True)
        logger.debug("开始调用服务层创建计划")

        # 创建计划
        plan_id = service.create_plan(
            child_id=plan_data.child_id,
            plan_data=plan_dict
        )

        if not plan_id:
            logger.error(f"服务层创建计划失败 - 学生ID: {plan_data.child_id}")
            raise HTTPException(
                status_code=500,
                detail="创建学习计划失败"
            )

        response = {
            "success": True,
            "message": f"成功创建学生{plan_data.child_id}的{plan_data.subject}学习计划",
            "plan_id": plan_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"✅ API响应: 创建学习计划成功 - 学生ID: {plan_data.child_id}, 计划ID: {plan_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 创建学习计划失败 - 学生ID: {plan_data.child_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"创建学习计划失败: {str(e)}"
        )


@router.get("/plans/{child_id}", response_model=List[StudyPlanResponse])
async def get_plans(
    child_id: int,
    subject: Optional[str] = Query(None, description="学科筛选"),
    status: Optional[str] = Query(None, description="状态筛选"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数限制"),
    service: PlanningService = Depends(get_planning_service)
):
    """
    获取学生的学习计划

    - **child_id**: 学生ID
    - **subject**: 学科筛选（可选）
    - **status**: 状态筛选（可选：pending, in_progress, completed, cancelled）
    - **start_date**: 开始日期（可选，默认30天前）
    - **end_date**: 结束日期（可选，默认当前时间）
    - **limit**: 返回记录数限制（1-1000）
    """
    logger.info(f"📋 API请求: 获取学习计划 - 学生ID: {child_id}, 学科: {subject}, 状态: {status}, 限制: {limit}")
    logger.debug(f"查询参数 - 开始日期: {start_date}, 结束日期: {end_date}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 获取计划
        logger.debug("开始调用服务层获取计划")
        plans = service.get_plans(
            child_id=child_id,
            subject=subject,
            status=status,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        # 转换为响应格式
        logger.debug(f"开始转换 {len(plans)} 个计划为响应格式")
        response_plans = []
        conversion_errors = 0

        for plan in plans:
            try:
                response_plan = StudyPlanResponse(**plan)
                response_plans.append(response_plan)
            except Exception as e:
                conversion_errors += 1
                logger.warning(f"转换计划数据时出错: {e}, 计划数据: {plan}")
                continue

        if conversion_errors > 0:
            logger.warning(f"数据转换过程中有 {conversion_errors} 个计划转换失败")

        logger.info(f"✅ API响应: 获取学习计划成功 - 学生ID: {child_id}, 返回计划数: {len(response_plans)}")
        return response_plans
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 获取学习计划失败 - 学生ID: {child_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取学习计划失败: {str(e)}"
        )


@router.get("/plans/detail/{plan_id}", response_model=StudyPlanResponse)
async def get_plan_by_id(
    plan_id: str,
    service: PlanningService = Depends(get_planning_service)
):
    """
    根据计划ID获取单个计划详情

    - **plan_id**: 计划ID
    """
    logger.info(f"🔍 API请求: 获取计划详情 - 计划ID: {plan_id}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 获取计划
        logger.debug("开始调用服务层获取计划详情")
        plan = service.get_plan_by_id(plan_id)

        if not plan:
            logger.warning(f"未找到指定的计划 - 计划ID: {plan_id}")
            raise HTTPException(
                status_code=404,
                detail=f"未找到计划ID为{plan_id}的计划"
            )

        response = StudyPlanResponse(**plan)
        logger.info(f"✅ API响应: 获取计划详情成功 - 计划ID: {plan_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 获取计划详情失败 - 计划ID: {plan_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取计划详情失败: {str(e)}"
        )


@router.put("/plans/{plan_id}", response_model=dict)
async def update_plan(
    plan_id: str,
    update_data: StudyPlanUpdate,
    service: PlanningService = Depends(get_planning_service)
):
    """
    更新学习计划

    - **plan_id**: 计划ID
    - 其他字段为可选更新字段
    """
    logger.info(f"✏️ API请求: 更新学习计划 - 计划ID: {plan_id}")
    logger.debug(f"更新数据: {update_data.model_dump(exclude_unset=True)}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 转换为字典格式，排除未设置的字段
        update_dict = update_data.model_dump(exclude_unset=True)

        if not update_dict:
            logger.warning(f"更新请求中没有提供要更新的字段 - 计划ID: {plan_id}")
            raise HTTPException(
                status_code=400,
                detail="没有提供要更新的字段"
            )

        # 更新计划
        logger.debug("开始调用服务层更新计划")
        success = service.update_plan(plan_id, update_dict)

        if not success:
            logger.error(f"服务层更新计划失败 - 计划ID: {plan_id}")
            raise HTTPException(
                status_code=500,
                detail="更新学习计划失败"
            )

        response = {
            "success": True,
            "message": f"成功更新计划{plan_id}",
            "plan_id": plan_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"✅ API响应: 更新学习计划成功 - 计划ID: {plan_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 更新学习计划失败 - 计划ID: {plan_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"更新学习计划失败: {str(e)}"
        )


@router.delete("/plans", response_model=dict)
async def delete_plans(
    delete_request: DeletePlanRequest,
    service: PlanningService = Depends(get_planning_service)
):
    """
    删除学习计划

    - **child_id**: 学生ID（必需）
    - **plan_id**: 计划ID（可选，指定则删除特定计划）
    - **start_date**: 开始日期（可选）
    - **end_date**: 结束日期（可选）
    - **subject**: 学科筛选（可选）
    """
    logger.info(f"🗑️ API请求: 删除学习计划 - 学生ID: {delete_request.child_id}, 计划ID: {delete_request.plan_id}, 学科: {delete_request.subject}")
    logger.debug(f"删除条件: {delete_request.model_dump()}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 删除计划
        logger.debug("开始调用服务层删除计划")
        success = service.delete_plans(
            child_id=delete_request.child_id,
            plan_id=delete_request.plan_id,
            start_date=delete_request.start_date,
            end_date=delete_request.end_date,
            subject=delete_request.subject
        )

        if not success:
            logger.error(f"服务层删除计划失败 - 学生ID: {delete_request.child_id}")
            raise HTTPException(
                status_code=500,
                detail="删除学习计划失败"
            )

        response = {
            "success": True,
            "message": f"成功删除学生{delete_request.child_id}的学习计划",
            "deleted_criteria": {
                "child_id": delete_request.child_id,
                "plan_id": delete_request.plan_id,
                "start_date": delete_request.start_date.isoformat() if delete_request.start_date else None,
                "end_date": delete_request.end_date.isoformat() if delete_request.end_date else None,
                "subject": delete_request.subject
            }
        }

        logger.info(f"✅ API响应: 删除学习计划成功 - 学生ID: {delete_request.child_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 删除学习计划失败 - 学生ID: {delete_request.child_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除学习计划失败: {str(e)}"
        )


@router.get("/plans/statistics/{child_id}", response_model=PlanStatistics)
async def get_plan_statistics(
    child_id: int,
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    subject: Optional[str] = Query(None, description="学科筛选"),
    service: PlanningService = Depends(get_planning_service)
):
    """
    获取学生的计划统计信息

    - **child_id**: 学生ID
    - **days**: 统计天数（1-365天）
    - **subject**: 学科筛选（可选）
    """
    logger.info(f"📊 API请求: 获取计划统计信息 - 学生ID: {child_id}, 统计天数: {days}, 学科: {subject}")

    try:
        # 检查InfluxDB连接
        logger.debug("检查 InfluxDB 连接状态")
        if not service.influxdb.check_connection():
            logger.error("InfluxDB 服务不可用")
            raise HTTPException(
                status_code=503,
                detail="InfluxDB服务不可用，请检查连接配置"
            )

        # 获取统计信息
        logger.debug("开始调用服务层获取统计信息")
        statistics = service.get_plan_statistics(
            child_id=child_id,
            days=days,
            subject=subject
        )

        if not statistics:
            logger.warning(f"未找到计划统计数据 - 学生ID: {child_id}")
            raise HTTPException(
                status_code=404,
                detail="未找到计划统计数据"
            )

        response = PlanStatistics(**statistics)
        logger.info(f"✅ API响应: 获取计划统计信息成功 - 学生ID: {child_id}, 总计划: {statistics.get('total_plans', 0)}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API异常: 获取计划统计信息失败 - 学生ID: {child_id}, 错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取计划统计信息失败: {str(e)}"
        )


@router.get("/health", response_model=dict)
async def health_check(
    service: PlanningService = Depends(get_planning_service)
):
    """
    计划服务健康检查
    """
    logger.debug("🔍 API请求: 计划服务健康检查")

    try:
        is_connected = service.influxdb.check_connection()

        status = "healthy" if is_connected else "unhealthy"
        response = {
            "service_status": status,
            "influxdb_connected": is_connected,
            "measurement": service.measurement,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        logger.info(f"💚 健康检查完成 - 状态: {status}, InfluxDB连接: {is_connected}")
        return response

    except Exception as e:
        logger.error(f"❌ 健康检查时发生异常: {e}")
        return {
            "service_status": "error",
            "influxdb_connected": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
