#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试规划模块统一测试启动脚本
"""

import sys
import os
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def run_setup():
    """运行InfluxDB设置"""
    print("🔧 运行InfluxDB设置...")
    try:
        from ai_child.api.test_planning.database.setup_influxdb import main as setup_main
        setup_main()
        return True
    except Exception as e:
        print(f"❌ InfluxDB设置失败: {e}")
        return False

def run_integration_tests():
    """运行集成测试"""
    print("\n🧪 运行集成测试...")
    try:
        from ai_child.api.test_planning.tests.test_influxdb_integration import main as integration_main
        integration_main()
        return True
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def run_api_tests():
    """运行API测试"""
    print("\n🌐 运行API测试...")
    try:
        from ai_child.api.test_planning.tests.test_enhanced_learning_api import main as api_main
        api_main()
        return True
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        import requests
        response = requests.get("http://localhost:8000/api/v1/daily-learning/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务器: {e}")
        print("💡 请确保服务器已启动: uvicorn ai_child.main:app --reload")
        return False

def show_file_structure():
    """显示文件结构"""
    print("\n📁 测试规划模块文件结构:")
    base_path = Path(__file__).parent
    
    def print_tree(path, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
        
        items = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name))
        for i, item in enumerate(items):
            is_last = i == len(items) - 1
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item.name}")
            
            if item.is_dir() and not item.name.startswith('.') and not item.name.startswith('__'):
                next_prefix = prefix + ("    " if is_last else "│   ")
                print_tree(item, next_prefix, max_depth, current_depth + 1)
    
    print_tree(base_path)

def main():
    """主函数"""
    print("🚀 测试规划模块统一测试启动")
    print("=" * 60)
    
    # 显示文件结构
    show_file_structure()
    
    print("\n" + "=" * 60)
    
    # 检查服务器状态
    server_running = check_server_status()
    
    # 运行设置
    setup_success = run_setup()
    
    # 运行集成测试
    integration_success = run_integration_tests()
    
    # 如果服务器运行，则运行API测试
    api_success = True
    if server_running:
        api_success = run_api_tests()
    else:
        print("\n⚠️ 跳过API测试（服务器未运行）")
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"InfluxDB设置: {'✅ 成功' if setup_success else '❌ 失败'}")
    print(f"集成测试: {'✅ 通过' if integration_success else '❌ 失败'}")
    print(f"API测试: {'✅ 通过' if api_success else '❌ 失败' if server_running else '⏭️ 跳过'}")
    
    if setup_success and integration_success and (api_success or not server_running):
        print("\n🎉 所有测试完成！系统运行正常。")
        
        if not server_running:
            print("\n💡 要运行完整的API测试，请启动服务器:")
            print("   cd /root/projects/smart_friend")
            print("   uvicorn ai_child.main:app --reload")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和日志。")
    
    print("\n📚 相关文档:")
    print("   - 数据格式说明: ai_child/api/test_planning/docs/每日学习情况数据格式说明.md")
    print("   - API文档: http://localhost:8000/docs (服务器运行时)")

if __name__ == "__main__":
    main()
