#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版每日学习数据API测试脚本
基于图片中的每日学习情况字段结构
"""

import requests
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000/api/v1/daily-learning"

def test_create_enhanced_learning_record():
    """测试创建增强版学习记录"""
    print("=== 测试创建增强版学习记录 ===")
    
    # 基于图片结构的完整测试数据
    learning_data = {
        "child_id": 1,
        "subject": "数学",
        "activity_type": "homework",
        "difficulty_level": 3,
        
        # === 专注度相关 ===
        "concentration_level": 4,
        "internal_interruptions": 2,  # 内部中断次数（分心）
        "desk_leaving_times": 1,      # 离开课桌次数
        
        # === 作业完成情况 ===
        "homework_completion_rate": 95.0,  # 作业完成率
        "completion_rate": 90.0,           # 总体完成率
        "accuracy_rate": 88.0,             # 正确率
        
        # === 完成耗时 ===
        "total_duration_minutes": 120.0,    # 总耗时
        "subject_duration_minutes": 45.0,   # 单科耗时（数学）
        "task_duration_minutes": 30.0,      # 任务耗时
        "study_duration_minutes": 45.0,     # 学习时长
        
        # === 时间管理 ===
        "is_ahead_schedule": False,          # 是否提前完成
        "is_behind_schedule": True,          # 是否延迟完成
        "schedule_deviation_minutes": 15.0,  # 延迟15分钟
        
        # === 积分奖励 ===
        "points_earned": 85,    # 获得积分
        "bonus_points": 10,     # 奖励积分
        
        # === 学科强弱势 ===
        "is_weak_subject": False,           # 不是薄弱学科
        "is_strong_subject": True,          # 是强势学科
        "subject_performance_level": 4,     # 学科表现等级
        
        # === 评分数据 ===
        "score": 88.0,
        "max_score": 100.0,
        
        # === 情感和态度评价 ===
        "enjoyment_rating": 4,      # 享受程度
        "difficulty_rating": 3,     # 难度感受
        "motivation_level": 4,      # 学习动机
        
        # === 学习行为 ===
        "questions_asked": 3,       # 提问次数
        "help_requests": 1,         # 求助次数
        "breaks_taken": 2,          # 休息次数
        
        # === 文本信息 ===
        "notes": "今天数学作业完成得不错，但有些分心，需要提高专注度",
        "feedback": "数学是强势学科，继续保持。建议减少分心次数，提高学习效率",
        "schedule_summary": "比计划延迟15分钟完成，主要原因是中途分心2次，离开课桌1次"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/learning-records",
            json=learning_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"创建增强版学习记录失败: {e}")
        return False

def test_create_multiple_subject_records():
    """测试创建多个学科的学习记录"""
    print("\n=== 测试创建多学科学习记录 ===")
    
    subjects_data = [
        {
            "subject": "语文",
            "is_weak_subject": True,
            "is_strong_subject": False,
            "subject_performance_level": 2,
            "homework_completion_rate": 75.0,
            "internal_interruptions": 4,
            "desk_leaving_times": 2,
            "schedule_deviation_minutes": 25.0,
            "is_behind_schedule": True,
            "points_earned": 60,
            "schedule_summary": "语文作业延迟较多，需要加强基础练习"
        },
        {
            "subject": "英语", 
            "is_weak_subject": False,
            "is_strong_subject": False,
            "subject_performance_level": 3,
            "homework_completion_rate": 85.0,
            "internal_interruptions": 1,
            "desk_leaving_times": 0,
            "schedule_deviation_minutes": -5.0,
            "is_ahead_schedule": True,
            "points_earned": 80,
            "schedule_summary": "英语作业提前完成，表现良好"
        },
        {
            "subject": "科学",
            "is_weak_subject": False,
            "is_strong_subject": True,
            "subject_performance_level": 5,
            "homework_completion_rate": 100.0,
            "internal_interruptions": 0,
            "desk_leaving_times": 0,
            "schedule_deviation_minutes": -10.0,
            "is_ahead_schedule": True,
            "points_earned": 100,
            "bonus_points": 20,
            "schedule_summary": "科学作业完成优秀，获得奖励积分"
        }
    ]
    
    success_count = 0
    
    for i, subject_info in enumerate(subjects_data):
        learning_data = {
            "child_id": 1,
            "activity_type": "homework",
            "difficulty_level": subject_info["subject_performance_level"],
            "concentration_level": 5 - subject_info.get("internal_interruptions", 0),
            "total_duration_minutes": 90.0 + (i * 15),
            "subject_duration_minutes": 30.0 + (i * 10),
            "completion_rate": subject_info["homework_completion_rate"],
            "accuracy_rate": subject_info["homework_completion_rate"] - 5,
            "score": subject_info["homework_completion_rate"],
            "max_score": 100.0,
            "enjoyment_rating": subject_info["subject_performance_level"],
            "difficulty_rating": 6 - subject_info["subject_performance_level"],
            "motivation_level": subject_info["subject_performance_level"],
            "timestamp": (datetime.now(timezone.utc) - timedelta(days=i)).isoformat(),
            **subject_info
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/learning-records",
                json=learning_data,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                success_count += 1
                print(f"✓ 成功创建{subject_info['subject']}学习记录")
            else:
                print(f"✗ 创建{subject_info['subject']}学习记录失败: {response.status_code}")
        except Exception as e:
            print(f"✗ 创建{subject_info['subject']}学习记录异常: {e}")
    
    print(f"成功创建 {success_count}/{len(subjects_data)} 条记录")
    return success_count > 0

def test_get_enhanced_learning_records():
    """测试获取增强版学习记录"""
    print("\n=== 测试获取增强版学习记录 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/learning-records/1?limit=3")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            records = response.json()
            print(f"获取到 {len(records)} 条记录")
            if records:
                print("第一条记录的新字段:")
                record = records[0]
                enhanced_fields = [
                    "internal_interruptions", "desk_leaving_times", 
                    "homework_completion_rate", "total_duration_minutes",
                    "is_ahead_schedule", "is_behind_schedule", "schedule_deviation_minutes",
                    "points_earned", "bonus_points", "is_weak_subject", "is_strong_subject",
                    "subject_performance_level", "motivation_level", "schedule_summary"
                ]
                
                for field in enhanced_fields:
                    if field in record and record[field] is not None:
                        print(f"  {field}: {record[field]}")
        else:
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"获取增强版学习记录失败: {e}")
        return False

def test_learning_analytics():
    """测试学习分析功能"""
    print("\n=== 测试学习分析功能 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/learning-statistics/1?days=7")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            stats = response.json()
            print("学习统计分析:")
            print(f"  总记录数: {stats.get('total_records', 0)}")
            print(f"  总学习时间: {stats.get('total_study_time', 0)} 分钟")
            print(f"  平均完成率: {stats.get('average_completion_rate', 0):.1f}%")
            print(f"  平均正确率: {stats.get('average_accuracy_rate', 0):.1f}%")
            print(f"  平均享受程度: {stats.get('average_enjoyment', 0):.1f}/5")
            print(f"  学习学科: {', '.join(stats.get('subjects_studied', []))}")
        else:
            print(f"响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"获取学习分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试增强版InfluxDB每日学习数据API")
    print("基于每日学习情况图片结构")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("创建增强版学习记录", test_create_enhanced_learning_record),
        ("创建多学科记录", test_create_multiple_subject_records),
        ("获取增强版记录", test_get_enhanced_learning_records),
        ("学习分析功能", test_learning_analytics),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")

if __name__ == "__main__":
    main()
