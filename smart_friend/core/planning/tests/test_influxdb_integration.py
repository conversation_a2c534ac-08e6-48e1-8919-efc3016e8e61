#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InfluxDB集成测试脚本
测试数据库连接、数据写入、查询等功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from datetime import datetime, timezone, timedelta
from ai_child.api.test_planning.database.influxdb_connection import get_influxdb_manager
from ai_child.api.test_planning.services.daily_learning_service import DailyLearningService

def test_influxdb_connection():
    """测试InfluxDB连接"""
    print("=== 测试InfluxDB连接 ===")
    
    influxdb = get_influxdb_manager()
    
    if influxdb.check_connection():
        print("✓ InfluxDB连接成功")
        return True
    else:
        print("✗ InfluxDB连接失败")
        return False

def test_daily_learning_service():
    """测试每日学习服务"""
    print("\n=== 测试每日学习服务 ===")
    
    service = DailyLearningService()
    
    # 测试数据
    learning_data = {
        "activity_type": "homework",
        "difficulty_level": 3,
        
        # 专注度相关
        "concentration_level": 4,
        "internal_interruptions": 2,
        "desk_leaving_times": 1,
        
        # 作业完成情况
        "homework_completion_rate": 95.0,
        "completion_rate": 90.0,
        "accuracy_rate": 88.0,
        
        # 完成耗时
        "total_duration_minutes": 120.0,
        "subject_duration_minutes": 45.0,
        "task_duration_minutes": 30.0,
        "study_duration_minutes": 45.0,
        
        # 时间管理
        "is_ahead_schedule": False,
        "is_behind_schedule": True,
        "schedule_deviation_minutes": 15.0,
        
        # 积分奖励
        "points_earned": 85,
        "bonus_points": 10,
        
        # 学科强弱势
        "is_weak_subject": False,
        "is_strong_subject": True,
        "subject_performance_level": 4,
        
        # 评分数据
        "score": 88.0,
        "max_score": 100.0,
        
        # 情感和态度评价
        "enjoyment_rating": 4,
        "difficulty_rating": 3,
        "motivation_level": 4,
        
        # 学习行为
        "questions_asked": 3,
        "help_requests": 1,
        "breaks_taken": 2,
        
        # 文本信息
        "notes": "今天数学作业完成得不错，但有些分心",
        "feedback": "数学是强势学科，继续保持",
        "schedule_summary": "比计划延迟15分钟完成，主要原因是中途分心2次"
    }
    
    # 测试添加记录
    success = service.add_learning_record(
        child_id=1,
        subject="数学",
        learning_data=learning_data
    )
    
    if success:
        print("✓ 成功添加学习记录")
    else:
        print("✗ 添加学习记录失败")
        return False
    
    # 测试获取记录
    records = service.get_learning_records(child_id=1, limit=5)
    
    if records:
        print(f"✓ 成功获取 {len(records)} 条学习记录")
        
        # 显示第一条记录的详细信息
        if records:
            record = records[0]
            print("最新记录详情:")
            print(f"  时间: {record.get('timestamp')}")
            print(f"  学科: {record.get('subject')}")
            print(f"  专注度: {record.get('concentration_level')}")
            print(f"  分心次数: {record.get('internal_interruptions')}")
            print(f"  作业完成率: {record.get('homework_completion_rate')}%")
            print(f"  是否强势学科: {record.get('is_strong_subject')}")
            print(f"  获得积分: {record.get('points_earned')}")
    else:
        print("✗ 获取学习记录失败")
        return False
    
    # 测试统计信息
    stats = service.get_learning_statistics(child_id=1, days=7)
    
    if stats:
        print("✓ 成功获取学习统计信息")
        print(f"  总记录数: {stats.get('total_records')}")
        print(f"  总学习时间: {stats.get('total_study_time')} 分钟")
        print(f"  平均完成率: {stats.get('average_completion_rate'):.1f}%")
        print(f"  学习学科: {', '.join(stats.get('subjects_studied', []))}")
    else:
        print("✗ 获取学习统计信息失败")
        return False
    
    return True

def test_multiple_subjects():
    """测试多学科数据"""
    print("\n=== 测试多学科数据 ===")
    
    service = DailyLearningService()
    
    subjects_data = [
        {
            "subject": "语文",
            "data": {
                "is_weak_subject": True,
                "subject_performance_level": 2,
                "homework_completion_rate": 75.0,
                "internal_interruptions": 4,
                "points_earned": 60,
                "schedule_summary": "语文作业延迟较多"
            }
        },
        {
            "subject": "英语",
            "data": {
                "is_weak_subject": False,
                "subject_performance_level": 3,
                "homework_completion_rate": 85.0,
                "internal_interruptions": 1,
                "points_earned": 80,
                "schedule_summary": "英语作业提前完成"
            }
        },
        {
            "subject": "科学",
            "data": {
                "is_strong_subject": True,
                "subject_performance_level": 5,
                "homework_completion_rate": 100.0,
                "internal_interruptions": 0,
                "points_earned": 100,
                "bonus_points": 20,
                "schedule_summary": "科学作业完成优秀"
            }
        }
    ]
    
    success_count = 0
    
    for subject_info in subjects_data:
        success = service.add_learning_record(
            child_id=1,
            subject=subject_info["subject"],
            learning_data=subject_info["data"]
        )
        
        if success:
            success_count += 1
            print(f"✓ 成功添加{subject_info['subject']}记录")
        else:
            print(f"✗ 添加{subject_info['subject']}记录失败")
    
    print(f"成功添加 {success_count}/{len(subjects_data)} 条记录")
    return success_count > 0

def test_data_cleanup():
    """测试数据清理"""
    print("\n=== 测试数据清理 ===")
    
    service = DailyLearningService()
    
    # 删除测试数据
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=1)
    
    success = service.delete_learning_records(
        child_id=1,
        start_date=start_date,
        end_date=end_date
    )
    
    if success:
        print("✓ 成功清理测试数据")
        return True
    else:
        print("✗ 清理测试数据失败")
        return False

def main():
    """主测试函数"""
    print("开始InfluxDB集成测试")
    print("=" * 50)
    
    tests = [
        ("InfluxDB连接", test_influxdb_connection),
        ("每日学习服务", test_daily_learning_service),
        ("多学科数据", test_multiple_subjects),
        ("数据清理", test_data_cleanup),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"{test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("集成测试结果汇总:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("\n🎉 所有集成测试通过！系统运行正常。")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查配置。")

if __name__ == "__main__":
    main()
