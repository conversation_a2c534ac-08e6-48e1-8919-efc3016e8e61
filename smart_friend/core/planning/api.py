# Planning模块API路由汇总
from fastapi import APIRouter

from .endpoints import daily_learning, planning, plan_modification_api

# 创建planning API路由器
planning_router = APIRouter()

# 包含每日学习数据路由
planning_router.include_router(
    daily_learning.router,
    prefix="/daily-learning",
    tags=["每日学习数据"]
)

# 包含学习计划路由
planning_router.include_router(
    planning.router,
    prefix="/plans",
    tags=["学习计划管理"]
)

# AI学习计划生成路由已移除，功能已整合到其他模块

# 包含计划表修改路由
planning_router.include_router(
    plan_modification_api.router,
    prefix="/plans",
    tags=["计划表修改"]
)
