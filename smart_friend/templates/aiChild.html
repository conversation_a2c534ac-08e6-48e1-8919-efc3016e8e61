<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Child - 语音识别界面</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
    .input-section textarea {
        width: 40%;
        height: 200px;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
        margin: 10px;
    }

    .input-section textarea:focus {
        border-color: #007bff;
        outline: none;
    }

    .input-section #submitData {
        margin: 20px auto;
        display: block;
    }
         /* 新增任务计划表格样式 */
         .task-plan-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        .task-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .task-table th, .task-table td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        .task-table th {
            background-color: #007bff;
            color: white;
        }

        .task-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        .task-table ul {
            list-style-type: none;
            padding-left: 0;
        }

        .task-table li {
            margin-bottom: 8px;
        }

        /* .task-plan-section {
            margin-top: 20px;
        } */

        #taskPlanTable {
            width: 100%;
            border-collapse: collapse;
        }

        #taskPlanTable th, #taskPlanTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .tag {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 16px;
            margin: 2px;
            font-size: 0.9em;
        }

        .tag-delete {
            margin-left: 6px;
            cursor: pointer;
            font-weight: bold;
            color: #ff4444;
        }

        .tag-delete:hover {
            color: #cc0000;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background:rgba(255, 255, 255, 0.7);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 1200px;
            width: 120%;
            margin-left: -20%; /* 负值会让容器向左移动，可根据需求调整数值 */
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-card {
            position: fixed;
            top: 20%;
            right:2%;
            transform: translateY(-50%);
            max-width: 300px; /* 可根据需要调整宽度 */
            z-index: 1000; /* 确保在其他元素之上 */
            border-radius: 15px; /* 设置圆角 */
            padding: 30px;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3); /* 增加边框 */
            /* 使用渐变背景，和浅紫色搭配 */
            background: linear-gradient(135deg, #a599e9 0%, #73a6ff 100%); 
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* 添加阴影 */
        }



        .microphone-indicator {
            font-size: 4em;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .microphone-indicator.recording {
            animation: pulse 1.5s infinite;
            color: #dc3545;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .status {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status.connected {
            color: #28a745;
        }

        .status.recording {
            color: #dc3545;
        }

        .status.inactive {
            color: #6c757d;
        }

        .controls {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-bottom: 40px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 180px;
            justify-content: center;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-start {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-stop {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }

        .btn-connect {
            background: linear-gradient(45deg, #007bff, #6610f2);
            color: white;
        }

        .btn-submit {
            background: linear-gradient(45deg, #4685b9, #6ec79a);
            color: white;
        }

        .btn-checkTask {
            background: linear-gradient(45deg, #3fc43f, #6ec79a);
            color: white;
        }

        .results-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        .voice-results-section {
            position: fixed;
            top: 60%;
            bottom: -40%; /* 增加底部距离，确保有空间扩展 */
            right: 1%;
            transform: translateY(-60%);
            max-width: 350px; /* 适当增加宽度 */
            width: 50%; /* 保证在小屏幕也有合适的宽度 */
            z-index: 1000;
            border-radius: 20px; /* 增加圆角 */
            padding: 25px; /* 增加内边距 */
            background: linear-gradient(135deg, #7ca6ee 0%, #5c8ee6 100%); /* 使用渐变背景 */
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); /* 增加阴影 */
            transition: all 0.3s ease; /* 添加过渡效果 */
            color: white; /* 设置文本颜色 */
            display: flex; /* 使用 flex 布局 */
            flex-direction: column; /* 垂直排列子元素 */
        }

        .results-header {
            flex-shrink: 0; /* 头部不收缩 */
            margin-bottom: 20px; /* 与列表之间的间距 */
        }

        .results-header h3 {
            color: #333;
            margin: 0;
        }

        .clear-btn {
            position: fixed;
            right:10% ;
            top:3%;
            background: linear-gradient(45deg, #ff4757, #ff6b81); /* 好看的渐变背景色 */
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease; /* 添加过渡效果 */
        }

        .clear-btn:hover {
            background: linear-gradient(45deg, #ff6b81, #ff4757); /* 鼠标悬停时的渐变背景色 */
        transform: translateY(-2px); /* 鼠标悬停时向上移动 2px */
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* 鼠标悬停时添加阴影 */
        }

        .results-list {
            flex-grow: 1; /* 列表自动填充剩余空间 */
            overflow-y: auto; /* 内容超出时显示垂直滚动条 */
            border: 1px solid #dee2e6;
            border-radius: 10px;
            background: white;
            padding: 20px;
            color: #333; 
        }

        .result-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
            animation: slideIn 0.3s ease;
        }

        .result-item.final {
            border-left-color: #28a745;
            background: #d4edda;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .result-text {
            font-size: 1.1em;
            color: #333;
            margin-bottom: 5px;
        }

        .result-meta {
            font-size: 0.9em;
            color: #666;
            display: flex;
            justify-content: space-between;
        }

        .empty-state {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            z-index: 1000;
        }

        .connection-status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .connection-status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* 新增摄像头相关样式 */
        #video, #canvas {
            max-width: 100%;
            margin: 20px 0;
        }
        #camera-controls {
            margin: 20px 0;
        }
        #notification {
            position: fixed;
            top: 20px;
            left: 0%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            display: none;
        }
        .image-container {
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .container {
                background: rgb(168, 75, 75);
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                padding: 40px;
                max-width: 1200px; /* 增加最大宽度 */
                width: 70%; /* 增加宽度比例 */
                margin-left: 5%; /* 让主体稍微偏左 */
                margin-right: auto;
            }

            .controls {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        .input-section {
        text-align: center;
        }

        .subject-detection-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        margin-top: 20px;
        
    }

    .subject-detection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .subject-detection-header h3 {
        color: #333;
        margin: 0;
    }

    .subject-status-indicator {
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 20px;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeeba;
        color: #856404;
    }

    .subject-webcam-container {
        text-align: center;
        margin-bottom: 20px;
    }

    .task-webcam-container {
        text-align: center;
        margin-bottom: 20px;
    }

    #subject-webcam {
        max-width: 100%;
        height: 200px;
        border: 2px solid #ddd;
        border-radius: 10px;
    }

    #stask-webcam {
        max-width: 100%;
        height: 200px;
        border: 2px solid #ddd;
        border-radius: 10px;
    }

    .subject-control-buttons {
        display: flex; /* 使用 Flexbox 布局 */
        text-align: center;
        margin-bottom: 20px;
    }
    

    .subject-result-container {
        min-height: 150px;
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        text-align: center;
    }

    .subject-stats-container {
        margin-top: 20px;
    }

    .input-section {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 20px; /* 文本框之间的间距 */
        margin: 20px 0;
    }
        /* 优化文本框样式 */
        .input-section textarea {
        width: 40%;
        height: 200px;
        padding: 15px;
        border: 2px solid #e0e0e0;
        border-radius: 12px;
        font-size: 16px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        transition: border-color 0.3s ease, box-shadow 0.3s ease;
    }

    .input-section textarea:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        outline: none;
    }


    .input-section label {
        margin-bottom: 10px; /* 为 label 和 textarea 之间添加间距 */
        font-weight: bold; /* 让 label 文字加粗 */
    }

    .finish-section {
            position: fixed;
            top: 90%;
            left: 90%;
            transform: translate(-50%, -50%);
            background: linear-gradient(45deg, #7ca6ee , #5c8ee6 ); /* 鼠标悬停时的渐变背景色 */
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 20px;
            z-index: 1000; /* 确保显示在其他元素之上 */
        }

        /* 可以添加遮罩层，让背景变暗 */
    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999; /* 遮罩层在 finish-section 之下 */
        display: none; /* 默认隐藏 */
    }

    @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }

         /* 新增或修改消息框样式 */
    #envCheckingModal {
        display: none;
        position: fixed;
        top:10%;
        left: 40%;
        width: 20%;
        background-color: #fff3cd;
        color: #856404;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid #ffeeba;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        z-index: 1001;
        font-weight: bold;
        font-size: 1.1em;
    }

            /* 科目检测弹窗样式 */
        #subjectCheckModal {
            display: none;
            position: fixed;
            top:85%;
            left: 88.5%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #a599e9 0%, #73a6ff 100%); /* 紫色渐变背景 */
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            z-index: 1001;
            width: 350px; /* 设置固定宽度 */
            max-width: 90%; /* 最大宽度，防止在小屏幕下过大 */
            color: white; /* 文字颜色 */
        }

        #subjectCheckModal h3 {
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2); /* 文字阴影 */
        }

        #subjectCheckModal p {
            font-size: 1.2em;
            margin-bottom: 15px;
        }

        #subjectCheckModal button {
            display: block;
            margin: 20px auto 0;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            background: linear-gradient(45deg, #dc3545, #fd7e14); /* 按钮渐变背景 */
            color: white;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #subjectCheckModal button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .deleted-subtasks-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-top: 20px;
        }

        #deletedSubtasksTable {
        width: 100%;
        border-collapse: collapse;
    }

    #deletedSubtasksTable th, #deletedSubtasksTable td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    #deletedSubtasksTable th {
        background-color: #007bff;
        color: white;
    }

    #deletedSubtasksTable tr:nth-child(even) {
        background-color: #f2f2f2;
    }

            /* 定制撤销按钮样式 */
            #deletedSubtasksTable td button {
            padding: 8px 16px; /* 增大内边距 */
            border: none;
            border-radius: 5px; /* 圆角边框 */
            background: linear-gradient(45deg, #28a745, #20c997); /* 绿色渐变背景 */
            color: white;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        #deletedSubtasksTable td button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

    .tag {
        display: inline-block;
        background-color: #007bff;
        color: white;
        padding: 4px 8px;
        border-radius: 16px;
        margin: 2px;
        font-size: 0.9em;
    }

    .voice-input-section {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        align-items: center;
    }

    .task-select {
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        flex: 1;
    }

    .subtask-input {
        padding: 10px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        flex: 2;
    }

            /* 时间输入框样式 */
            input[type="time"] {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: border-color 0.3s ease;
            width: 120px; /* 固定宽度 */
            margin: 0 5px; /* 添加左右边距 */
        }

        input[type="time"]:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
        }

        /* 时间输入框中间的分隔符样式 */
        .task-plan-section td > span {
            margin: 0 5px;
            font-weight: bold;
            color: #333;
        }

    </style>
</head>
<body>
    <div class="connection-status" id="connectionStatus">连接中...</div>
    <div id="envCheckingModal" style="display: none; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1001;">
        桌面环境检测中，请稍候...
    </div>
    <div id="subjectCheckModal" style=" display:none; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1001;">
       
        
        <!-- <button onclick="closeSubjectCheckModal()">关闭</button> -->
    </div>

    <!-- 正在分析中弹窗 -->
    <div id="analyzingModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: rgba(0, 0, 0, 0.7); color: white; padding: 20px; border-radius: 10px; z-index: 1002;">
        正在分析中，请稍候...
    </div>

    <!-- 分析结果弹窗 -->
    <div id="analysisResultModal" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.3); z-index: 1002; max-height: 80vh; overflow-y: auto;">
        <h3>分析结果</h3>
        <p id="resultContent" style="display: none"></p>
        <div id="markdown-container"></div>
        <button class="clear-btn" onclick="closeAnalysisResultModal()">关闭</button>
    </div>


    <div class="container">
        <div class="header">
            <h1>🎤 AI Child 语音识别</h1>
            <p>基于豆包(火山引擎)API的实时语音转文字服务</p>
        </div>

        <div class="status-card" style="display: none">
            <div class="microphone-indicator" id="micIndicator">🎤</div>
            <div id="status" class="status inactive">等待连接...</div>
            <div id="statusMessage" style="margin-top: 10px; color: #666;">
                请点击"连接服务"按钮启动自动语音识别
            </div>
            <div id="silenceStatus" style="margin-top: 10px; color: #ff6b35; font-weight: bold; display: none;">
                静音检测: <span id="silenceCountdown">2.0</span>秒后自动停止
            </div>
        </div>

        <div class="controls">
            <button id="connectBtn" class="btn btn-connect" onclick="connectService()">
                <span>🔗</span>
                连接服务
            </button>
            <button id="startBtn" class="btn btn-start" onclick="startRecognition()" disabled style="display: none;">
                <span>🎙️</span>
                开始语音识别
            </button>
            <button id="stopBtn" class="btn btn-stop" onclick="stopRecognition()" disabled style="display: none;">
                <span>⏹️</span>
                停止语音识别
            </button>
            <div id="autoModeIndicator" class="btn" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; cursor: default;">
                <span>🔄</span>
                自动语音识别模式
            </div>
        </div>
        <div class="input-section">
            <textarea id="teacherDailyTask" 
            placeholder="输入教师每日任务" 
            style="width: 40%; height: 200px;"></textarea>
            <textarea id="yesterdayFeedback" placeholder="输入昨日任务反馈" style="width: 40%; height: 200px;"></textarea>
    
        </div>
        <div class="input-section">
            <button id="submitData" class="btn btn-submit" style="left: 45%; text-align: center;">提交数据</button>
        </div>
        <div id="taskPlanLoading" style="display: none; text-align: center; color: #007bff; margin-bottom: 20px;">
            <span class="loading-spinner"></span>任务计划正在生成中，请稍候...
        </div>
        <!-- <div class="task-plan-section">
            <h3>个性化任务计划</h3>
            <table id="taskPlanTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>时段</th>
                        <th>子任务</th>
                        <th>定制</th>
                        <th>难点</th>
                        <th>方案</th>
                        <th>执行信心指数</th>
                    </tr>
                </thead>
                <tbody id="taskPlanBody">
                </tbody>
            </table>
        </div> -->
        
        <div class="task-plan-section">
            <h3>个性化任务计划</h3>

                <!-- 新增语音输入相关元素 -->
            <div class="voice-input-section">
                <select id="taskNameSelect" class="task-select">
                    <option value="">请选择任务</option>
                </select>
                <button id="startVoiceInput" class="btn btn-start">
                    <span>🎙️</span>
                    语音输入子任务
                </button>
                <input type="text" id="newSubTaskInput" placeholder="新增子任务内容" class="subtask-input">
                <button id="addSubTaskBtn" class="btn btn-submit">添加子任务</button>
            </div>
            <table id="taskPlanTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>时段</th>
                        <th>子任务</th>
                        <th>定制</th>
                        <th>难点</th>
                        <th>方案</th>
                        <th>执行信心指数</th>
                    </tr>
                </thead>
                <tbody id="taskPlanBody">
                </tbody>
            </table>
        </div>
        <!-- 新增已删除子任务表格 -->
        <div class="deleted-subtasks-section">
            <h3>已删除的子任务</h3>
            <table id="deletedSubtasksTable">
                <thead>
                    <tr>
                        <th>任务名称</th>
                        <th>子任务内容</th>
                        <th>子任务来源</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="deletedSubtasksBody">
                </tbody>
            </table>
        </div>
        
        <div class="input-section">
            <button id="checkTask" class="btn btn-checkTask" style=" margin: 20px auto;
        display: block;">确认任务</button>
        </div>
        <!-- 新增摄像头相关 HTML 元素 -->
        <div id="camera-controls">
            <button id="start-camera"  class="btn btn-connect"  style="display: none">启动摄像头</button>
            <button id="capture" class="btn btn-start" disabled style="display: none">拍照</button>
            <button id="analyze" class="btn btn-stop" disabled style="display: none">分析</button>
        </div>
        <video id="video" autoplay playsinline></video>
        <canvas id="canvas" style="display: none;"></canvas>
        <div class="image-container" id="original-image-container">
            <p class="placeholder-text">检测图像:</p>
            <img id="original-image" style="display: none;">
        </div>
        <!-- <div class="image-container" id="annotated-image-container">
            <p class="placeholder-text">标注图像将显示在这里</p>
            <img id="annotated-image" style="display: none;">
        </div> -->
        <!-- <pre id="items-text"></pre>
        <pre id="tidiness-text"></pre>
        <div id="notification"></div> -->

        <!-- 新增桌面整洁度和清理建议文本框 -->
        <div class="input-section">
            <label for="deskTidiness">桌面整洁度:</label>
            <textarea id="deskTidiness"  style="width: 80%; height: 100px;"></textarea>
        </div>
        <div class="input-section">
            <label for="deskSuggestion">桌面清理建议:</label>
            <textarea id="deskSuggestion"  style="width: 80%; height: 100px;"></textarea>
        </div>
        <div class="input-section">
            <button id="checkEnv" class="btn btn-checkTask" style=" margin: 20px auto;
        display: block;">清理完成</button>
        </div>


        <div class="results-section subject-detection-section">
            <div class="results-header subject-detection-header">
                <h3>📚 学科检测功能</h3>
            </div>
            
            <!-- 学科检测状态
            <div id="subject-status-indicator" class="subject-status-indicator alert alert-warning">
                <span id="subject-status-text">学科检测状态: 未初始化</span>
            </div> -->
        
            <!-- 摄像头视频流 -->
            <!-- <div class="subject-webcam-container">
                <video id="subject-webcam" autoplay playsinline></video>
            </div> -->

            <!-- 摄像头视频流 -->
            <!-- <div class="task-webcam-container">
                <video id="task-webcam" autoplay playsinline></video>
            </div> -->
                    
        
            <!-- 控制按钮 -->


            <div class="input-section">
                <button id="subject-capture-btn" class="btn btn-checkTask" style=" margin: 20px auto;
            display: block;">拍照检测学科</button>

                <button id="task-generate-btn" class="btn btn-checkTask" style=" margin: 20px auto;
                            display: block;">拍照生成任务</button>
            </div>

            <div class="input-section">
                <button id="summary-btn"  class="btn btn-checkTask" style=" margin: 20px auto;
            display: block;">总结汇报</button>
            <button id="finish-task-btn" class="btn btn-checkTask" style=" margin: 20px auto;
            display: block; background: linear-gradient(45deg, #3fc43f, #6ec79a);">提交检测</button>
            </div>
        
            <!-- 检测结果显示区域 -->
            <div id="subject-result-container" class="subject-result-container">
                <p class="text-muted">请拍照或上传图片进行学科检测</p>
            </div>
        
            <!-- 统计信息 -->
            <div id="subject-stats-container" class="subject-stats-container" style="margin-top: 20px;">
                <!-- 统计信息将在这里显示 -->
            </div>
            
        </div>

        <div class="voice-results-section">
            <div class="results-header">
                <h3>📝 识别结果</h3>
                <button class="clear-btn" onclick="clearResults()">清空结果</button>
            </div>
            <div class="results-list" id="resultsList">
                <div class="empty-state">
                    暂无识别结果，请开始语音识别...
                </div>
            </div>

            <p id="currentSubjectText">检测的科目: </p>
            <p id="targetTaskNameText">计划的科目: </p>
            <div class="subject-webcam-container">
                <video id="subject-webcam" autoplay playsinline></video>
            </div>
        </div>

  <!-- 音频播放器 -->
        <div class="audio-player-section" id="audioPlayer" style="display: none;">
            <div class="audio-player-header">
                <span>🔊</span>
                <h4>语音播放</h4>
            </div>
            <div class="audio-text" id="audioText">
                等待语音播放...
            </div>
        </div>

        
        
    </div>

    <script>
        // 全局变量
        const socket = io();
        let isRecording = false;
        let isConnected = false;
        let audioContext;
        let processor;
        let microphone;
        let currentSubject = '';
        let targetTaskName = '';
        let currentAudio = null;

        // 新增全局变量，用于标记是否处于子任务语音输入状态
        let isSubTaskVoiceInput = false;

        // 新增摄像头相关全局变量
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let captureButton = document.getElementById('capture');
        let analyzeButton = document.getElementById('analyze');
        let checkTaskButton = document.getElementById('checkTask');
        let checkEnvButton = document.getElementById('checkEnv');
        let startCameraButton = document.getElementById('start-camera');
        let subjectResultContainer = document.getElementById('subject-result-container');
        let subjectWebcam = document.getElementById('subject-webcam');
        let taskWebcam = document.getElementById('task-webcam');
        let subjectCaptureBtn = document.getElementById('subject-capture-btn');
        let taskGenerateBtn  = document.getElementById('task-generate-btn');
        let summaryBtn = document.getElementById('summary-btn');
        let subjectStatsContainer = document.getElementById('subject-stats-container');
       
        let finishTaskBtn = document.getElementById('finish-task-btn');    

        
        let capturedImage = null;
        let analysisResult = null;

        finishTaskBtn.addEventListener('click', async () => {
            try {


                const response = await fetch('/api/sumbitFinishTask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });
                
                // 读取响应体为文本
                const responseText = await response.text();

                console.log('提交任务接口 /api/sumbitFinishTask 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });


         // 初始化学科检测功能
         async function initSubjectDetection() {
            try {
                // 获取摄像头权限
                subjectStream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 640 },
                        height: { ideal: 480 }
                    }
                });
                subjectWebcam.srcObject = subjectStream;

                console.log('学科检测功能初始化完成');
            } catch (err) {
                console.error('初始化学科检测功能失败:', err);
                subjectStatusText.textContent = '无法访问摄像头，请检查权限设置';
                subjectStatusIndicator.className = 'alert alert-danger';
            }
        }


         // 显示学科检测结果
         function displaySubjectResult(result) {
            if (result.success) {
                const subject = result.subject;
                const confidence = result.confidence;
                const confidencePercent = (confidence * 100).toFixed(1);

                // 根据置信度设置颜色
                let confidenceColor = 'text-danger';
                if (confidence >= 0.7) confidenceColor = 'text-success';
                else if (confidence >= 0.4) confidenceColor = 'text-warning';

                subjectResultContainer.innerHTML = `
                    <div class="text-center">
                        <div class="mb-3">
                            <i class="fas fa-book fa-3x text-primary mb-3"></i>
                            <h4 class="font-weight-bold">${subject}</h4>
                        </div>
                        <div class="progress mb-2" style="height: 25px;">
                            <div class="progress-bar ${confidence >= 0.7 ? 'bg-success' : confidence >= 0.4 ? 'bg-warning' : 'bg-danger'}"
                                 role="progressbar"
                                 style="width: ${confidencePercent}%"
                                 aria-valuenow="${confidencePercent}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                ${confidencePercent}%
                            </div>
                        </div>
                        <p class="${confidenceColor}">置信度: ${confidencePercent}%</p>
                    </div>
                `;

                // 更新 currentSubject 变量
                currentSubject = subject;
                showSubjectCheckModal();

                // 如果有图像路径，显示图像
                if (result.image_path) {
                    subjectResultContainer.innerHTML += `
                        <div class="mt-3 text-center">
                            <img src="/${result.image_path}" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    `;
                }
            } else {
                subjectResultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 检测失败: ${result.message || '未知错误'}
                    </div>
                `;
            }
        }
                // 更新学科检测统计
        async function updateSubjectStats() {
            try {
                const response = await fetch('/api/subject/status');
                const data = await response.json();

                if (data.success) {
                    updateSubjectStatsDisplay(data);
                } else {
                    console.error('获取学科检测统计失败:', data.message);
                }
            } catch (err) {
                console.error('获取学科检测统计时出错:', err);
            }
        }



        // 显示学科检测统计
        function updateSubjectStatsDisplay(stats) {
            if (stats.success) {
                const historyCount = stats.history ? stats.history.length : 0;
                const durationsCount = stats.durations ? Object.keys(stats.durations).length : 0;

                // subjectStatsContainer.innerHTML = `
                //     <div class="card">
                //         <div class="card-body">
                //             <h5 class="card-title">学科检测统计</h5>
                //             <p class="card-text">检测状态: ${stats.active ? '活跃' : '未活跃'}</p>
                //             <p class="card-text">功能状态: ${stats.initialized ? '已初始化' : '未初始化'}</p>
                //             <p class="card-text">历史记录数: ${historyCount}</p>
                //             <p class="card-text">学科时长记录: ${durationsCount} 个学科</p>
                //             ${stats.last_detected ? `
                //                 <div class="mt-2">
                //                     <strong>最后检测:</strong><br>
                //                     学科: ${stats.last_detected.subject}<br>
                //                     置信度: ${(stats.last_detected.confidence * 100).toFixed(1)}%<br>
                //                     时间: ${new Date(stats.last_detected.timestamp).toLocaleString()}
                //                 </div>
                //             ` : ''}
                //         </div>
                //     </div>
                // `;


                subjectStatsContainer.innerHTML = `
                    <div class="card">
                        <div class="card-body">
                            ${stats.last_detected ? `
                                <div class="mt-2">
                                    <strong>最后检测:</strong><br>
                                    学科: ${stats.last_detected.subject}<br>
                                    置信度: ${(stats.last_detected.confidence * 100).toFixed(1)}%<br>
                                    时间: ${new Date(stats.last_detected.timestamp).toLocaleString()}
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            } else {
                subjectStatsContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 获取统计失败: ${stats.message || '未知错误'}
                    </div>
                `;
            }
        }

        subjectCaptureBtn.addEventListener('click', async () => {
            try {
                console.log("开始检测学科拍照...")
                // 创建canvas并绘制当前视频帧
                const canvas = document.createElement('canvas');
                canvas.width = subjectWebcam.videoWidth;
                canvas.height = subjectWebcam.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(subjectWebcam, 0, 0);

                // 转换为blob
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, `capture_${Date.now()}.jpg`);

                    // 显示加载状态
                    subjectResultContainer.innerHTML = `
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">正在检测...</span>
                                
                            </div>
                            <p class="mt-2">正在检测学科...</p>
                        </div>
                    `;

                    try {
                        const response = await fetch('/api/subject/detect', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();
                        displaySubjectResult(result);

                        // 更新统计
                        updateSubjectStats();
                    } catch (err) {
                        console.error('检测学科失败:', err);
                        subjectResultContainer.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> 检测失败: ${err.message}
                            </div>
                        `;
                    }
                }, 'image/jpeg', 0.9);
            } catch (err) {
                console.error('拍照失败:', err);
                subjectResultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 拍照失败: ${err.message}
                    </div>
                `;
            }
        });

        //  // 初始化学科检测功能
        //  async function initSubjectDetection() {
        //     try {
        //         // 获取摄像头权限
        //         taskStream = await navigator.mediaDevices.getUserMedia({
        //             video: {
        //                 width: { ideal: 640 },
        //                 height: { ideal: 480 }
        //             }
        //         });
        //         taskWebcam.srcObject = taskStream;

        //         console.log('拍照生成任务功能初始化完成');
        //     } catch (err) {
        //         console.error('初始拍照生成任务测功能失败:', err);
        //         subjectStatusText.textContent = '无法访问摄像头，请检查权限设置';
        //         subjectStatusIndicator.className = 'alert alert-danger';
        //     }
        // }

        //利用拍照生成任务
        taskGenerateBtn.addEventListener('click', async () => {
            try {
                console.log("开始拍照生成任务...")
                // 创建canvas并绘制当前视频帧
                const canvas = document.createElement('canvas');
                canvas.width = subjectWebcam.videoWidth;
                canvas.height = subjectWebcam.videoHeight;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(subjectWebcam, 0, 0);
                console.log("开始拍照生成任务1...")   
                // 转换为blob
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, `capture_${Date.now()}.jpg`);


                    try {
                        const response = await fetch('/api/task/detect', {
                            method: 'POST',
                            body: formData
                        });

                        const result = await response.json();
                        console.log("result for task generate:",result)
                        console.log(result.tasks[0].description)
                        generate_task=result.tasks[0].description

                        const selectedTaskName = taskNameSelect.value;
                        const subTaskContent = generate_task;
                        if (selectedTaskName && subTaskContent) {
                            const taskPlanTable = document.getElementById('taskPlanTable');
                            const rows = taskPlanTable.getElementsByTagName('tr');

                            for (let i = 0; i < rows.length; i++) {
                                const taskNameCell = rows[i].cells[0];
                                if (taskNameCell.textContent === selectedTaskName) {
                                    const subTaskCell = rows[i].cells[2];
                                    const newTag = document.createElement('div');
                                    newTag.className = 'tag';
                                    newTag.innerHTML = `${subTaskContent} (来源: 拍照生成)<span class="tag-delete" onclick="removeSubTask(this, '${selectedTaskName}', '${subTaskContent}', '语音输入')">×</span>`;
                                    subTaskCell.appendChild(newTag);
                                    break;
                                }
                            }

    
                        } else {
                            showMessage('请选择任务并输入子任务内容', 'error');
                        }

                    } catch (err) {
                        console.error('拍照生成任务失败:', err);
    
                    }
                }, 'image/jpeg', 0.9);
            } catch (err) {
                console.error('拍照生成任务失败:', err);
                subjectResultContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> 拍照生成任务失败: ${err.message}
                    </div>
                `;
            }
        });
        //每 30 秒触发一次按钮点击事件
        // setInterval(() => {
        //     subjectCaptureBtn.click();
        // }, 3000000);
                // 添加子任务
            function addSubTask() {

        }

        checkTaskButton.addEventListener('click', async () => {
            try {
                 // 显示消息框
                envCheckingModal.style.display = 'block';
                // 模拟点击开启摄像头按钮
                await simulateStartCamera();
                // 模拟点击拍照按钮
                await simulateCapture();
                // 模拟点击分析按钮
                await simulateAnalyze();

                const response = await fetch('/api/check_task_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });
                
                // 读取响应体为文本
                const responseText = await response.text();

                // console.log('服务器桌面检测接口 /api/check_task_data 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });

        checkEnvButton.addEventListener('click', async () => {
            try {
  
                const response = await fetch('/api/check_env_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    // body: JSON.stringify({
                    //     image: capturedImage
                    // })
                });
                
                // 读取响应体为文本
                const responseText = await response.text();

                console.log('服务器桌面检测接口 /api/check_env_data 响应内容:', responseText);


            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        });

        setInterval(() => {
            getTargetSubjet();
        }, 3000);
        async function getTargetSubjet() {
            try {
  
                const response = await fetch('/api/getTargetSubjet', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
     
                });

                const result = await response.json();
   
                targetTaskName=result.targetSubjetName

                console.log('服务器桌面检测接口 /api/getTargetSubjet 响应内容:', result);
                showSubjectCheckModal();

            } catch (error) {
                console.error('任务确认执行过程中出错:', error);
                showNotification(`任务确认执行过程中出错: ${error.message}`, 5000);
            }
        }


                // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSubjectDetection();
            connectService();
            setTimeout(function() {
                console.log('🎵 页面加载完成，播放欢迎语音');
                showMessage('🎵 页面加载完成，播放欢迎语音', 'success');

                // 发送TTS请求到后端
                fetch('/api/tts_play', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: '我们一起开始今日作业吧'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('页面加载TTS请求响应:', data);
                    if (data.success) {
                        showMessage('🎵 欢迎语音播放请求已发送', 'success');
                    } else {
                        console.error('页面加载TTS请求失败:', data.message);
                    }
                })
                .catch(error => {
                    console.error('页面加载TTS请求错误:', error);
                });
            }, 2000); // 延迟2秒播放，确保页面完全加载
            
        });


            
        

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (isRecording) {
                stopRecognition();
            }
            if (socket) {
                socket.disconnect();
            }
        });


        async function simulateStartCamera() {
            if (startCameraButton.disabled) {
                return;
            }
            await startCameraButton.click();
            // 等待摄像头启动，可根据实际情况调整等待时间
            await new Promise(resolve => setTimeout(resolve, 1000)); 
        }

        async function simulateCapture() {
            if (captureButton.disabled) {
                throw new Error('拍照按钮不可用，请先启动摄像头');
            }
            await captureButton.click();
            // 等待拍照完成，可根据实际情况调整等待时间
            await new Promise(resolve => setTimeout(resolve, 1000)); 
        }

        async function simulateAnalyze() {
            if (analyzeButton.disabled) {
                throw new Error('分析按钮不可用，请先拍照');
            }
            await analyzeButton.click();
        }

        // 摄像头相关函数
        startCameraButton.addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                video.srcObject = stream;
                captureButton.disabled = false;
                startCameraButton.disabled = true;
            } catch (error) {
                console.error('无法访问摄像头:', error);
                showNotification(`无法访问摄像头: ${error.message}`, 5000);
            }
        });

        captureButton.addEventListener('click', () => {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            capturedImage = canvas.toDataURL('image/jpeg', 0.8);
            document.getElementById('original-image').src = capturedImage;
            document.getElementById('original-image').style.display = 'block';
            document.querySelector('#original-image-container .placeholder-text').style.display = 'none';
            analyzeButton.disabled = false;
            showNotification('拍照成功！', 3000);
        });

        analyzeButton.addEventListener('click', async () => {
            if (!capturedImage) {
                showNotification('请先拍照', 3000);
                return;
            }



            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        image: capturedImage
                    })
                });

                // 直接解析响应为 JSON 对象
                const analysisResult = await response.json();

                console.log('服务器桌面检测接口 /api/analyze 响应内容:', analysisResult);
                console.log('整洁度评分: analysisResult.tidiness_score')
                console.log(analysisResult.tidiness_score)

                console.log('整洁等级: ${analysisResult.tidiness_level}')
                console.log(analysisResult.tidiness_level)   
                
                console.log('整洁建议: ${analysisResult.tidiness_suggestion}')
                console.log(analysisResult.tidiness_suggestion)

      

                // document.getElementById('annotated-image').src = analysisResult.annotated_image_path;
                // document.getElementById('annotated-image').style.display = 'block';
                // document.querySelector('#annotated-image-container .placeholder-text').style.display = 'none';
                // document.getElementById('items-text').textContent = `检测到的物品: ${JSON.stringify(analysisResult.items, null, 2)}`;


                // 将整洁度评分和等级赋值到 deskTidiness 文本框
                const deskTidinessTextarea = document.getElementById('deskTidiness');
                deskTidinessTextarea.value = `整洁度评分: ${analysisResult.tidiness_score}   整洁等级: ${analysisResult.tidiness_level}`;

                // 将建议赋值到 deskSuggestion 文本框
                const deskSuggestionTextarea = document.getElementById('deskSuggestion');
                deskSuggestionTextarea.value = analysisResult.tidiness_suggestion;

                console.log('分析完成');
                 // 隐藏消息框
                envCheckingModal.style.display = 'none';
            } catch (error) {
                console.error('分析图像时出错:', error);
                console.log(`分析失败`);
            }
        });

        function showNotification(message, duration) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.style.display = 'block';
            setTimeout(() => {
                notification.style.display = 'none';
            }, duration);
        }

        // 原有语音识别相关代码保持不变
        // DOM元素
        const connectBtn = document.getElementById('connectBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const statusMessage = document.getElementById('statusMessage');
        const micIndicator = document.getElementById('micIndicator');
        const resultsList = document.getElementById('resultsList');
        const connectionStatus = document.getElementById('connectionStatus');
        const silenceStatus = document.getElementById('silenceStatus');
        const silenceCountdown = document.getElementById('silenceCountdown');
        const analyzingModal = document.getElementById('analyzingModal');
        const analysisResultModal = document.getElementById('analysisResultModal');
        const resultContent = document.getElementById('resultContent');

        // 显示正在分析中弹窗
        function showAnalyzingModal() {
            analyzingModal.style.display = 'block';
        }

        // 隐藏正在分析中弹窗
        function hideAnalyzingModal() {
            analyzingModal.style.display = 'none';
        }

        // 显示分析结果弹窗
        function showAnalysisResultModal(result) {
            resultContent.textContent = result;
            analysisResultModal.style.display = 'block';
        }

        // 关闭分析结果弹窗
        function closeAnalysisResultModal() {
            analysisResultModal.style.display = 'none';
        }
        // Socket事件监听
            // 监听 current_subject 事件
        socket.on('current_subject', function(data) {
            currentSubject = data;
        });

        socket.on('processNum', function(data) {
            console.log("获取的processNum")
            processNum = data;
            // 根据 processNum 的值调用对应的函数
            console.log("获取的processNum",processNum)
            switch (processNum) {
                case 0:
                    console.log("传输0，")
                    break;
                case 1:
  
                    break;
                case 2:
                    console.log("传输2，点击确认任务")
                    checkTaskButton.click();
                    break;
                case 3:
                    console.log("传输3，点击桌面清理")
                    checkEnvButton.click();
                    break;
                case 4:
                    // handleProcessNum4();
                    break;
                case 5:
                    finishTaskBtn.click();
                    break;
                case 6:
                    summaryBtn().click();
                    break;
                default:
                    console.warn('收到的 processNum 不在 0 到 6 的范围内:', processNum);
            }
        });

        // 监听 target_task_name 事件
        // socket.on('target_task_name', function(data) {
        //     targetTaskName = data;
        // });
        socket.on('connect', function() {
            console.log('已连接到服务器');
            updateConnectionStatus(true);
            updateStatus('已连接到服务器', 'connected');
            updateStatusMessage('请点击"连接服务"按钮连接到语音识别服务');
        });

        socket.on('disconnect', function() {
            console.log('与服务器断开连接');
            updateConnectionStatus(false);
            updateStatus('与服务器断开连接', 'inactive');
            updateStatusMessage('正在尝试重新连接...');
            resetButtons();
        });

        socket.on('status', function(data) {
            console.log('状态更新:', data);
            updateStatusMessage(data.message);
        });

        socket.on('error', function(data) {
            console.error('错误:', data.message);
            showMessage(data.message, 'error');
            resetButtons();
        });

        
        socket.on('targetSubjetName', function(data) {
            targetTaskName = data;
        });
        socket.on('recognition_started', function(data) {
            console.log('语音识别已开始:', data.message);
            isRecording = true;
            updateStatus('正在录音...', 'recording');
            updateStatusMessage('请说话，系统正在识别您的语音');
            micIndicator.classList.add('recording');

            startBtn.disabled = true;
            stopBtn.disabled = false;
            connectBtn.disabled = true;

            // 显示静音检测状态
            if (data.silence_timeout) {
                silenceStatus.style.display = 'block';
                startSilenceCountdown(data.silence_timeout);
            }

            showMessage('语音识别已开始，将在1.5秒静音后自动停止', 'success');
        });

        socket.on('recognition_stopped', function(data) {
            console.log('语音识别已停止:', data.message);
            isRecording = false;
            updateStatus('识别已停止', 'connected');
            updateStatusMessage('语音识别已停止，可以重新开始');
            micIndicator.classList.remove('recording');

            startBtn.disabled = false;
            stopBtn.disabled = true;
            connectBtn.disabled = false;

            showMessage('语音识别已停止', 'success');
        });

        socket.on('recognition_result', function(data) {
            console.log('识别结果:', data);
            addResult(data.text, data.is_final, data.timestamp);
        });

        socket.on('task_created', function(data) {
            console.log('任务创建结果:', data);
            showMessage('语音大模型已处理，任务已创建！', 'success');

            // 添加任务创建结果到显示区域
            addTaskResult(data.voice_result, data.daily_task, data.timestamp);
        });

        socket.on('recognition_restarted', function(data) {
            console.log('语音识别已重启:', data.message);
            isRecording = true;
            updateStatus('正在录音...', 'recording');
            updateStatusMessage('语音识别已自动重启，请说话');
            micIndicator.classList.add('recording');

            // 在自动模式下不需要手动控制按钮
            startBtn.disabled = true;
            stopBtn.disabled = true;
            connectBtn.disabled = true;

            // 显示静音检测状态
            if (data.silence_timeout) {
                silenceStatus.style.display = 'block';
                startSilenceCountdown(data.silence_timeout);
            }

            showMessage('语音识别已自动重启', 'success');
        });

        socket.on('connection_confirmed', function(data) {
            console.log('✅ 后端连接确认:', data.message);
            showMessage('音频传输通道已建立', 'success');
        });
        // 显示科目检测弹窗
        function showSubjectCheckModal() {
            const currentSubjectText = document.getElementById('currentSubjectText');
            const targetTaskNameText = document.getElementById('targetTaskNameText');
            currentSubjectText.textContent = `检测的科目: ${currentSubject}`;
            targetTaskNameText.textContent = `计划的科目: ${targetTaskName}`;
            document.getElementById('subjectCheckModal').style.display = 'none';
        }

           // 关闭科目检测弹窗
        function closeSubjectCheckModal() {
            document.getElementById('subjectCheckModal').style.display = 'none';
        }

        // 连接到语音识别服务
        function connectService() {
            console.log('连接到语音识别服务...');
            updateStatus('正在连接...', 'inactive');
            updateStatusMessage('正在连接到豆包语音识别服务...');

            fetch('/api/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateStatus('服务已连接', 'connected');
                    updateStatusMessage('语音识别服务已连接，正在启动自动语音识别...');
                    // 在自动模式下隐藏手动控制按钮
                    startBtn.style.display = 'none';
                    stopBtn.style.display = 'none';
                    connectBtn.textContent = '🔗 断开服务';
                    connectBtn.onclick = disconnectService;
                    showMessage('成功连接到语音识别服务', 'success');

                    console.log('服务连接成功，准备启动自动语音识别...');

                    // 自动启动语音识别
                    setTimeout(() => {
                        console.log('开始自动启动语音识别...');
                        autoStartRecognition();
                    }, 2000);  // 增加延迟到2秒
                } else {
                    updateStatus('连接失败', 'inactive');
                    updateStatusMessage(data.message);
                    showMessage('连接失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('连接错误:', error);
                updateStatus('连接错误', 'inactive');
                updateStatusMessage('连接时发生错误');
                showMessage('连接时发生错误: ' + error.message, 'error');
            });
        }

                // 自动启动语音识别（用于自动模式）
            function autoStartRecognition() {
            console.log('🎤 开始自动启动语音识别...');

            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                console.error('❌ 浏览器不支持音频API');
                showMessage('浏览器不支持音频API，请使用现代浏览器', 'error');
                return;
            }

            // 请求麦克风权限并开始录音
            navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
            .then(stream => {
                console.log('✅ 获得麦克风权限，开始设置音频处理...');

                // 先设置音频处理
                setupAudioProcessing(stream);

                // 等待音频处理设置完成后再启用录音
                setTimeout(() => {
                    // 重要：设置录音状态为true，确保音频数据会被发送
                    isRecording = true;

                    console.log('✅ 音频处理已设置完成，录音已启动');
                    console.log('📊 当前状态 - 录音:', isRecording, 'Socket连接:', socket.connected);

                    // 更新状态
                    updateStatus('自动语音识别已启动', 'recording');
                    updateStatusMessage('系统正在持续监听语音输入，基于内容变化检测2秒静音');
                    micIndicator.classList.add('recording');

                    // 发送测试消息确认连接
                    if (socket && socket.connected) {
                        socket.emit('test_connection', {message: '前端音频系统已就绪'});
                        console.log('� 已发送测试连接消息');
                    } else {
                        console.warn('⚠️ Socket未连接，无法发送测试消息');
                    }

                    // 显示成功消息
                    showMessage('自动语音识别已启动，开始监听语音输入', 'success');
                }, 500);  // 等待500ms确保音频处理器已就绪

            })
            .catch(error => {
                console.error('❌ 无法访问麦克风:', error);
                if (error.name === 'NotAllowedError') {
                    showMessage('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问', 'error');
                } else if (error.name === 'NotFoundError') {
                    showMessage('未找到麦克风设备，请检查硬件连接', 'error');
                } else {
                    showMessage('无法访问麦克风: ' + error.message, 'error');
                }
            });
        }
        // 断开语音识别服务
        function disconnectService() {
            console.log('断开语音识别服务...');

            // 如果正在录音，先停止
            if (isRecording) {
                stopRecognition();
            }

            fetch('/api/disconnect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                updateStatus('服务已断开', 'inactive');
                updateStatusMessage('语音识别服务已断开');
                resetButtons();
                showMessage('已断开语音识别服务', 'success');
            })
            .catch(error => {
                console.error('断开连接错误:', error);
                showMessage('断开连接时发生错误: ' + error.message, 'error');
            });
        }

        // 开始语音识别
        function startRecognition() {
            console.log('开始语音识别...');

            // 请求麦克风权限并开始录音
            navigator.mediaDevices.getUserMedia({
                audio: {
                    sampleRate: 16000,
                    channelCount: 1,
                    echoCancellation: true,
                    noiseSuppression: true
                }
            })
            .then(stream => {
                console.log('获得麦克风权限');

                // 通过Socket通知服务器开始识别
                socket.emit('start_recognition');

                // 设置音频处理
                setupAudioProcessing(stream);

            })
            .catch(error => {
                console.error('无法访问麦克风:', error);
                showMessage('无法访问麦克风，请检查权限设置', 'error');
            });
        }

        // 停止语音识别
        function stopRecognition() {
            console.log('停止语音识别...');

            // 停止音频处理
            stopAudioProcessing();

            // 通过Socket通知服务器停止识别
            socket.emit('stop_recognition');
        }

        // 设置音频处理
        function setupAudioProcessing(stream) {
            try {
                // 创建音频上下文
                audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });

                // 创建音频源
                microphone = audioContext.createMediaStreamSource(stream);

                // 创建脚本处理器
                processor = audioContext.createScriptProcessor(4096, 1, 1);

                processor.onaudioprocess = function(event) {
                    if (isRecording) {
                        const inputBuffer = event.inputBuffer;
                        const inputData = inputBuffer.getChannelData(0);

                        // 转换为16位PCM
                        const pcmData = convertFloat32ToInt16(inputData);

                        // 转换为base64并发送
                        const base64Data = arrayBufferToBase64(pcmData.buffer);
                        socket.emit('audio_data', { audio: base64Data });
                    }
                };

                // 连接音频节点
                microphone.connect(processor);
                processor.connect(audioContext.destination);

                console.log('音频处理设置完成');

            } catch (error) {
                console.error('设置音频处理时出错:', error);
                showMessage('设置音频处理时出错: ' + error.message, 'error');
            }
        }

        // 停止音频处理
        function stopAudioProcessing() {
            try {
                if (processor) {
                    processor.disconnect();
                    processor = null;
                }

                if (microphone) {
                    microphone.disconnect();
                    microphone = null;
                }

                if (audioContext) {
                    audioContext.close();
                    audioContext = null;
                }

                console.log('音频处理已停止');

            } catch (error) {
                console.error('停止音频处理时出错:', error);
            }
        }

        // 转换Float32数组为Int16数组
        function convertFloat32ToInt16(float32Array) {
            const int16Array = new Int16Array(float32Array.length);
            for (let i = 0; i < float32Array.length; i++) {
                const sample = Math.max(-1, Math.min(1, float32Array[i]));
                int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
            }
            return int16Array;
        }

        // 将ArrayBuffer转换为base64
        function arrayBufferToBase64(buffer) {
            let binary = '';
            const bytes = new Uint8Array(buffer);
            const len = bytes.byteLength;
            for (let i = 0; i < len; i++) {
                binary += String.fromCharCode(bytes[i]);
            }
            return window.btoa(binary);
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            if (connected) {
                connectionStatus.textContent = '🟢 已连接';
                connectionStatus.className = 'connection-status connected';
            } else {
                connectionStatus.textContent = '🔴 未连接';
                connectionStatus.className = 'connection-status disconnected';
            }
        }

        // 更新状态
        function updateStatus(message, type) {
            status.textContent = message;
            status.className = 'status ' + type;
        }

        // 更新状态消息
        function updateStatusMessage(message) {
            statusMessage.textContent = message;
        }

        document.getElementById('submitData').addEventListener('click', async () => {
            const teacherDailyTask = document.getElementById('teacherDailyTask').value;
            const yesterdayFeedback = document.getElementById('yesterdayFeedback').value;
            console.log('teacherDailyTask:', teacherDailyTask);
            console.log('yesterdayFeedback:', yesterdayFeedback);
            // 显示加载提示
            const taskPlanLoading = document.getElementById('taskPlanLoading');
            taskPlanLoading.style.display = 'block';

            try {
                const response = await fetch('/api/submit_task_data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        teacher_daily_task: teacherDailyTask,
                        yesterday_feedback: yesterdayFeedback
                    })
                });

                if (!response.ok) {
                    console.log('response generate fail:');
                    throw new Error('提交数据失败');
                }

                const result = await response.json();
                console.log('提交结果:', result);
            } catch (error) {
                console.error('提交数据出错:', error);
            }
        });


  
 
        summaryBtn.addEventListener('click', async () => {
             // 显示正在分析中弹窗
             showAnalyzingModal();
            try {
                const response = await fetch('/api/getSummary', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },

                });


                const result = await response.json();
                markdownText=result.aiAnalysis.analysis
                const html = marked.parse(markdownText);
                const container = document.getElementById('markdown-container');
                container.innerHTML = html;   
                console.log('总结汇报结果:', result);
                console.log('总结汇报结果aiAnalysis:', result.aiAnalysis);
                console.log('总结汇报结果summary:', result.summary);
                // 隐藏正在分析中弹窗
                hideAnalyzingModal();

                // 显示分析结果弹窗
                if (result.success) {
                    showAnalysisResultModal(
                    `分析: ${result.aiAnalysis.analysis} \n

                    总结: \n
                        作业平均评分： ${result.summary.average_score} \n
                        完成作业数量： ${result.summary.completed_homework} \n
                        总共作业数量： ${result.summary.total_homework} \n
                        完成作业率： ${result.summary.completion_rate} \n
                    `);
                } else {
                    showAnalysisResultModal(`检测失败`);
                }

            } catch (error) {
                console.error('提交数据出错:', error);
            }
        });





        // 重置按钮状态
        function resetButtons() {
            startBtn.disabled = true;
            stopBtn.disabled = true;
            connectBtn.disabled = false;
            connectBtn.textContent = '🔗 连接服务';
            connectBtn.onclick = connectService;
            isRecording = false;
            micIndicator.classList.remove('recording');
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
            messageDiv.textContent = message;

            // 插入到状态卡片后面
            const statusCard = document.querySelector('.status-card');
            statusCard.parentNode.insertBefore(messageDiv, statusCard.nextSibling);

            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        // 添加识别结果
        function addResult(text, isFinal, timestamp) {
            // console.log("语音有结果了:",text)
            requestAnimationFrame(() => {
            // 移除空状态
            const emptyState = resultsList.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            // 创建结果项
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item' + (isFinal ? ' final' : '');

            const resultText = document.createElement('div');
            resultText.className = 'result-text';
            resultText.textContent = text;

            const resultMeta = document.createElement('div');
            resultMeta.className = 'result-meta';

            const timeSpan = document.createElement('span');
            const date = new Date(timestamp);
            timeSpan.textContent = date.toLocaleTimeString();

            const typeSpan = document.createElement('span');
            typeSpan.textContent = isFinal ? '最终结果' : '临时结果';
            typeSpan.style.fontWeight = isFinal ? 'bold' : 'normal';
            typeSpan.style.color = isFinal ? '#ffffff' : '#007bff';

            resultMeta.appendChild(timeSpan);
            resultMeta.appendChild(typeSpan);

            resultItem.appendChild(resultText);
            resultItem.appendChild(resultMeta);

            // 插入到列表顶部
            resultsList.insertBefore(resultItem, resultsList.firstChild);

            if(isFinal){
                //将最终文本交给后端进行二分判断
                console.log("最终传给后端判断的文本:",text)

            }

             // 如果处于子任务语音输入状态，将结果输入到新增子任务的文本框中
            if (isSubTaskVoiceInput && isFinal) {
                    newSubTaskInput.value = text;
                    console.log("语音有结果了:",text)
                    // 重置状态
                    isSubTaskVoiceInput = false;
            }

            // 限制显示的结果数量
            const maxResults = 50;
            const results = resultsList.querySelectorAll('.result-item');
            if (results.length > maxResults) {
                for (let i = maxResults; i < results.length; i++) {
                    results[i].remove();
                }
            }

            // 滚动到顶部显示最新结果
            resultsList.scrollTop = 0;
        });
        }

        // 添加任务创建结果
        function addTaskResult(voiceResult, dailyTask, timestamp) {
            // 移除空状态
            const emptyState = resultsList.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            // 创建任务结果项
            const taskItem = document.createElement('div');
            taskItem.className = 'result-item task-result';
            taskItem.style.borderLeftColor = '#ffc107';
            taskItem.style.background = '#fff3cd';

            const taskHeader = document.createElement('div');
            taskHeader.className = 'result-text';
            taskHeader.style.fontWeight = 'bold';
            taskHeader.style.color = '#856404';
            taskHeader.textContent = '🎯 任务已创建';

            const voiceResultDiv = document.createElement('div');
            voiceResultDiv.style.marginTop = '10px';
            voiceResultDiv.style.fontSize = '0.95em';
            voiceResultDiv.innerHTML = `<strong>语音大模型结果:</strong> ${voiceResult}`;

            const taskDiv = document.createElement('div');
            taskDiv.style.marginTop = '8px';
            taskDiv.style.fontSize = '0.9em';
            taskDiv.style.color = '#666';
            taskDiv.innerHTML = `<strong>当日任务:</strong> ${dailyTask.substring(0, 100)}${dailyTask.length > 100 ? '...' : ''}`;

            const resultMeta = document.createElement('div');
            resultMeta.className = 'result-meta';

            const timeSpan = document.createElement('span');
            const date = new Date(timestamp);
            timeSpan.textContent = date.toLocaleTimeString();

            const typeSpan = document.createElement('span');
            typeSpan.textContent = '任务创建';
            typeSpan.style.fontWeight = 'bold';
            typeSpan.style.color = '#ffc107';

            resultMeta.appendChild(timeSpan);
            resultMeta.appendChild(typeSpan);

            taskItem.appendChild(taskHeader);
            taskItem.appendChild(voiceResultDiv);
            taskItem.appendChild(taskDiv);
            taskItem.appendChild(resultMeta);

            // 插入到列表顶部
            resultsList.insertBefore(taskItem, resultsList.firstChild);

            // 滚动到顶部显示最新结果
            resultsList.scrollTop = 0;
        }

        // 清空结果
        function clearResults() {
            resultsList.innerHTML = '<div class="empty-state">暂无识别结果，请开始语音识别...</div>';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化AI Child语音识别界面');
            updateConnectionStatus(false);
            resetButtons();
            // 初始化学科检测功能
            initSubjectDetection();
            // 显示科目检测弹窗
            showSubjectCheckModal();    
            
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (isRecording) {
                stopRecognition();
            }
            if (socket) {
                socket.disconnect();
            }
        });

        //  // 新增显示任务计划的函数
        //  function showTaskPlan(taskPlan) {
        //     const taskPlanTable = document.getElementById('taskPlanTable');
        //     let tableHtml = '<table class="task-table">';
        //     tableHtml += '<thead><tr>';
        //     tableHtml += '<th>任务名称</th><th>时段</th><th>子任务</th><th>定制</th><th>难点</th><th>方案</th><th>执行信心指数</th>';
        //     tableHtml += '</tr></thead><tbody>';

        //     taskPlan.forEach(task => {
        //         tableHtml += '<tr>';
        //         tableHtml += `<td>${task['任务名称']}</td>`;
        //         tableHtml += `<td>${task['时段']}</td>`;
        //         tableHtml += '<td>';
        //         task['子任务'].forEach(subTask => {
        //             tableHtml += `<div class="tag">${subTask['任务内容']} (来源: ${subTask['来源']})<span class="tag-delete" onclick="removeSubTask(this)">×</span></div>`;
        //         });
        //         tableHtml += '</td>';
        //         tableHtml += `<td>${task['定制']}</td>`;
        //         tableHtml += `<td>${task['难点']}</td>`;
        //         tableHtml += `<td>${task['方案']}</td>`;
        //         tableHtml += `<td>${task['执行信心指数']}</td>`;
        //         tableHtml += '</tr>';
        //     });

        //     tableHtml += '</tbody></table>';
        //     taskPlanTable.innerHTML = tableHtml;
        // }

        // function removeSubTask(deleteButton) {
        //     const tag = deleteButton.parentNode;
        //     console.log("删除子任务")
        //     tag.remove();
        // }


        // 存储已删除的子任务
        let deletedSubtasks = [];

        let speechRecognition;
        const taskNameSelect = document.getElementById('taskNameSelect');
        const startVoiceInput = document.getElementById('startVoiceInput');
        const newSubTaskInput = document.getElementById('newSubTaskInput');
        const addSubTaskBtn = document.getElementById('addSubTaskBtn');

        // 填充任务名称下拉框
        function populateTaskNameSelect(taskPlan) {
            taskNameSelect.innerHTML = '<option value="">请选择任务</option>';
            taskPlan.forEach(task => {
                const option = document.createElement('option');
                option.value = task['任务名称'];
                option.textContent = task['任务名称'];
                taskNameSelect.appendChild(option);
            });
        }

        // 添加子任务
        function addSubTask() {
            const selectedTaskName = taskNameSelect.value;
            const subTaskContent = newSubTaskInput.value;
            if (selectedTaskName && subTaskContent) {
                const taskPlanTable = document.getElementById('taskPlanTable');
                const rows = taskPlanTable.getElementsByTagName('tr');

                for (let i = 0; i < rows.length; i++) {
                    const taskNameCell = rows[i].cells[0];
                    if (taskNameCell.textContent === selectedTaskName) {
                        const subTaskCell = rows[i].cells[2];
                        const newTag = document.createElement('div');
                        newTag.className = 'tag';
                        newTag.innerHTML = `${subTaskContent} (来源: 语音输入)<span class="tag-delete" onclick="removeSubTask(this, '${selectedTaskName}', '${subTaskContent}', '语音输入')">×</span>`;
                        subTaskCell.appendChild(newTag);
                        break;
                    }
                }

                newSubTaskInput.value = '';
            } else {
                showMessage('请选择任务并输入子任务内容', 'error');
            }
        }

        // 初始化语音识别
        function initSpeechRecognition() {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            if (SpeechRecognition) {
                speechRecognition = new SpeechRecognition();
                speechRecognition.lang = 'zh-CN';
                speechRecognition.interimResults = false; // 只获取最终结果

                speechRecognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript;
                    newSubTaskInput.value = transcript;
                    console.log('语音识别结果:newSubTaskInput.value=', transcript);
                };

                speechRecognition.onerror = function(event) {
                    console.error('语音识别出错:', event.error);
                };
            } else {
                console.error('浏览器不支持语音识别功能');
                showMessage('浏览器不支持语音识别功能，请使用现代浏览器', 'error');
            }
        }
         initSpeechRecognition();

        // 监听语音输入按钮点击事件
        startVoiceInput.addEventListener('click', function() {
            if (speechRecognition) {
                isSubTaskVoiceInput = true;
                speechRecognition.start();
            }
        });

        // 监听添加子任务按钮点击事件
        addSubTaskBtn.addEventListener('click', addSubTask);


        // 新增时间格式函数，将时间字符串转换为 HH:mm 格式
        function formatTime(timeStr) {
            const date = new Date(`1970-01-01T${timeStr}`);
            return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
        }

        // 新增显示任务计划的函数
        function showTaskPlan(taskPlan) {
            const taskPlanTable = document.getElementById('taskPlanTable');
            let tableHtml = '<tbody>';

            taskPlan.forEach(task => {
                tableHtml += '<tr>';
                tableHtml += `<td>${task['任务名称']}</td>`;
                // 将时间转换为可编辑的输入框
                const [startTime, endTime] = task['时段'].split('-');
                tableHtml += `<td>
                    <input type="time" value="${formatTime(startTime)}" step="60" onchange="updateTaskTime(this, '${task['任务名称']}', 'start')"> - 
                    <input type="time" value="${formatTime(endTime)}" step="60" onchange="updateTaskTime(this, '${task['任务名称']}', 'end')">
                </td>`;
                tableHtml += '<td>';
                task['子任务'].forEach(subTask => {
                    tableHtml += `<div class="tag">${subTask['任务内容']} (来源: ${subTask['来源']})<span class="tag-delete" onclick="removeSubTask(this, '${task['任务名称']}', '${subTask['任务内容']}', '${subTask['来源']}')">×</span></div>`;
                });
                tableHtml += '</td>';
                tableHtml += `<td>${task['定制']}</td>`;
                tableHtml += `<td>${task['难点']}</td>`;
                tableHtml += `<td>${task['方案']}</td>`;
                tableHtml += `<td>${task['执行信心指数']}</td>`;
                tableHtml += '</tr>';
            });

            tableHtml += '</tbody>';
            taskPlanTable.innerHTML = tableHtml;

            // 填充任务名称下拉框
            populateTaskNameSelect(taskPlan);
        }

        // 新增函数，用于处理时间更新
        function updateTaskTime(input, taskName, type) {
            const newTime = input.value;
            console.log(`任务 ${taskName} 的 ${type === 'start' ? '开始' : '结束'} 时间更新为 ${newTime}`);
            // 这里可以添加发送更新请求到后端的逻辑
        }

        // 删除子任务函数
        function removeSubTask(deleteButton, taskName, subTaskContent, subTaskSource) {
            const tag = deleteButton.parentNode;
            tag.remove();

            // 保存已删除的子任务
            deletedSubtasks.push({
                taskName: taskName,
                subTaskContent: subTaskContent,
                subTaskSource: subTaskSource
            });

            // 渲染已删除子任务表格
            renderDeletedSubtasks();
        }

        // 撤销删除子任务函数
        function undoRemoveSubTask(index) {
            const deletedSubtask = deletedSubtasks[index];
            const taskPlanTable = document.getElementById('taskPlanTable');
            const rows = taskPlanTable.getElementsByTagName('tr');

            for (let i = 0; i < rows.length; i++) {
                const taskNameCell = rows[i].cells[0];
                if (taskNameCell.textContent === deletedSubtask.taskName) {
                    const subTaskCell = rows[i].cells[2];
                    const newTag = document.createElement('div');
                    newTag.className = 'tag';
                    newTag.innerHTML = `${deletedSubtask.subTaskContent} (来源: ${deletedSubtask.subTaskSource})<span class="tag-delete" onclick="removeSubTask(this, '${deletedSubtask.taskName}', '${deletedSubtask.subTaskContent}', '${deletedSubtask.subTaskSource}')">×</span>`;
                    subTaskCell.appendChild(newTag);
                    break;
                }
            }

            // 从已删除子任务数组中移除
            deletedSubtasks.splice(index, 1);
            // 重新渲染已删除子任务表格
            renderDeletedSubtasks();
        }

        // 渲染已删除子任务表格
        function renderDeletedSubtasks() {
            const deletedSubtasksBody = document.getElementById('deletedSubtasksBody');
            let tableHtml = '';

            deletedSubtasks.forEach((deletedSubtask, index) => {
                tableHtml += '<tr>';
                tableHtml += `<td>${deletedSubtask.taskName}</td>`;
                tableHtml += `<td>${deletedSubtask.subTaskContent}</td>`;
                tableHtml += `<td>${deletedSubtask.subTaskSource}</td>`;
                tableHtml += `<td><button onclick="undoRemoveSubTask(${index})">撤销</button></td>`;
                tableHtml += '</tr>';
            });

            deletedSubtasksBody.innerHTML = tableHtml;
        }
        // 假设服务器通过 Socket 发送任务计划数据
        socket.on('task_plan', function(data) {
            console.log('收到任务计划:', data);
            showTaskPlan(data);

            // 隐藏加载提示
            const taskPlanLoading = document.getElementById('taskPlanLoading');
            taskPlanLoading.style.display = 'none';
        });

// 音频播放功能
        function playAudioFromBase64(audioBase64, text, format = 'mp3') {
            try {
                console.log('🎵 开始播放音频:', text, '格式:', format);
                console.log('🎵 Base64数据长度:', audioBase64.length);

                // 验证Base64数据
                if (!audioBase64 || audioBase64.length === 0) {
                    throw new Error('Base64音频数据为空');
                }

                // 停止当前播放的音频
                if (currentAudio) {
                    console.log('🎵 停止当前播放的音频');
                    currentAudio.pause();
                    currentAudio = null;
                }

                // 根据格式确定MIME类型
                let mimeType = 'audio/mpeg'; // 默认MP3
                if (format === 'wav') {
                    mimeType = 'audio/wav';
                } else if (format === 'mp3') {
                    mimeType = 'audio/mpeg';
                }

                console.log('🎵 使用MIME类型:', mimeType);

                // 创建音频数据URL
                const audioBlob = base64ToBlob(audioBase64, mimeType);
                const audioUrl = URL.createObjectURL(audioBlob);

                console.log('🎵 音频Blob大小:', audioBlob.size, 'bytes');
                console.log('🎵 音频URL:', audioUrl);

                // 验证Blob大小
                if (audioBlob.size === 0) {
                    throw new Error('生成的音频Blob为空');
                }

                // 创建音频元素
                currentAudio = new Audio(audioUrl);

                // 设置音频事件
                currentAudio.onloadstart = function() {
                    console.log('音频开始加载');
                    showAudioPlayer(text, '正在加载...');
                };

                currentAudio.oncanplay = function() {
                    console.log('音频可以播放');
                    updateAudioPlayerStatus('准备播放');
                };

                currentAudio.onplay = function() {
                    console.log('音频开始播放');
                    updateAudioPlayerStatus('正在播放');
                };

                currentAudio.onerror = function(e) {
                    console.error('音频播放错误:', e);
                    console.error('错误详情:', currentAudio.error);
                    showMessage('音频播放错误: ' + (currentAudio.error ? currentAudio.error.message : '未知错误'), 'error');
                };

                currentAudio.onloadeddata = function() {
                    console.log('音频数据加载完成');
                };

                currentAudio.oncanplaythrough = function() {
                    console.log('音频可以完整播放');
                };

                currentAudio.onended = function() {
                    console.log('音频播放完成');
                    hideAudioPlayer();
                    URL.revokeObjectURL(audioUrl);
                    currentAudio = null;
                };

                currentAudio.onerror = function(e) {
                    console.error('音频播放错误:', e);
                    updateAudioPlayerStatus('播放失败');
                    setTimeout(hideAudioPlayer, 2000);
                };

                // 自动播放
                currentAudio.play().catch(error => {
                    console.error('自动播放失败:', error);
                    updateAudioPlayerStatus('播放失败 - 请手动点击播放');
                });

            } catch (error) {
                console.error('播放音频时出错:', error);
            }
        }

        // Base64转Blob
        function base64ToBlob(base64, mimeType) {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            return new Blob([byteArray], { type: mimeType });
        }

        // 显示音频播放器
        function showAudioPlayer(text, status) {
            const audioPlayer = document.getElementById('audioPlayer');
            const audioText = document.getElementById('audioText');

            audioText.textContent = text;
            audioPlayer.style.display = 'block';

            if (status) {
                updateAudioPlayerStatus(status);
            }
        }

        // 更新音频播放器状态
        function updateAudioPlayerStatus(status) {
            const audioText = document.getElementById('audioText');
            const originalText = audioText.textContent;
            audioText.innerHTML = `${originalText}<br><small style="opacity: 0.7;">${status}</small>`;
        }

        // 隐藏音频播放器
        function hideAudioPlayer() {
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.style.display = 'none';
        }

        // 监听来自服务器的音频播放事件
        socket.on('play_audio', function(data) {
            console.log('🎵 收到音频播放请求:', data.text);
            console.log('🎵 音频数据长度:', data.audio_data ? data.audio_data.length : 0);
            console.log('🎵 音频格式:', data.format || 'mp3');
            console.log('🎵 完整数据:', data);

            // 显示明显的提示
            showMessage('🎵 收到TTS音频播放请求: ' + data.text, 'success');

            // 立即显示音频播放器
            showAudioPlayer(data.text, '正在准备播放...');

            try {
                // 验证音频数据
                if (!data.audio_data || data.audio_data.length === 0) {
                    throw new Error('音频数据为空');
                }

                // 传递格式参数
                playAudioFromBase64(data.audio_data, data.text, data.format || 'mp3');
                showMessage('🎵 正在播放语音: ' + data.text, 'success');
            } catch (error) {
                console.error('🎵 播放音频失败:', error);
                showMessage('🎵 音频播放失败: ' + data.text + ' - ' + error.message, 'error');
                updateAudioPlayerStatus('播放失败: ' + error.message);
            }
        });

        // 监听TTS成功事件
        socket.on('tts_success', function(data) {
            console.log('TTS成功:', data.message);
            showMessage('语音播放成功: ' + data.text, 'success');
        });

        // 监听TTS错误事件
        socket.on('tts_error', function(data) {
            console.error('TTS错误:', data.error);
            showMessage('语音合成失败: ' + data.text, 'error');
        });





    </script>
</body>
</html>
