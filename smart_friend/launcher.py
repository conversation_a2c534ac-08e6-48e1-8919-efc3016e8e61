#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend Application Launcher

This launcher provides a professional interface to start the Smart Friend
application in different modes.

Usage:
    python launcher.py                    # Interactive mode selection
    python launcher.py --mode standard    # Start in standard mode
    python launcher.py --mode openmanus   # Start in OpenManus mode
    python launcher.py --help            # Show help
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def print_banner():
    """Print application banner"""
    print("🚀 Smart Friend - AI Learning Assistant")
    print("=" * 60)
    print("Professional AI-powered educational companion for children")
    print("=" * 60)


def print_mode_info():
    """Print information about available modes"""
    print("\n📋 Available Modes:")
    print()
    print("1. 📋 STANDARD MODE")
    print("   • Core AI services (Doubao, ASR, TTS)")
    print("   • User management and learning analytics")
    print("   • Task planning and daily learning tracking")
    print("   • Voice interaction and multimodal input")
    print("   • Real-time communication via Socket.IO")
    print()
    print("2. 🧠 OPENMANUS ENHANCED MODE")
    print("   • All standard features PLUS:")
    print("   • OpenManus AI orchestration framework")
    print("   • Jina embeddings for semantic understanding")
    print("   • Intent classification and context awareness")
    print("   • Multi-step reasoning and task planning")
    print("   • Intelligent conversation management")
    print("   • Educational content personalization")
    print()


def get_user_choice():
    """Get user's mode choice interactively"""
    print_mode_info()
    
    while True:
        try:
            choice = input("Select mode (1 for Standard, 2 for OpenManus): ").strip()
            
            if choice == "1":
                return "standard"
            elif choice == "2":
                return "openmanus"
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            sys.exit(0)


def start_application(mode: str):
    """Start the application in the specified mode"""
    print(f"\n🚀 Starting Smart Friend in {mode.upper()} mode...")
    
    if mode == "standard":
        script_name = "main_standard.py"
    elif mode == "openmanus":
        script_name = "main_openmanus.py"
    else:
        print(f"❌ Unknown mode: {mode}")
        sys.exit(1)
    
    script_path = project_root / script_name
    
    if not script_path.exists():
        print(f"❌ Script not found: {script_path}")
        sys.exit(1)
    
    try:
        # Start the application
        subprocess.run([sys.executable, str(script_path)], check=True)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Application failed to start: {e}")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


def main():
    """Main launcher function"""
    parser = argparse.ArgumentParser(
        description="Smart Friend Application Launcher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python launcher.py                    # Interactive mode selection
  python launcher.py --mode standard    # Start in standard mode
  python launcher.py --mode openmanus   # Start in OpenManus mode

Modes:
  standard   - Core AI services with basic functionality
  openmanus  - Enhanced with OpenManus AI framework
        """
    )
    
    parser.add_argument(
        "--mode",
        choices=["standard", "openmanus"],
        help="Application mode to start"
    )
    
    args = parser.parse_args()
    
    print_banner()
    
    # Determine mode
    if args.mode:
        mode = args.mode
        print(f"📋 Mode selected: {mode.upper()}")
    else:
        mode = get_user_choice()
    
    # Start application
    start_application(mode)


if __name__ == "__main__":
    main()
