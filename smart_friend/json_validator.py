#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON格式验证工具
"""

import json
import sys

def validate_json(json_string):
    """验证JSON格式并提供详细错误信息"""
    try:
        parsed = json.loads(json_string)
        print("✅ JSON格式正确！")
        print("解析结果:")
        print(json.dumps(parsed, indent=2, ensure_ascii=False))
        return True
    except json.JSONDecodeError as e:
        print("❌ JSON格式错误！")
        print(f"错误位置: 第 {e.pos} 个字符")
        print(f"错误信息: {e.msg}")
        print(f"错误行号: {e.lineno}")
        print(f"错误列号: {e.colno}")
        
        # 显示错误位置的上下文
        lines = json_string.split('\n')
        if e.lineno <= len(lines):
            error_line = lines[e.lineno - 1]
            print(f"\n错误行内容:")
            print(f"{e.lineno}: {error_line}")
            print(" " * (len(str(e.lineno)) + 2 + e.colno - 1) + "^")
        
        return False

# 常见的JSON错误示例和修复
def show_common_errors():
    """显示常见JSON错误和修复方法"""
    print("🔧 常见JSON错误和修复方法:\n")
    
    examples = [
        {
            "title": "1. 缺少逗号",
            "wrong": '''{
  "name": "张三"
  "age": 25
}''',
            "correct": '''{
  "name": "张三",
  "age": 25
}'''
        },
        {
            "title": "2. 多余逗号",
            "wrong": '''{
  "name": "张三",
  "age": 25,
}''',
            "correct": '''{
  "name": "张三",
  "age": 25
}'''
        },
        {
            "title": "3. 字符串引号问题",
            "wrong": '''{
  "name": '张三',
  "age": 25
}''',
            "correct": '''{
  "name": "张三",
  "age": 25
}'''
        },
        {
            "title": "4. 学习记录JSON示例",
            "wrong": '''{
  "child_id": 1,
  "subject": "数学"
  "homework_completion_rate": 95.0,
  "is_strong_subject": true
}''',
            "correct": '''{
  "child_id": 1,
  "subject": "数学",
  "homework_completion_rate": 95.0,
  "is_strong_subject": true
}'''
        }
    ]
    
    for example in examples:
        print(f"{example['title']}")
        print("❌ 错误格式:")
        print(example['wrong'])
        print("\n✅ 正确格式:")
        print(example['correct'])
        print("-" * 50)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 从命令行参数读取JSON
        json_string = sys.argv[1]
        validate_json(json_string)
    else:
        # 显示使用说明和常见错误
        print("JSON格式验证工具")
        print("=" * 50)
        print("使用方法:")
        print('python json_validator.py \'{"key": "value"}\'')
        print("\n或者直接运行查看常见错误示例:")
        show_common_errors()

if __name__ == "__main__":
    main()
