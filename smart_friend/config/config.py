# 项目配置 (数据库URL等)
import os
from typing import Optional

class Settings:
    """应用配置类"""

    # 数据库配置
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./kids.db")

    # InfluxDB配置
    INFLUXDB_ENABLED: bool = os.getenv("INFLUXDB_ENABLED", "True").lower() == "true"
    INFLUXDB_URL: str = os.getenv("INFLUXDB_URL", "http://101.126.157.41:8086")
    INFLUXDB_TOKEN: str = os.getenv("INFLUXDB_TOKEN", "gIS-8--WRvRSsnLllLqstWtGMxxiEEg6KbPGv6UeV07jgjoR65Dpp9Z5mGaRafOcNMjawbsQw-bZi3Rru1xvlg==")
    INFLUXDB_ORG: str = os.getenv("INFLUXDB_ORG", "1")
    INFLUXDB_BUCKET: str = os.getenv("INFLUXDB_BUCKET", "daily_learning")

    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "AI Child Learning Assistant"

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: str = os.getenv("LOG_FILE", "logs/app.log")

    # AI模型配置
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    MODEL_NAME: str = os.getenv("MODEL_NAME", "gpt-3.5-turbo")

    # 豆包模型配置
    DOUBAO_API_KEY: str = os.getenv("DOUBAO_API_KEY", "945a7413-a966-4215-bdbc-80ed84e92555")
    DOUBAO_BASE_URL: str = os.getenv("DOUBAO_BASE_URL", "https://ark.cn-beijing.volces.com/api/v3")
    DOUBAO_MODEL_NAME: str = os.getenv("DOUBAO_MODEL_NAME", "doubao-1-5-vision-pro-32k-250115")
    DOUBAO_TEMPERATURE: float = float(os.getenv("DOUBAO_TEMPERATURE", "0.7"))
    DOUBAO_TOP_P: float = float(os.getenv("DOUBAO_TOP_P", "0.9"))
    DOUBAO_MAX_TOKENS: int = int(os.getenv("DOUBAO_MAX_TOKENS", "4000"))
    DOUBAO_TIMEOUT: int = int(os.getenv("DOUBAO_TIMEOUT", "60"))

    class Config:
        case_sensitive = True

settings = Settings()
