[API]
# 豆包(火山引擎)语音识别API凭据
asr_app_key = 5311525929
asr_access_key = DRNTjbbfC1QcfDrTndiSSBdTr23F0-23

# 豆包视觉API凭据
vision_api_key = 945a7413-a966-4215-bdbc-80ed84e92555
vision_base_url = https://ark.cn-beijing.volces.com/api/v3

# 字节跳动TTS服务凭据
tts_app_id = 5311525929
tts_token = DRNTjbbfC1QcfDrTndiSSBdTr23F0-23

[AUDIO]
# 音频采样率
sample_rate = 16000
# 音频通道数
channels = 1
# 音频位数
bits = 16

[DIALOGUE]
# 对话历史最大条数
max_history = 10

[SYSTEM]
# 调试模式
debug = False

[MILVUS]
# Milvus向量数据库配置 - 使用Milvus Lite
host = ./milvus_lite.db
# port = 19530  # Milvus Lite不使用端口
# 如果使用Milvus Cloud或有认证需求，配置以下参数
# user =
# password =
# secure = False
# 连接超时时间(秒)
timeout = 30
# 数据库名称
database = ai_child
# 集合配置
portrait_collection = student_portraits
history_collection = learning_history
# 向量维度(根据你使用的embedding模型调整)
vector_dim = 768
# 索引类型
index_type = IVF_FLAT
# 索引参数
index_params = {"nlist": 1024}
# 搜索参数
search_params = {"nprobe": 10}
# 相似度度量类型: L2, IP, COSINE
metric_type = COSINE

[LOGGING]
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
level = DEBUG
# 日志格式
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
# 日期格式
date_format = %Y-%m-%d %H:%M:%S
# 是否输出到控制台
console_output = True
# 是否输出到文件
file_output = True
# 日志文件路径
log_file = logs/app.log
# 日志文件最大大小(字节)，默认10MB
max_file_size = 10485760
# 保留的日志文件数量
backup_count = 5