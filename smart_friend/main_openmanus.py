#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - OpenManus Enhanced Mode

This is the main entry point for the Smart Friend application
running with OpenManus AI framework enhancements.

Enhanced Features:
- All standard features PLUS:
- OpenManus AI orchestration framework
- Jina embeddings for semantic understanding
- Intent classification and context awareness
- Multi-step reasoning and task planning
- Intelligent conversation management
- Educational content personalization

Usage:
    python main_openmanus.py

Server will start on: http://localhost:8003
API Documentation: http://localhost:8003/docs
Web Interface: http://localhost:8003/static/aiChild.html

OpenManus Endpoints:
- POST /api/v1/openmanus/chat - Complete intelligent conversation
- POST /api/v1/openmanus/classify-intent - Intent classification
- GET /api/v1/openmanus/health - System health monitoring
- GET /api/v1/openmanus/stats - Performance analytics
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
from pathlib import Path

# Set environment for OpenManus mode
os.environ["APP_MODE"] = "openmanus"
os.environ["ENABLE_OPENMANUS"] = "true"
os.environ["OPENMANUS_INTENT_CLASSIFICATION"] = "true"
os.environ["OPENMANUS_SEMANTIC_SEARCH"] = "true"
os.environ["OPENMANUS_CONTEXT_AWARENESS"] = "true"

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app_factory import create_app
from config.app_config import get_app_config
from backend.utils.logging import setup_logger
from service.socketio_service import init_socketio_service
from api.v1.endpoints.asr_socketio_api import create_socketio_handlers, connect_asr_service

# Initialize logging
logger = setup_logger('main_openmanus')

# Global variables for ASR
asr_client = None
asr_connected = False
is_recording = False
last_speech_time = time.time()
silence_timeout = 1.5


def initialize_openmanus():
    """Initialize OpenManus framework"""
    logger.info("🧠 Initializing OpenManus AI framework...")
    
    try:
        from openmanus import initialize_system
        
        # Initialize OpenManus system
        planner = initialize_system(auto_start_docker=False)
        logger.info("✅ OpenManus framework initialized successfully")
        
        # Test basic functionality
        test_result = planner.classify_user_intent("Hello, test message")
        logger.info(f"✅ OpenManus test successful: {test_result['output']['predicted_intention']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ OpenManus initialization failed: {e}")
        logger.warning("⚠️ Falling back to standard mode")
        return False


def initialize_services():
    """Initialize additional services"""
    logger.info("🔧 Initializing additional services...")
    
    try:
        # Initialize Socket.IO service
        socketio_service = init_socketio_service()
        logger.info("✅ Socket.IO service initialized")
        
        # Integrate ASR Socket.IO handlers
        create_socketio_handlers(socketio_service.sio)
        logger.info("✅ ASR Socket.IO handlers created")
        
        # Auto-connect ASR service
        async def connect_asr():
            try:
                asr_result = await connect_asr_service()
                if asr_result["success"]:
                    logger.info(f"✅ ASR service connected: {asr_result['message']}")
                else:
                    logger.warning(f"⚠️ ASR service connection failed: {asr_result['message']}")
            except Exception as e:
                logger.error(f"❌ ASR service connection error: {e}")
        
        # Run ASR connection in background
        def run_asr_connection():
            asyncio.run(connect_asr())
        
        threading.Thread(target=run_asr_connection, daemon=True).start()
        
    except Exception as e:
        logger.error(f"❌ Service initialization error: {e}")


def main():
    """Main application entry point"""
    config = get_app_config()
    
    print("🚀 Smart Friend - OpenManus Enhanced Mode")
    print("=" * 60)
    print(f"📋 Application: {config.PROJECT_NAME}")
    print(f"📋 Version: {config.VERSION}")
    print(f"📋 Mode: {config.APP_MODE.value.upper()} (OpenManus Enhanced)")
    print(f"📍 Server: http://{config.HOST}:{config.PORT}")
    print(f"📖 API Documentation: http://localhost:{config.PORT}/docs")
    print(f"📱 Web Interface: http://localhost:{config.PORT}/static/aiChild.html")
    print("")
    print("🧠 OpenManus Features:")
    print("   • Intent Classification with Jina Embeddings")
    print("   • Multi-step Task Planning and Execution")
    print("   • Context-aware Conversation Management")
    print("   • Educational Content Personalization")
    print("")
    print("🌐 OpenManus Endpoints:")
    print(f"   • POST /api/v1/openmanus/chat")
    print(f"   • POST /api/v1/openmanus/classify-intent")
    print(f"   • GET /api/v1/openmanus/health")
    print(f"   • GET /api/v1/openmanus/stats")
    print("=" * 60)
    
    # Initialize OpenManus
    openmanus_success = initialize_openmanus()
    if not openmanus_success:
        print("⚠️ OpenManus initialization failed - some features may be limited")
    
    # Create application
    app = create_app()
    
    # Initialize services
    initialize_services()
    
    logger.info("🌐 Starting Smart Friend server with OpenManus enhancements...")
    
    # Start server
    uvicorn.run(
        app,
        host=config.HOST,
        port=config.PORT,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
