"""
日志管理模块

提供统一的日志配置和管理，支持从配置文件加载日志设置。
实现日志输出到控制台和文件的功能，支持日志轮转和级别控制。
"""

import os
import logging
from logging.handlers import RotatingFileHandler
import sys
from typing import Optional, Dict, Any

# 导入配置管理器
from .config_manager import get_config

# 日志级别映射表
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# 单例模式实现
_root_logger = None
_logger_cache = {}


def get_logger(name: str = None) -> logging.Logger:
    """
    获取配置好的日志记录器
    
    Args:
        name: 日志记录器名称，用于区分不同模块的日志。
             如果为None或空字符串，则返回根日志记录器
             
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    global _root_logger, _logger_cache
    
    # 如果根记录器尚未初始化，则初始化
    if _root_logger is None:
        _root_logger = _setup_root_logger()
    
    # 如果请求根记录器，则直接返回
    if name is None or name == '':
        return _root_logger
    
    # 使用缓存避免重复创建日志记录器
    if name in _logger_cache:
        return _logger_cache[name]
    
    # 创建新的日志记录器
    logger = logging.getLogger(name)
    # 不要重复日志消息到根记录器
    logger.propagate = False
    
    # 使用根记录器的处理器
    for handler in _root_logger.handlers:
        logger.addHandler(handler)
    
    # 使用与根记录器相同的级别
    logger.setLevel(_root_logger.level)
    
    # 缓存此记录器
    _logger_cache[name] = logger
    
    return logger


def _setup_root_logger() -> logging.Logger:
    """
    设置根日志记录器
    
    从配置文件读取日志配置，设置根日志记录器。
    
    Returns:
        logging.Logger: 配置好的根日志记录器
    """
    # 获取配置
    config = get_config()
    
    # 获取日志配置
    log_level = config.get('LOGGING', 'level', 'INFO')
    log_format = config.get('LOGGING', 'format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    date_format = config.get('LOGGING', 'date_format', '%Y-%m-%d %H:%M:%S')
    console_output = config.get_bool('LOGGING', 'console_output', True)
    file_output = config.get_bool('LOGGING', 'file_output', False)
    log_file = config.get('LOGGING', 'log_file', 'logs/lifebuddy.log')
    max_file_size = config.get_int('LOGGING', 'max_file_size', 10 * 1024 * 1024)  # 默认10MB
    backup_count = config.get_int('LOGGING', 'backup_count', 5)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    
    # 设置日志级别
    level = LOG_LEVELS.get(log_level.upper(), logging.INFO)
    root_logger.setLevel(level)
    
    # 移除所有现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(log_format, date_format)
    
    # 添加控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 添加文件处理器
    if file_output and log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 创建轮转文件处理器
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    return root_logger


def reset_logger(config_path: Optional[str] = None):
    """
    重置日志系统
    
    用于在配置更改后重新加载日志配置
    
    Args:
        config_path: 可选的配置文件路径。如果提供，将先重新加载配置管理器
    """
    global _root_logger, _logger_cache
    
    # 如果提供了配置路径，则重新加载配置
    if config_path:
        from .config_manager import get_config
        get_config(config_path)
    
    # 重置根记录器
    _root_logger = None
    
    # 清空缓存
    _logger_cache = {}
    
    # 重新初始化根记录器
    get_logger()


if __name__ == "__main__":
    # 测试日志记录
    logger = get_logger("LoggingTest")
    logger.debug("这是一条调试日志")
    logger.info("这是一条信息日志")
    logger.warning("这是一条警告日志")
    logger.error("这是一条错误日志")
    logger.critical("这是一条严重错误日志")