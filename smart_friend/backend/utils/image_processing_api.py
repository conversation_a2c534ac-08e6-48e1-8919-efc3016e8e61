"""
图像预处理相关的 FastAPI 路由和端点
提供图像预处理算法的 REST API 接口
"""
from fastapi import APIRouter, HTTPException
from datetime import datetime, timezone
import time
import logging

from backend.models.image_processing_models import (
    GrayscaleRequest, BlurRequest, EnhancementRequest,
    ImageProcessingResponse, ImageProcessingHealthResponse,
    GrayscaleMethod, BlurMethod, EnhancementType
)
from backend.services.image_processing_service import ImageProcessingService
from backend.utils.logging import get_camera_logger

logger = get_camera_logger()
router = APIRouter()


@router.post("/grayscale", response_model=ImageProcessingResponse)
async def process_grayscale(request: GrayscaleRequest):
    """
    图像灰度化处理

    将彩色图像转换为灰度图像，支持多种灰度化方法
    """
    start_time = time.time()

    try:
        logger.info(f"开始灰度化处理: method={request.method}")

        # 解码图像
        image = ImageProcessingService.decode_base64_image(request.image_data)
        original_size = f"{image.shape[1]}x{image.shape[0]}"

        # 应用灰度化
        gray_image = ImageProcessingService.apply_grayscale(image, request.method)

        # 编码结果
        processed_image_data = ImageProcessingService.encode_image_to_base64(
            gray_image, request.return_format, request.quality
        )

        processing_time = time.time() - start_time
        processed_size = f"{gray_image.shape[1]}x{gray_image.shape[0]}"

        logger.info(f"灰度化处理完成: 耗时 {processing_time:.3f}s")

        return ImageProcessingResponse(
            success=True,
            message="图像灰度化处理成功",
            processed_image=processed_image_data,
            processing_time=processing_time,
            original_size=original_size,
            processed_size=processed_size,
            algorithm_info={
                "algorithm": "grayscale",
                "method": request.method,
                "preserve_alpha": request.preserve_alpha
            }
        )

    except ValueError as e:
        logger.error(f"灰度化处理参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"灰度化处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"灰度化处理失败: {str(e)}")


@router.post("/blur", response_model=ImageProcessingResponse)
async def process_blur(request: BlurRequest):
    """
    高斯模糊处理

    对图像应用模糊效果，支持多种模糊方法
    """
    start_time = time.time()

    try:
        logger.info(f"开始模糊处理: method={request.method}, kernel_size={request.kernel_size}")

        # 解码图像
        image = ImageProcessingService.decode_base64_image(request.image_data)
        original_size = f"{image.shape[1]}x{image.shape[0]}"

        # 应用模糊
        blurred_image = ImageProcessingService.apply_blur(
            image,
            request.method,
            request.kernel_size,
            request.sigma_x,
            request.sigma_y
        )

        # 编码结果
        processed_image_data = ImageProcessingService.encode_image_to_base64(
            blurred_image, request.return_format, request.quality
        )

        processing_time = time.time() - start_time
        processed_size = f"{blurred_image.shape[1]}x{blurred_image.shape[0]}"

        logger.info(f"模糊处理完成: 耗时 {processing_time:.3f}s")

        return ImageProcessingResponse(
            success=True,
            message="图像模糊处理成功",
            processed_image=processed_image_data,
            processing_time=processing_time,
            original_size=original_size,
            processed_size=processed_size,
            algorithm_info={
                "algorithm": "blur",
                "method": request.method,
                "kernel_size": request.kernel_size,
                "sigma_x": request.sigma_x,
                "sigma_y": request.sigma_y
            }
        )

    except ValueError as e:
        logger.error(f"模糊处理参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"模糊处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"模糊处理失败: {str(e)}")


@router.post("/enhancement", response_model=ImageProcessingResponse)
async def process_enhancement(request: EnhancementRequest):
    """
    图像增强处理

    对图像进行各种增强处理，包括亮度、对比度、饱和度调整等
    """
    start_time = time.time()

    try:
        logger.info(f"开始图像增强: type={request.enhancement_type}")

        # 解码图像
        image = ImageProcessingService.decode_base64_image(request.image_data)
        original_size = f"{image.shape[1]}x{image.shape[0]}"

        # 准备增强参数
        enhancement_params = {}
        if request.brightness is not None:
            enhancement_params['brightness'] = request.brightness
        if request.contrast is not None:
            enhancement_params['contrast'] = request.contrast
        if request.saturation is not None:
            enhancement_params['saturation'] = request.saturation
        if request.gamma is not None:
            enhancement_params['gamma'] = request.gamma
        if request.auto_enhance_strength is not None:
            enhancement_params['auto_enhance_strength'] = request.auto_enhance_strength

        # 应用增强
        enhanced_image = ImageProcessingService.apply_enhancement(
            image, request.enhancement_type, **enhancement_params
        )

        # 编码结果
        processed_image_data = ImageProcessingService.encode_image_to_base64(
            enhanced_image, request.return_format, request.quality
        )

        processing_time = time.time() - start_time
        processed_size = f"{enhanced_image.shape[1]}x{enhanced_image.shape[0]}"

        logger.info(f"图像增强完成: 耗时 {processing_time:.3f}s")

        return ImageProcessingResponse(
            success=True,
            message="图像增强处理成功",
            processed_image=processed_image_data,
            processing_time=processing_time,
            original_size=original_size,
            processed_size=processed_size,
            algorithm_info={
                "algorithm": "enhancement",
                "enhancement_type": request.enhancement_type,
                **enhancement_params
            }
        )

    except ValueError as e:
        logger.error(f"图像增强参数错误: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"图像增强失败: {e}")
        raise HTTPException(status_code=500, detail=f"图像增强失败: {str(e)}")


@router.get("/health", response_model=ImageProcessingHealthResponse)
async def health_check():
    """
    图像处理服务健康检查
    """
    try:
        available_methods = ImageProcessingService.get_available_methods()

        return ImageProcessingHealthResponse(
            service_status="healthy",
            opencv_version=ImageProcessingService.get_opencv_version(),
            available_methods=available_methods,
            timestamp=datetime.now(timezone.utc).isoformat()
        )

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return ImageProcessingHealthResponse(
            service_status="error",
            opencv_version="unknown",
            available_methods={},
            timestamp=datetime.now(timezone.utc).isoformat()
        )
