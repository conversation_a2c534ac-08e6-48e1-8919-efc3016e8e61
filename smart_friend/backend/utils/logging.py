"""
日志配置模块
提供统一的日志配置和管理功能
"""
import logging
import os
from config.config import settings


def setup_logger(name: str, level: str = None) -> logging.Logger:
    """
    设置并返回配置好的日志记录器

    Args:
        name: 日志记录器名称
        level: 日志级别，默认使用配置文件中的级别

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    # 首先设置根日志记录器，确保所有子 logger 都能继承处理器
    root_logger = logging.getLogger()

    # 如果根日志记录器还没有处理器，则设置
    if not root_logger.handlers:
        # 设置日志级别
        log_level = level or settings.LOG_LEVEL
        root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

        # 文件处理器
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(settings.LOG_FILE)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            file_handler = logging.FileHandler(settings.LOG_FILE, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)

            # 记录日志系统初始化成功
            root_logger.info(f"📝 日志系统初始化成功 - 文件: {settings.LOG_FILE}, 级别: {log_level}")
        except Exception as e:
            root_logger.warning(f"无法创建文件日志处理器: {e}")

    # 获取指定名称的 logger
    logger = logging.getLogger(name)

    # 确保子 logger 不重复处理日志消息
    logger.propagate = True

    return logger


def get_camera_logger() -> logging.Logger:
    """获取摄像头模块专用的日志记录器"""
    return setup_logger('camera_manager')