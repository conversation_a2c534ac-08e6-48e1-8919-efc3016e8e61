"""
TTS Utils Module - 文本转语音工具模块

这个模块提供了完整的TTS功能，包括：
1. 协议处理 - 处理WebSocket通信协议
2. 音频播放 - 支持文件播放和流式播放
3. TTS客户端 - 基础TTS功能
4. TTS管理器 - 高级TTS功能，支持流式处理
5. 文本预处理 - 优化TTS输出

设计原则：
- 单一职责原则：每个类负责特定功能
- 开闭原则：通过继承扩展功能
- 依赖倒置：依赖抽象而非具体实现
"""

import pygame
import websockets
import websocket
import aiofiles
import asyncio
import json
import os
import tempfile
import time
import uuid
import warnings
import threading
import re
import queue
from typing import Union, Optional
from abc import ABC, abstractmethod

# 导入日志管理模块
from backend.utils.logging_manager import get_logger

# 获取日志记录器
logger = get_logger("TTSUtils")

# 可选导入pydub，如果不可用则使用pygame作为备选
try:
    from pydub.playback import play
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("Pydub不可用，将仅使用Pygame进行音频播放")

# 抑制警告
warnings.filterwarnings("ignore", category=RuntimeWarning, module="pydub.utils")
os.environ['PYGAME_HIDE_SUPPORT_PROMPT'] = '1'


# ==================== 协议常量 ====================
class TTSProtocolConstants:
    """TTS协议常量类 - 集中管理所有协议相关常量"""
    
    # 协议版本
    PROTOCOL_VERSION = 0b0001
    DEFAULT_HEADER_SIZE = 0b0001
    
    # 消息类型
    FULL_CLIENT_REQUEST = 0b0001
    AUDIO_ONLY_RESPONSE = 0b1011
    FULL_SERVER_RESPONSE = 0b1001
    ERROR_INFORMATION = 0b1111
    
    # 消息类型特定标志
    MSG_TYPE_FLAG_NO_SEQ = 0b0000
    MSG_TYPE_FLAG_POSITIVE_SEQ = 0b1
    MSG_TYPE_FLAG_LAST_NO_SEQ = 0b10
    MSG_TYPE_FLAG_NEGATIVE_SEQ = 0b11
    MSG_TYPE_FLAG_WITH_EVENT = 0b100
    
    # 消息序列化
    NO_SERIALIZATION = 0b0000
    JSON = 0b0001
    
    # 消息压缩
    COMPRESSION_NO = 0b0000
    COMPRESSION_GZIP = 0b0001
    
    # 事件常量
    EVENT_NONE = 0
    EVENT_START_CONNECTION = 1
    EVENT_FINISH_CONNECTION = 2
    EVENT_CONNECTION_STARTED = 50
    EVENT_CONNECTION_FAILED = 51
    EVENT_CONNECTION_FINISHED = 52
    
    # Session事件
    EVENT_START_SESSION = 100
    EVENT_FINISH_SESSION = 102
    EVENT_SESSION_STARTED = 150
    EVENT_SESSION_FINISHED = 152
    EVENT_SESSION_FAILED = 153
    
    # 通用事件
    EVENT_TASK_REQUEST = 200
    
    # TTS事件
    EVENT_TTS_SENTENCE_START = 350
    EVENT_TTS_SENTENCE_END = 351
    EVENT_TTS_RESPONSE = 352


# ==================== 文本预处理器 ====================
class TextPreprocessor:
    """文本预处理器 - 负责优化TTS输入文本"""
    
    @staticmethod
    def preprocess_text_for_tts(text: str) -> str:
        """
        预处理文本以优化TTS输出，避免读出标点符号和语法格式
        
        Args:
            text (str): 原始文本
            
        Returns:
            str: 处理后的文本
        """
        # 移除Markdown代码块标记
        text = re.sub(r'```[\w]*\n|```', ' ', text)
        
        # 移除Markdown链接格式 [text](url)
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        # 移除Markdown粗体和斜体标记
        text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
        text = re.sub(r'\*([^*]+)\*', r'\1', text)
        text = re.sub(r'__([^_]+)__', r'\1', text)
        text = re.sub(r'_([^_]+)_', r'\1', text)
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 处理特殊符号
        text = text.replace('&nbsp;', ' ')
        text = text.replace('&lt;', '<')
        text = text.replace('&gt;', '>')
        text = text.replace('&amp;', '&')
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 处理数字和特殊字符
        text = re.sub(r'(\d+)\.(\d+)', r'\1点\2', text)
        text = text.replace('%', '百分之')
        text = text.replace('&', '和')
        text = text.replace('@', '艾特')
        text = text.replace('#', '井号')
        
        # 移除或替换可能导致TTS问题的字符
        text = text.replace('|', '或')
        text = text.replace('~', '')
        text = text.replace('^', '')
        text = text.replace('`', '')
        
        # 确保句子以适当的标点结尾
        if text and not text[-1] in '。！？.!?':
            text += '。'
            
        return text


# ==================== 协议处理类 ====================
class TTSProtocolHeader:
    """TTS协议头部类 - 处理WebSocket消息头部"""
    
    def __init__(self,
                 protocol_version=TTSProtocolConstants.PROTOCOL_VERSION,
                 header_size=TTSProtocolConstants.DEFAULT_HEADER_SIZE,
                 message_type: int = 0,
                 message_type_specific_flags: int = 0,
                 serial_method: int = TTSProtocolConstants.NO_SERIALIZATION,
                 compression_type: int = TTSProtocolConstants.COMPRESSION_NO,
                 reserved_data=0):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        """将头部转换为字节数组"""
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_specific_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class TTSProtocolOptional:
    """TTS协议可选部分类 - 处理WebSocket消息可选字段"""
    
    def __init__(self, event: int = TTSProtocolConstants.EVENT_NONE, 
                 session_id: str = None, sequence: int = None):
        self.event = event
        self.session_id = session_id
        self.error_code: int = 0
        self.connection_id: Union[str, None] = None
        self.response_meta_json: Union[str, None] = None
        self.sequence = sequence

    def as_bytes(self) -> bytes:
        """将可选部分转换为字节数组"""
        option_bytes = bytearray()
        if self.event != TTSProtocolConstants.EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.session_id is not None:
            session_id_bytes = str.encode(self.session_id)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class TTSProtocolResponse:
    """TTS协议响应类 - 封装WebSocket响应"""
    
    def __init__(self, header: TTSProtocolHeader, optional: TTSProtocolOptional):
        self.optional = optional
        self.header = header
        self.payload: Union[bytes, None] = None

    def __str__(self):
        return f"TTSProtocolResponse(event={self.optional.event}, payload_size={len(self.payload) if self.payload else 0})"


# ==================== 协议处理器 ====================
class TTSProtocolProcessor:
    """TTS协议处理器 - 负责协议解析和消息构建"""
    
    @staticmethod
    def read_content(res: bytes, offset: int):
        """读取响应内容"""
        content_size = int.from_bytes(res[offset: offset + 4], "big", signed=True)
        offset += 4
        content = str(res[offset: offset + content_size])
        offset += content_size
        return content, offset

    @staticmethod
    def read_payload(res: bytes, offset: int):
        """读取响应载荷"""
        payload_size = int.from_bytes(res[offset: offset + 4], "big", signed=True)
        offset += 4
        payload = res[offset: offset + payload_size]
        offset += payload_size
        return payload, offset

    @classmethod
    def parse_response(cls, res) -> TTSProtocolResponse:
        """解析WebSocket响应"""
        if isinstance(res, str):
            raise RuntimeError(res)
            
        response = TTSProtocolResponse(TTSProtocolHeader(), TTSProtocolOptional())
        
        # 解析头部
        header = response.header
        num = 0b00001111
        header.protocol_version = res[0] >> 4 & num
        header.header_size = res[0] & 0x0f
        header.message_type = (res[1] >> 4) & num
        header.message_type_specific_flags = res[1] & 0x0f
        header.serialization_method = res[2] >> num
        header.message_compression = res[2] & 0x0f
        header.reserved = res[3]
        
        offset = 4
        optional = response.optional
        
        if header.message_type == TTSProtocolConstants.FULL_SERVER_RESPONSE or TTSProtocolConstants.AUDIO_ONLY_RESPONSE:
            # 读取事件
            if header.message_type_specific_flags == TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT:
                optional.event = int.from_bytes(res[offset:offset+4], "big", signed=True)
                offset += 4
                
                if optional.event == TTSProtocolConstants.EVENT_NONE:
                    return response
                elif optional.event == TTSProtocolConstants.EVENT_CONNECTION_STARTED:
                    optional.connection_id, offset = cls.read_content(res, offset)
                elif optional.event == TTSProtocolConstants.EVENT_CONNECTION_FAILED:
                    optional.response_meta_json, offset = cls.read_content(res, offset)
                elif optional.event in [TTSProtocolConstants.EVENT_SESSION_STARTED,
                                      TTSProtocolConstants.EVENT_SESSION_FAILED,
                                      TTSProtocolConstants.EVENT_SESSION_FINISHED]:
                    optional.session_id, offset = cls.read_content(res, offset)
                    optional.response_meta_json, offset = cls.read_content(res, offset)
                else:
                    optional.session_id, offset = cls.read_content(res, offset)
                    response.payload, offset = cls.read_payload(res, offset)
                    
        elif header.message_type == TTSProtocolConstants.ERROR_INFORMATION:
            optional.error_code = int.from_bytes(res[offset:offset + 4], "big", signed=True)
            offset += 4
            response.payload, offset = cls.read_payload(res, offset)
            
        return response

    @staticmethod
    def get_payload_bytes(uid='1234', event=TTSProtocolConstants.EVENT_NONE, 
                         text='', speaker='', audio_format='mp3', audio_sample_rate=24000):
        """构建载荷字节数据"""
        return str.encode(json.dumps({
            "user": {"uid": uid},
            "event": event,
            "namespace": "BidirectionalTTS",
            "req_params": {
                "text": text,
                "speaker": speaker,
                "audio_params": {
                    "format": audio_format,
                    "sample_rate": audio_sample_rate
                }
            }
        }))

    @staticmethod
    async def send_event(ws: websocket, header: bytes, optional: Union[bytes, None] = None,
                        payload: bytes = None):
        """发送WebSocket事件"""
        full_client_request = bytearray(header)
        if optional is not None:
            full_client_request.extend(optional)
        if payload is not None:
            payload_size = len(payload).to_bytes(4, 'big', signed=True)
            full_client_request.extend(payload_size)
            full_client_request.extend(payload)
        await ws.send(full_client_request)


# ==================== 音频播放器抽象基类 ====================
class BaseAudioPlayer(ABC):
    """音频播放器抽象基类 - 定义音频播放接口"""

    @abstractmethod
    def play_file(self, file_path: str) -> bool:
        """播放音频文件"""
        pass

    @abstractmethod
    def start_stream_playback(self):
        """开始流式播放"""
        pass

    @abstractmethod
    def stop_stream_playback(self):
        """停止流式播放"""
        pass

    @abstractmethod
    def add_audio_chunk(self, audio_data: bytes):
        """添加音频数据块"""
        pass


# ==================== Pygame音频播放器 ====================
class PygameAudioPlayer(BaseAudioPlayer):
    """基于Pygame的音频播放器 - 负责音频文件播放和流式播放"""

    def __init__(self):
        """初始化Pygame音频播放器"""
        self.initialized = False
        self.is_playing = False
        self.stream_queue = queue.Queue()
        self.playback_thread = None
        self.stop_event = threading.Event()

        try:
            pygame.mixer.init()
            self.initialized = True
            logger.info("Pygame音频播放器初始化成功")
        except Exception as e:
            logger.error(f"初始化Pygame音频播放器失败: {e}")
            self.initialized = False

    def play_file(self, file_path: str) -> bool:
        """
        播放音频文件

        Args:
            file_path (str): 音频文件路径

        Returns:
            bool: 是否成功播放
        """
        if not self.initialized:
            logger.error("音频播放器未初始化")
            return False

        try:
            if not pygame.mixer.get_init():
                pygame.mixer.init()

            sound = pygame.mixer.Sound(file_path)
            sound.play()
            pygame.time.wait(int(sound.get_length() * 1000))

            logger.info(f"成功播放音频文件: {file_path}")
            return True
        except Exception as e:
            logger.error(f"播放音频文件失败: {e}")
            return False

    def start_stream_playback(self):
        """开始流式播放"""
        if self.is_playing:
            logger.warning("流式播放已在运行中")
            return

        self.is_playing = True
        self.stop_event.clear()
        self.playback_thread = threading.Thread(target=self._stream_playback_worker)
        self.playback_thread.start()
        logger.info("开始流式播放")

    def stop_stream_playback(self):
        """停止流式播放"""
        if not self.is_playing:
            logger.debug("流式播放未在运行中，忽略停止请求")
            return

        self.stop_event.set()
        if self.playback_thread and self.playback_thread.is_alive():
            self.playback_thread.join(timeout=1.0)
        self.is_playing = False

        # 清空队列
        while not self.stream_queue.empty():
            try:
                self.stream_queue.get_nowait()
            except queue.Empty:
                break

        logger.info("停止流式播放")

    def add_audio_chunk(self, audio_data: bytes):
        """添加音频数据块到播放队列"""
        self.stream_queue.put(audio_data)

    def _stream_playback_worker(self):
        """流式播放工作线程（内部方法）"""
        chunk_id = 0

        with tempfile.TemporaryDirectory() as temp_dir:
            while not self.stop_event.is_set():
                try:
                    try:
                        audio_data = self.stream_queue.get(timeout=0.5)
                    except queue.Empty:
                        continue

                    if not audio_data:
                        continue

                    temp_path = os.path.join(temp_dir, f"chunk_{chunk_id}.mp3")
                    with open(temp_path, "wb") as f:
                        f.write(audio_data)

                    try:
                        pygame.mixer.music.load(temp_path)
                        pygame.mixer.music.play()

                        while pygame.mixer.music.get_busy() and not self.stop_event.is_set():
                            time.sleep(0.1)
                    except Exception as e:
                        logger.error(f"播放音频块失败: {e}")

                    chunk_id += 1

                except Exception as e:
                    logger.error(f"流式播放线程异常: {e}")
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.5)


# ==================== Pydub音频播放器 ====================
class PydubAudioPlayer(BaseAudioPlayer):
    """基于Pydub的音频播放器 - 提供高质量音频播放"""

    def __init__(self):
        """初始化Pydub音频播放器"""
        self.is_playing = False
        self.stream_queue = queue.Queue()
        self.playback_thread = None
        self.stop_event = threading.Event()

        if not PYDUB_AVAILABLE:
            logger.warning("Pydub不可用，PydubAudioPlayer将回退到Pygame")
            # 初始化pygame作为备选
            try:
                pygame.mixer.init()
                self.pygame_available = True
            except Exception as e:
                logger.error(f"Pygame初始化失败: {e}")
                self.pygame_available = False
        else:
            self.pygame_available = False

        logger.info("Pydub音频播放器初始化成功")

    def play_file(self, file_path: str) -> bool:
        """播放音频文件"""
        if PYDUB_AVAILABLE:
            try:
                sound = AudioSegment.from_mp3(file_path)
                play(sound)
                logger.info(f"成功播放音频文件: {file_path}")
                return True
            except Exception as e:
                logger.error(f"Pydub播放音频文件失败: {e}")
                # 回退到pygame
                return self._play_with_pygame(file_path)
        else:
            return self._play_with_pygame(file_path)

    def _play_with_pygame(self, file_path: str) -> bool:
        """使用pygame播放音频文件（备选方案）"""
        if not self.pygame_available:
            logger.error("Pygame不可用，无法播放音频")
            return False

        try:
            if not pygame.mixer.get_init():
                pygame.mixer.init()

            pygame.mixer.music.load(file_path)
            pygame.mixer.music.play()

            while pygame.mixer.music.get_busy():
                time.sleep(0.1)

            logger.info(f"成功使用Pygame播放音频文件: {file_path}")
            return True
        except Exception as e:
            logger.error(f"Pygame播放音频文件失败: {e}")
            return False

    def start_stream_playback(self):
        """开始流式播放"""
        if self.is_playing:
            logger.warning("流式播放已在运行中")
            return

        self.is_playing = True
        self.stop_event.clear()
        self.playback_thread = threading.Thread(target=self._stream_playback_worker)
        self.playback_thread.start()
        logger.info("开始流式播放")

    def stop_stream_playback(self):
        """停止流式播放"""
        if not self.is_playing:
            return

        self.stop_event.set()
        if self.playback_thread and self.playback_thread.is_alive():
            self.playback_thread.join(timeout=1.0)
        self.is_playing = False

        while not self.stream_queue.empty():
            try:
                self.stream_queue.get_nowait()
            except queue.Empty:
                break

        logger.info("停止流式播放")

    def add_audio_chunk(self, audio_data: bytes):
        """添加音频数据块到播放队列"""
        self.stream_queue.put(audio_data)

    def _stream_playback_worker(self):
        """流式播放工作线程"""
        while not self.stop_event.is_set():
            try:
                try:
                    audio_data = self.stream_queue.get(timeout=0.5)
                except queue.Empty:
                    continue

                if not audio_data:
                    continue

                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                    temp_path = temp_file.name
                    temp_file.write(audio_data)

                try:
                    if PYDUB_AVAILABLE:
                        sound = AudioSegment.from_mp3(temp_path)
                        play(sound)
                    else:
                        # 使用pygame播放
                        self._play_with_pygame(temp_path)
                except Exception as e:
                    logger.error(f"播放音频块失败: {e}")
                finally:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

            except Exception as e:
                logger.error(f"流式播放线程异常: {e}")
                if self.stop_event.is_set():
                    break
                time.sleep(0.5)


# ==================== 音频播放器工厂 ====================
class AudioPlayerFactory:
    """音频播放器工厂 - 负责创建合适的音频播放器实例"""

    @staticmethod
    def create_player(player_type: str = "auto") -> BaseAudioPlayer:
        """
        创建音频播放器实例

        Args:
            player_type (str): 播放器类型 ("pygame", "pydub", "auto")

        Returns:
            BaseAudioPlayer: 音频播放器实例
        """
        if player_type == "pygame":
            return PygameAudioPlayer()
        elif player_type == "pydub":
            return PydubAudioPlayer()
        elif player_type == "auto":
            # 自动选择最佳播放器
            try:
                pygame_player = PygameAudioPlayer()
                if pygame_player.initialized:
                    return pygame_player
                else:
                    if PYDUB_AVAILABLE:
                        logger.info("Pygame不可用，使用Pydub播放器")
                        return PydubAudioPlayer()
                    else:
                        logger.warning("Pygame和Pydub都不可用，返回Pygame播放器")
                        return pygame_player
            except Exception:
                if PYDUB_AVAILABLE:
                    logger.info("Pygame不可用，使用Pydub播放器")
                    return PydubAudioPlayer()
                else:
                    logger.warning("所有播放器都不可用")
                    return PygameAudioPlayer()
        else:
            raise ValueError(f"不支持的播放器类型: {player_type}")


# ==================== WebSocket连接管理器 ====================
class WebSocketManager:
    """WebSocket连接管理器 - 负责管理TTS服务的WebSocket连接"""

    def __init__(self, app_id: str, token: str):
        """
        初始化WebSocket管理器

        Args:
            app_id (str): 应用ID
            token (str): 访问令牌
        """
        self.app_id = app_id
        self.token = token
        self.ws_header = {
            "X-Api-App-Key": app_id,
            "X-Api-Access-Key": token,
            "X-Api-Resource-Id": 'volc.service_type.10029',
            "X-Api-Connect-Id": str(uuid.uuid4()),
        }
        self.url = 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'
        self.ws = None
        self.connected = False

    async def connect(self):
        """建立WebSocket连接"""
        try:
            self.ws = await websockets.connect(
                self.url,
                additional_headers=self.ws_header,
                max_size=1000000000
            )
            self.connected = True
            logger.info("WebSocket连接已建立")
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise

    async def disconnect(self):
        """断开WebSocket连接"""
        if self.ws:
            await self.ws.close()
            self.connected = False
            logger.info("WebSocket连接已断开")

    async def start_connection(self):
        """开始TTS连接"""
        header = TTSProtocolHeader(
            message_type=TTSProtocolConstants.FULL_CLIENT_REQUEST,
            message_type_specific_flags=TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT
        ).as_bytes()
        optional = TTSProtocolOptional(event=TTSProtocolConstants.EVENT_START_CONNECTION).as_bytes()
        payload = str.encode("{}")
        await TTSProtocolProcessor.send_event(self.ws, header, optional, payload)

    async def start_session(self, speaker: str, session_id: str):
        """开始TTS会话"""
        header = TTSProtocolHeader(
            message_type=TTSProtocolConstants.FULL_CLIENT_REQUEST,
            message_type_specific_flags=TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=TTSProtocolConstants.JSON
        ).as_bytes()
        optional = TTSProtocolOptional(
            event=TTSProtocolConstants.EVENT_START_SESSION,
            session_id=session_id
        ).as_bytes()
        payload = TTSProtocolProcessor.get_payload_bytes(
            event=TTSProtocolConstants.EVENT_START_SESSION,
            speaker=speaker
        )
        await TTSProtocolProcessor.send_event(self.ws, header, optional, payload)

    async def send_text(self, text: str, speaker: str, session_id: str):
        """发送文本进行TTS转换"""
        header = TTSProtocolHeader(
            message_type=TTSProtocolConstants.FULL_CLIENT_REQUEST,
            message_type_specific_flags=TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=TTSProtocolConstants.JSON
        ).as_bytes()
        optional = TTSProtocolOptional(
            event=TTSProtocolConstants.EVENT_TASK_REQUEST,
            session_id=session_id
        ).as_bytes()
        payload = TTSProtocolProcessor.get_payload_bytes(
            event=TTSProtocolConstants.EVENT_TASK_REQUEST,
            text=text,
            speaker=speaker
        )
        await TTSProtocolProcessor.send_event(self.ws, header, optional, payload)

    async def finish_session(self, session_id: str):
        """结束TTS会话"""
        header = TTSProtocolHeader(
            message_type=TTSProtocolConstants.FULL_CLIENT_REQUEST,
            message_type_specific_flags=TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=TTSProtocolConstants.JSON
        ).as_bytes()
        optional = TTSProtocolOptional(
            event=TTSProtocolConstants.EVENT_FINISH_SESSION,
            session_id=session_id
        ).as_bytes()
        payload = str.encode('{}')
        await TTSProtocolProcessor.send_event(self.ws, header, optional, payload)

    async def finish_connection(self):
        """结束TTS连接"""
        header = TTSProtocolHeader(
            message_type=TTSProtocolConstants.FULL_CLIENT_REQUEST,
            message_type_specific_flags=TTSProtocolConstants.MSG_TYPE_FLAG_WITH_EVENT,
            serial_method=TTSProtocolConstants.JSON
        ).as_bytes()
        optional = TTSProtocolOptional(event=TTSProtocolConstants.EVENT_FINISH_CONNECTION).as_bytes()
        payload = str.encode('{}')
        await TTSProtocolProcessor.send_event(self.ws, header, optional, payload)


# ==================== 基础TTS客户端 ====================
class BaseTTSClient(ABC):
    """TTS客户端抽象基类 - 定义TTS客户端接口"""

    @abstractmethod
    def text_to_speech_file(self, text: str, output_path: str) -> bool:
        """将文本转换为语音文件"""
        pass

    @abstractmethod
    def text_to_speech_play(self, text: str) -> bool:
        """将文本转换为语音并播放"""
        pass

    @abstractmethod
    def set_debug(self, debug: bool):
        """设置调试模式"""
        pass


class SimpleTTSClient(BaseTTSClient):
    """简单TTS客户端 - 提供基础的文本转语音功能"""

    def __init__(self, app_id: str, token: str, speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts'):
        """
        初始化简单TTS客户端

        Args:
            app_id (str): 应用ID
            token (str): 访问令牌
            speaker (str): 发音人
        """
        if not app_id:
            raise ValueError("必须提供TTS服务的APP ID")
        if not token:
            raise ValueError("必须提供TTS服务的访问令牌")

        self.app_id = app_id
        self.token = token
        self.speaker = speaker
        self.debug = False

        # 创建WebSocket管理器和音频播放器
        self.ws_manager = WebSocketManager(app_id, token)
        self.audio_player = AudioPlayerFactory.create_player("auto")

        logger.info("简单TTS客户端初始化完成")

    def set_debug(self, debug: bool):
        """设置调试模式"""
        self.debug = debug
        logger.info(f"调试模式已{'启用' if debug else '禁用'}")

    async def _text_to_speech_internal(self, text: str, output_path: Optional[str] = None) -> bytes:
        """
        内部方法：执行文本转语音转换

        Args:
            text (str): 要转换的文本
            output_path (Optional[str]): 输出文件路径

        Returns:
            bytes: 音频数据
        """
        # 预处理文本
        processed_text = TextPreprocessor.preprocess_text_for_tts(text)
        if self.debug:
            logger.debug(f"原始文本: {text[:50]}...")
            logger.debug(f"处理后文本: {processed_text[:50]}...")

        audio_data = bytearray()

        # 建立连接
        await self.ws_manager.connect()

        try:
            # 开始连接
            await self.ws_manager.start_connection()
            res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"开始连接响应: {res}")
            if res.optional.event != TTSProtocolConstants.EVENT_CONNECTION_STARTED:
                raise RuntimeError("开始连接失败")

            # 开始会话
            session_id = uuid.uuid4().__str__().replace('-', '')
            await self.ws_manager.start_session(self.speaker, session_id)
            res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"开始会话响应: {res}")
            if res.optional.event != TTSProtocolConstants.EVENT_SESSION_STARTED:
                raise RuntimeError('开始会话失败!')

            # 发送文本
            await self.ws_manager.send_text(processed_text, self.speaker, session_id)
            await self.ws_manager.finish_session(session_id)

            # 接收音频数据
            if output_path:
                async with aiofiles.open(output_path, mode="wb") as output_file:
                    while True:
                        res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
                        if self.debug:
                            logger.debug(f"接收响应: {res}")
                        if (res.optional.event == TTSProtocolConstants.EVENT_TTS_RESPONSE and
                            res.header.message_type == TTSProtocolConstants.AUDIO_ONLY_RESPONSE):
                            await output_file.write(res.payload)
                            audio_data.extend(res.payload)
                        elif res.optional.event in [TTSProtocolConstants.EVENT_TTS_SENTENCE_START,
                                                   TTSProtocolConstants.EVENT_TTS_SENTENCE_END]:
                            continue
                        else:
                            break
            else:
                while True:
                    res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
                    if self.debug:
                        logger.debug(f"接收响应: {res}")
                    if (res.optional.event == TTSProtocolConstants.EVENT_TTS_RESPONSE and
                        res.header.message_type == TTSProtocolConstants.AUDIO_ONLY_RESPONSE):
                        audio_data.extend(res.payload)
                    elif res.optional.event in [TTSProtocolConstants.EVENT_TTS_SENTENCE_START,
                                               TTSProtocolConstants.EVENT_TTS_SENTENCE_END]:
                        continue
                    else:
                        break

            # 结束连接
            await self.ws_manager.finish_connection()
            res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"结束连接响应: {res}")

        finally:
            await self.ws_manager.disconnect()

        return bytes(audio_data)

    def text_to_speech_file(self, text: str, output_path: str) -> bool:
        """
        将文本转换为语音并保存为文件

        Args:
            text (str): 要转换的文本
            output_path (str): 输出文件路径

        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

            # 执行语音合成
            asyncio.run(self._text_to_speech_internal(text, output_path))

            logger.info(f"语音已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存语音文件失败: {e}")
            return False

    def text_to_speech_play(self, text: str) -> bool:
        """
        将文本转换为语音并播放

        Args:
            text (str): 要转换的文本

        Returns:
            bool: 是否成功播放
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 转换并保存到临时文件
                asyncio.run(self._text_to_speech_internal(text, temp_path))

                # 播放音频
                success = self.audio_player.play_file(temp_path)
                if success:
                    logger.info("语音播放完成")
                return success
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        except Exception as e:
            logger.error(f"播放语音失败: {e}")
            return False


# ==================== 高级TTS管理器 ====================
class AdvancedTTSManager(BaseTTSClient):
    """
    高级TTS管理器 - 提供完整的TTS功能，包括流式处理

    支持功能：
    1. 同步和异步文本转语音转换
    2. 流式文本输入和语音输出
    3. 音频文件保存与播放
    4. 健壮的WebSocket连接管理
    5. 支持多线程异步调用
    """

    def __init__(self, app_id: str = "5311525929", token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
                 speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts'):
        """
        初始化高级TTS管理器

        Args:
            app_id (str): 语音服务应用ID
            token (str): 语音服务访问令牌
            speaker (str): 发音人
        """
        if not app_id:
            raise ValueError("必须提供TTS服务的APP ID")
        if not token:
            raise ValueError("必须提供TTS服务的访问令牌")

        self.app_id = app_id
        self.token = token
        self.speaker = speaker
        self.debug = False

        # WebSocket连接管理器
        self.ws_manager = None

        # 音频播放器
        self.audio_player = AudioPlayerFactory.create_player("auto")

        # 流式合成相关
        self.stream_active = False
        try:
            self.stream_lock = asyncio.Lock()
            self.stream_processing_event = asyncio.Event()
        except RuntimeError:
            # 为测试环境创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            self.stream_lock = asyncio.Lock()
            self.stream_processing_event = asyncio.Event()
        self.stream_session_id = None
        self.stream_text_buffer = []
        self.stream_worker_task = None

        logger.info("高级TTS管理器已初始化")

    def set_debug(self, debug: bool):
        """设置调试模式"""
        self.debug = debug
        logger.info(f"调试模式已{'启用' if debug else '禁用'}")

    def text_to_speech_file(self, text: str, output_path: str) -> bool:
        """
        将文本转换为语音并保存为文件

        Args:
            text (str): 要转换的文本
            output_path (str): 输出文件路径

        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建目录（如果不存在）
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

            # 执行语音合成
            audio_data = asyncio.run(self._text_to_speech_internal(text))

            # 写入文件
            with open(output_path, 'wb') as f:
                f.write(audio_data)

            logger.info(f"语音已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存语音文件失败: {e}")
            return False

    def text_to_speech_play(self, text: str) -> bool:
        """
        将文本转换为语音并播放

        Args:
            text (str): 要转换的文本

        Returns:
            bool: 是否成功播放
        """
        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 执行语音合成
                audio_data = asyncio.run(self._text_to_speech_internal(text))

                # 写入临时文件
                with open(temp_path, 'wb') as f:
                    f.write(audio_data)

                # 播放音频
                success = self.audio_player.play_file(temp_path)
                if success:
                    logger.info("语音播放完成")
                return success
            finally:
                # 删除临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        except Exception as e:
            logger.error(f"播放语音失败: {e}")
            return False

    async def _text_to_speech_internal(self, text: str) -> bytes:
        """
        内部方法：执行文本转语音转换

        Args:
            text (str): 要转换的文本

        Returns:
            bytes: 音频数据
        """
        # 预处理文本
        processed_text = TextPreprocessor.preprocess_text_for_tts(text)
        if self.debug:
            logger.debug(f"原始文本: {text[:50]}...")
            logger.debug(f"处理后文本: {processed_text[:50]}...")

        audio_data = bytearray()

        # 创建WebSocket管理器
        ws_manager = WebSocketManager(self.app_id, self.token)

        # 建立连接
        await ws_manager.connect()

        try:
            # 开始连接
            await ws_manager.start_connection()
            res = TTSProtocolProcessor.parse_response(await ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"开始连接响应: {res}")
            if res.optional.event != TTSProtocolConstants.EVENT_CONNECTION_STARTED:
                raise RuntimeError("开始连接失败")

            # 开始会话
            session_id = uuid.uuid4().__str__().replace('-', '')
            await ws_manager.start_session(self.speaker, session_id)
            res = TTSProtocolProcessor.parse_response(await ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"开始会话响应: {res}")
            if res.optional.event != TTSProtocolConstants.EVENT_SESSION_STARTED:
                raise RuntimeError('开始会话失败!')

            # 发送文本
            await ws_manager.send_text(processed_text, self.speaker, session_id)
            await ws_manager.finish_session(session_id)

            # 接收音频数据
            while True:
                res = TTSProtocolProcessor.parse_response(await ws_manager.ws.recv())
                if self.debug:
                    logger.debug(f"接收响应: {res}")
                if (res.optional.event == TTSProtocolConstants.EVENT_TTS_RESPONSE and
                    res.header.message_type == TTSProtocolConstants.AUDIO_ONLY_RESPONSE):
                    audio_data.extend(res.payload)
                elif res.optional.event in [TTSProtocolConstants.EVENT_TTS_SENTENCE_START,
                                           TTSProtocolConstants.EVENT_TTS_SENTENCE_END]:
                    continue
                else:
                    break

            # 结束连接
            await ws_manager.finish_connection()
            res = TTSProtocolProcessor.parse_response(await ws_manager.ws.recv())
            if self.debug:
                logger.debug(f"结束连接响应: {res}")

        finally:
            await ws_manager.disconnect()

        return bytes(audio_data)

    async def start_streaming_synthesis(self) -> bool:
        """
        开始流式语音合成

        Returns:
            bool: 是否成功启动
        """
        if self.stream_active:
            logger.warning("流式语音合成已经在运行中")
            return False

        try:
            async with self.stream_lock:
                # 创建WebSocket管理器
                self.ws_manager = WebSocketManager(self.app_id, self.token)
                await self.ws_manager.connect()

                # 开始连接
                await self.ws_manager.start_connection()
                res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
                if res.optional.event != TTSProtocolConstants.EVENT_CONNECTION_STARTED:
                    raise RuntimeError("流式合成：开始连接失败")

                # 开始会话
                self.stream_session_id = uuid.uuid4().__str__().replace('-', '')
                await self.ws_manager.start_session(self.speaker, self.stream_session_id)
                res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())
                if res.optional.event != TTSProtocolConstants.EVENT_SESSION_STARTED:
                    raise RuntimeError('流式合成：开始会话失败!')

                # 设置流式状态
                self.stream_active = True
                self.stream_text_buffer = []

                # 启动音频播放器
                self.audio_player.start_stream_playback()

                # 启动流式处理工作线程
                self.stream_worker_task = asyncio.create_task(self._stream_processing_worker())

                logger.info("流式语音合成已启动")
                return True

        except Exception as e:
            logger.error(f"启动流式语音合成失败: {e}")
            await self._cleanup_streaming()
            return False

    async def stop_streaming_synthesis(self) -> bool:
        """
        停止流式语音合成

        Returns:
            bool: 是否成功停止
        """
        if not self.stream_active:
            logger.warning("流式语音合成未在运行中")
            return False

        try:
            async with self.stream_lock:
                # 处理剩余的文本缓冲区
                if self.stream_text_buffer:
                    remaining_text = ''.join(self.stream_text_buffer)
                    if remaining_text.strip():
                        await self._process_streaming_text(remaining_text)

                # 等待处理完成
                if self.stream_worker_task:
                    self.stream_worker_task.cancel()
                    try:
                        await self.stream_worker_task
                    except asyncio.CancelledError:
                        pass

                # 结束会话和连接
                if self.ws_manager and self.stream_session_id:
                    await self.ws_manager.finish_session(self.stream_session_id)
                    await self.ws_manager.finish_connection()
                    await self.ws_manager.disconnect()

                # 停止音频播放器
                self.audio_player.stop_stream_playback()

                # 清理状态
                await self._cleanup_streaming()

                logger.info("流式语音合成已停止")
                return True

        except Exception as e:
            logger.error(f"停止流式语音合成失败: {e}")
            await self._cleanup_streaming()
            return False

    async def add_streaming_text(self, text: str):
        """
        添加流式文本

        Args:
            text (str): 要添加的文本
        """
        if not self.stream_active:
            logger.warning("流式语音合成未启动，无法添加文本")
            return

        if not text.strip():
            return

        async with self.stream_lock:
            self.stream_text_buffer.append(text)

            # 检查是否需要立即处理
            combined_text = ''.join(self.stream_text_buffer)
            if (len(combined_text) >= 50 or
                any(punct in text for punct in '。！？.!?') or
                text.endswith('\n')):

                # 处理当前缓冲区的文本
                await self._process_streaming_text(combined_text)
                self.stream_text_buffer = []

    async def _process_streaming_text(self, text: str):
        """
        处理流式文本（内部方法）

        Args:
            text (str): 要处理的文本
        """
        if not self.stream_active or not self.ws_manager:
            return

        try:
            # 预处理文本，避免读出标点符号
            processed_text = TextPreprocessor.preprocess_text_for_tts(text)
            if self.debug:
                logger.debug(f"原始流式文本: {text[:30]}...")
                logger.debug(f"处理后流式文本: {processed_text[:30]}...")

            # 发送文本进行合成
            await self.ws_manager.send_text(processed_text, self.speaker, self.stream_session_id)

            # 接收音频数据
            received_audio = False
            while self.stream_active:
                if not self.ws_manager.ws:
                    break

                res = TTSProtocolProcessor.parse_response(await self.ws_manager.ws.recv())

                if (res.optional.event == TTSProtocolConstants.EVENT_TTS_RESPONSE and
                    res.header.message_type == TTSProtocolConstants.AUDIO_ONLY_RESPONSE):
                    # 将音频数据添加到播放队列
                    self.audio_player.add_audio_chunk(res.payload)
                    received_audio = True
                elif res.optional.event in [TTSProtocolConstants.EVENT_TTS_SENTENCE_START,
                                           TTSProtocolConstants.EVENT_TTS_SENTENCE_END]:
                    continue
                else:
                    # 非音频响应，结束接收
                    break

            if not received_audio:
                logger.warning(f"未收到音频数据，文本：{processed_text}")

        except Exception as e:
            logger.error(f"处理流式文本失败: {e}")

    async def _stream_processing_worker(self):
        """流式处理工作线程（内部方法）"""
        try:
            while self.stream_active:
                # 设置处理事件，等待文本添加
                self.stream_processing_event.clear()

                # 等待新文本或超时
                try:
                    await asyncio.wait_for(self.stream_processing_event.wait(), timeout=1.0)
                except asyncio.TimeoutError:
                    # 超时检查是否有待处理的文本
                    async with self.stream_lock:
                        if self.stream_text_buffer:
                            combined_text = ''.join(self.stream_text_buffer)
                            if combined_text.strip():
                                await self._process_streaming_text(combined_text)
                                self.stream_text_buffer = []
                    continue

        except asyncio.CancelledError:
            logger.debug("流式处理工作线程被取消")
        except Exception as e:
            logger.error(f"流式处理工作线程异常: {e}")

    async def _cleanup_streaming(self):
        """清理流式合成状态（内部方法）"""
        self.stream_active = False
        self.stream_session_id = None
        self.stream_text_buffer = []
        self.stream_worker_task = None
        self.ws_manager = None


# ==================== TTS工厂类 ====================
class TTSFactory:
    """TTS工厂类 - 负责创建不同类型的TTS客户端"""

    @staticmethod
    def create_simple_client(app_id: str, token: str, speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts') -> SimpleTTSClient:
        """
        创建简单TTS客户端

        Args:
            app_id (str): 应用ID
            token (str): 访问令牌
            speaker (str): 发音人

        Returns:
            SimpleTTSClient: 简单TTS客户端实例
        """
        return SimpleTTSClient(app_id, token, speaker)

    @staticmethod
    def create_advanced_manager(app_id: str = "5311525929", token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
                               speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts') -> AdvancedTTSManager:
        """
        创建高级TTS管理器

        Args:
            app_id (str): 应用ID
            token (str): 访问令牌
            speaker (str): 发音人

        Returns:
            AdvancedTTSManager: 高级TTS管理器实例
        """
        return AdvancedTTSManager(app_id, token, speaker)

    @staticmethod
    def create_default_manager() -> AdvancedTTSManager:
        """
        创建默认配置的TTS管理器

        Returns:
            AdvancedTTSManager: 使用默认配置的TTS管理器
        """
        return AdvancedTTSManager()


# ==================== 便捷函数 ====================
def create_tts_client(client_type: str = "advanced", app_id: str = "5311525929",
                     token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
                     speaker: str = 'zh_female_shuangkuaisisi_moon_bigtts') -> BaseTTSClient:
    """
    便捷函数：创建TTS客户端

    Args:
        client_type (str): 客户端类型 ("simple", "advanced")
        app_id (str): 应用ID
        token (str): 访问令牌
        speaker (str): 发音人

    Returns:
        BaseTTSClient: TTS客户端实例
    """
    if client_type == "simple":
        return TTSFactory.create_simple_client(app_id, token, speaker)
    elif client_type == "advanced":
        return TTSFactory.create_advanced_manager(app_id, token, speaker)
    else:
        raise ValueError(f"不支持的客户端类型: {client_type}")


def quick_tts_play(text: str, app_id: str = "5311525929", token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23") -> bool:
    """
    快速TTS播放函数

    Args:
        text (str): 要转换的文本
        app_id (str): 应用ID
        token (str): 访问令牌

    Returns:
        bool: 是否成功播放
    """
    try:
        client = create_tts_client("simple", app_id, token)
        return client.text_to_speech_play(text)
    except Exception as e:
        logger.error(f"快速TTS播放失败: {e}")
        return False


def quick_tts_file(text: str, output_path: str, app_id: str = "5311525929",
                  token: str = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23") -> bool:
    """
    快速TTS文件保存函数

    Args:
        text (str): 要转换的文本
        output_path (str): 输出文件路径
        app_id (str): 应用ID
        token (str): 访问令牌

    Returns:
        bool: 是否成功保存
    """
    try:
        client = create_tts_client("simple", app_id, token)
        return client.text_to_speech_file(text, output_path)
    except Exception as e:
        logger.error(f"快速TTS文件保存失败: {e}")
        return False


# ==================== 主要导出类和函数 ====================
__all__ = [
    # 常量类
    'TTSProtocolConstants',

    # 文本预处理
    'TextPreprocessor',

    # 协议处理
    'TTSProtocolHeader',
    'TTSProtocolOptional',
    'TTSProtocolResponse',
    'TTSProtocolProcessor',

    # 音频播放器
    'BaseAudioPlayer',
    'PygameAudioPlayer',
    'PydubAudioPlayer',
    'AudioPlayerFactory',

    # WebSocket管理
    'WebSocketManager',

    # TTS客户端
    'BaseTTSClient',
    'SimpleTTSClient',
    'AdvancedTTSManager',

    # 工厂类
    'TTSFactory',

    # 便捷函数
    'create_tts_client',
    'quick_tts_play',
    'quick_tts_file',
]


# ==================== 示例用法 ====================
if __name__ == "__main__":
    import os
    from pathlib import Path
    import sys

    # 添加项目根目录到Python路径
    sys.path.insert(0, str(Path(__file__).parent.parent))

    # 示例1：使用简单TTS客户端
    print("=== 简单TTS客户端示例 ===")
    simple_client = TTSFactory.create_simple_client("5311525929", "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23")
    simple_client.text_to_speech_file("你好，这是简单TTS客户端测试。", "simple_output.mp3")
    simple_client.text_to_speech_play("这是简单TTS客户端播放测试。")

    # 示例2：使用高级TTS管理器
    print("\n=== 高级TTS管理器示例 ===")
    advanced_manager = TTSFactory.create_advanced_manager()
    advanced_manager.text_to_speech_file("你好，这是高级TTS管理器测试。", "advanced_output.mp3")
    advanced_manager.text_to_speech_play("这是高级TTS管理器播放测试。")

    # 示例3：使用便捷函数
    print("\n=== 便捷函数示例 ===")
    quick_tts_file("这是便捷函数文件保存测试。", "quick_output.mp3")
    quick_tts_play("这是便捷函数播放测试。")

    # 示例4：流式语音合成（异步使用方式）
    print("\n=== 流式语音合成示例 ===")
    async def stream_example():
        manager = TTSFactory.create_advanced_manager()
        await manager.start_streaming_synthesis()

        # 模拟逐字发送文本
        text = "这是一个流式语音合成的测试，可以逐字逐句地处理文本并进行播放。"
        for i in range(0, len(text), 2):
            chunk = text[i:i+2]
            await manager.add_streaming_text(chunk)
            await asyncio.sleep(0.2)  # 模拟文本生成的间隔

        # 等待所有文本处理完成
        await asyncio.sleep(2)

        # 停止流式合成
        await manager.stop_streaming_synthesis()

    # 运行异步示例（取消注释以测试）
    # asyncio.run(stream_example())

    print("\n=== 所有示例完成 ===")
