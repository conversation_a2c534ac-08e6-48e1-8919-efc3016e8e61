# Database package

# SQLite连接相关导入
from core.user_management.database.connection import (
    SQLiteManager,
    get_sqlite_manager,
    get_db_session,
    get_db_session_context,
    check_db_connection,
    init_db
)

# InfluxDB连接相关导入
from core.planning.database.influxdb_connection import (
    InfluxDBManager,
    get_influxdb_manager
)

# 小孩和家长模型导入
from core.user_management.models.user_models import (
    Base,
    Parent,
    Child,
    ChildParentRelationship,
    ChildAcademicRecord
)

# 每日任务模型导入
from core.daily_tasks.models.daily_task_models import (
    DailyTask,
    TaskItem,
    TaskStatusEnum,
    TaskTypeEnum
)

# 枚举类型导入
from core.user_management.schemas import (
    GenderEnum,
    RelationshipEnum,
    AcademicLevelEnum,
    BehaviorLevelEnum
)

__all__ = [
    # SQLite
    "SQLiteManager",
    "get_sqlite_manager",
    "get_db_session",
    "get_db_session_context",
    "check_db_connection",
    "init_db",
    # InfluxDB
    "InfluxDBManager",
    "get_influxdb_manager",
    # 数据模型
    "Base",
    "Parent",
    "Child",
    "ChildParentRelationship",
    "ChildAcademicRecord",
    "DailyTask",
    "TaskItem",
    # 枚举类型
    "GenderEnum",
    "RelationshipEnum",
    "AcademicLevelEnum",
    "BehaviorLevelEnum",
    "TaskStatusEnum",
    "TaskTypeEnum"
]

# Models package

# 只导入存在的模块
try:
    from .camera_models import *
except ImportError:
    pass

try:
    from .image_processing_models import *
except ImportError:
    pass

# 尝试导入其他模块（如果存在）
try:
    from .sqlite_connection import (
        SQLiteManager,
        get_sqlite_manager,
        get_db_session,
        get_db_session_context,
        check_db_connection,
        init_db
    )
except ImportError:
    pass

try:
    from .influxdb_connection import (
        InfluxDBManager,
        get_influxdb_manager
    )
except ImportError:
    pass

try:
    from .child_models import (
        Base,
        Parent,
        Child,
        ChildParentRelationship,
        ChildAcademicRecord,
        GenderEnum,
        RelationshipEnum,
        AcademicLevelEnum,
        BehaviorLevelEnum
    )
except ImportError:
    pass
