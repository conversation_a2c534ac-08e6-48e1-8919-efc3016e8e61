"""
摄像头相关的数据模型
定义摄像头操作的请求和响应模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class RotationAngle(str, Enum):
    """旋转角度枚举"""
    ROTATE_90 = "90"
    ROTATE_180 = "180"
    ROTATE_270 = "270"


class CameraStatus(str, Enum):
    """摄像头状态枚举"""
    AVAILABLE = "available"
    CONNECTED = "connected"
    STREAMING = "streaming"
    IN_USE = "in_use"
    ERROR = "error"
    DISCONNECTED = "disconnected"


class StreamType(str, Enum):
    """流类型枚举"""
    WEBRTC = "webrtc"
    WEBSOCKET = "websocket"
    HTTP_STREAM = "http_stream"


class CameraInfo(BaseModel):
    """摄像头信息模型"""
    camera_id: str = Field(..., description="摄像头ID")
    name: str = Field(..., description="摄像头名称")
    status: CameraStatus = Field(default=CameraStatus.AVAILABLE, description="摄像头状态")
    resolution: Optional[str] = Field(None, description="分辨率，如 '1920x1080'")
    client_id: Optional[str] = Field(None, description="客户端ID")
    stream_type: Optional[StreamType] = Field(None, description="流类型")
    is_default: bool = Field(False, description="是否为默认摄像头")


class CameraListResponse(BaseModel):
    """摄像头列表响应模型"""
    cameras: List[CameraInfo] = Field(..., description="摄像头列表")
    total_count: int = Field(..., description="摄像头总数")
    available_count: int = Field(..., description="可用摄像头数量")


class CameraRegisterRequest(BaseModel):
    """摄像头注册请求模型"""
    client_id: str = Field(..., description="客户端ID")
    cameras: List[Dict[str, Any]] = Field(..., description="摄像头列表")


class CameraStartRequest(BaseModel):
    """启动摄像头请求模型"""
    camera_id: Optional[int] = Field(None, description="摄像头ID，不指定则使用默认摄像头")
    resolution: Optional[str] = Field("640x480", description="分辨率，如 '640x480'")


class CameraRotateRequest(BaseModel):
    """旋转摄像头画面请求模型"""
    angle: RotationAngle = Field(..., description="旋转角度")


class CameraZoomRequest(BaseModel):
    """缩放摄像头画面请求模型"""
    zoom_factor: float = Field(..., ge=0.1, le=5.0, description="缩放因子，0.1-5.0之间")


class CameraResponse(BaseModel):
    """摄像头操作响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class CameraStatusResponse(BaseModel):
    """摄像头状态响应模型"""
    camera_id: Optional[int] = Field(None, description="当前使用的摄像头ID")
    is_active: bool = Field(..., description="摄像头是否激活")
    current_rotation: int = Field(0, description="当前旋转角度")
    current_zoom: float = Field(1.0, description="当前缩放因子")
    resolution: Optional[str] = Field(None, description="当前分辨率")
    frame_rate: Optional[float] = Field(None, description="帧率")


class SessionCreateRequest(BaseModel):
    """创建会话请求模型"""
    client_id: str = Field(..., description="客户端ID")
    camera_id: str = Field(..., description="摄像头ID")


class SessionResponse(BaseModel):
    """会话响应模型"""
    success: bool = Field(..., description="操作是否成功")
    session_id: Optional[str] = Field(None, description="会话ID")
    message: str = Field(..., description="响应消息")


class FrameUpdateRequest(BaseModel):
    """画面更新请求模型"""
    session_id: str = Field(..., description="会话ID")
    frame_data: str = Field(..., description="Base64编码的画面数据")
    test_mode: Optional[bool] = Field(False, description="测试模式，不保存数据")


class TransformRequest(BaseModel):
    """画面变换请求模型"""
    session_id: str = Field(..., description="会话ID")
    rotation: Optional[int] = Field(None, description="旋转角度")
    zoom: Optional[float] = Field(None, description="缩放因子")


class CameraCaptureRequest(BaseModel):
    """图像捕获请求模型"""
    session_id: str = Field(..., description="会话ID")
    save_path: Optional[str] = Field(None, description="保存路径，不指定则使用默认路径")
    filename: Optional[str] = Field(None, description="文件名，不指定则使用北京时间自动生成")
    apply_transforms: bool = Field(True, description="是否应用当前的变换设置（旋转、缩放等）")


class CameraCaptureResponse(BaseModel):
    """图像捕获响应模型"""
    success: bool = Field(..., description="捕获是否成功")
    message: str = Field(..., description="响应消息")
    file_path: Optional[str] = Field(None, description="保存的文件完整路径")
    filename: Optional[str] = Field(None, description="保存的文件名")
    capture_time: Optional[str] = Field(None, description="捕获时间（ISO格式）")
    image_size: Optional[str] = Field(None, description="图像尺寸，如 '640x480'")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")


class FrameCaptureResponse(BaseModel):
    """画面捕获响应模型"""
    success: bool = Field(..., description="捕获是否成功")
    image_data: Optional[str] = Field(None, description="Base64编码的图像数据")
    timestamp: str = Field(..., description="捕获时间戳")
    format: str = Field("jpeg", description="图像格式")


class CameraHealthResponse(BaseModel):
    """摄像头健康检查响应模型"""
    service_status: str = Field(..., description="服务状态")
    total_cameras: int = Field(..., description="总摄像头数量")
    active_sessions: int = Field(..., description="活跃会话数量")
    timestamp: str = Field(..., description="检查时间戳")
    # 保留原有字段以保持兼容性
    camera_available: Optional[bool] = Field(None, description="摄像头是否可用")
    opencv_version: Optional[str] = Field(None, description="OpenCV版本")
