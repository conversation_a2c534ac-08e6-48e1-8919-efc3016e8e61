"""
服务器端摄像头管理工具类
管理客户端摄像头连接、会话和数据处理
"""
import base64
import threading
import uuid
import os
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from io import BytesIO
from PIL import Image
import pytz

from backend.utils.logging import get_camera_logger

# 导入数据模型
from backend.models.camera_models import CameraStatus

# 摄像头信息类
class CameraInfo:
    """客户端摄像头信息类"""
    def __init__(self, camera_id: str, name: str, status: CameraStatus,
                 resolution: str = None, client_id: str = None, stream_type: str = None):
        self.camera_id = camera_id
        self.name = name
        self.status = status
        self.resolution = resolution
        self.client_id = client_id
        self.stream_type = stream_type
        self.connected_at = datetime.now(timezone.utc)

# 客户端摄像头会话类
class CameraSession:
    """客户端摄像头会话"""
    def __init__(self, session_id: str, client_id: str, camera_info: CameraInfo):
        self.session_id = session_id
        self.client_id = client_id
        self.camera_info = camera_info
        self.created_at = datetime.now(timezone.utc)
        self.last_frame_time = None
        self.last_frame_data = None
        self.is_active = False
        self.current_rotation = 0
        self.current_zoom = 1.0
        self.settings = {
            "resolution": "640x480",
            "fps": 30,
            "quality": 80
        }

logger = get_camera_logger()


class ServerCameraManager:
    """服务器端摄像头管理器类"""
    
    def __init__(self):
        # 管理客户端摄像头会话
        self.sessions: Dict[str, CameraSession] = {}
        self.client_cameras: Dict[str, List[CameraInfo]] = {}  # client_id -> cameras
        self._lock = threading.Lock()
        
        logger.info("服务器端摄像头管理器初始化完成")
    
    def register_client_cameras(self, client_id: str, cameras_data: List[Dict[str, Any]]) -> bool:
        """
        注册客户端摄像头列表
        
        Args:
            client_id: 客户端ID
            cameras_data: 摄像头数据列表
            
        Returns:
            bool: 注册是否成功
        """
        try:
            with self._lock:
                cameras = []
                for cam_data in cameras_data:
                    camera_info = CameraInfo(
                        camera_id=cam_data.get("camera_id", "0"),
                        name=cam_data.get("name", "Unknown Camera"),
                        status=CameraStatus.AVAILABLE,
                        resolution=cam_data.get("resolution", "640x480"),
                        client_id=client_id,
                        stream_type=cam_data.get("stream_type", "websocket")
                    )
                    cameras.append(camera_info)
                
                self.client_cameras[client_id] = cameras
                logger.info(f"客户端 {client_id} 注册了 {len(cameras)} 个摄像头")
                return True
                
        except Exception as e:
            logger.error(f"注册客户端摄像头失败: {e}")
            return False
    
    def get_all_cameras(self) -> List[CameraInfo]:
        """获取所有已注册的摄像头"""
        cameras = []
        with self._lock:
            for client_cameras in self.client_cameras.values():
                cameras.extend(client_cameras)
        return cameras
    
    def get_client_cameras(self, client_id: str) -> List[CameraInfo]:
        """获取指定客户端的摄像头"""
        with self._lock:
            return self.client_cameras.get(client_id, [])
    
    def create_camera_session(self, client_id: str, camera_id: str) -> Optional[str]:
        """
        创建摄像头会话

        Args:
            client_id: 客户端ID
            camera_id: 摄像头ID

        Returns:
            Optional[str]: 会话ID，失败返回None
        """
        try:
            with self._lock:
                # 查找摄像头
                camera_info = None
                client_cameras = self.client_cameras.get(client_id, [])
                for cam in client_cameras:
                    if cam.camera_id == camera_id:
                        camera_info = cam
                        break

                if not camera_info:
                    logger.error(f"未找到摄像头: client_id={client_id}, camera_id={camera_id}")
                    return None

                # 检查摄像头是否已被占用
                if camera_info.status == CameraStatus.CONNECTED:
                    logger.warning(f"摄像头已被占用: camera_id={camera_id}")
                    return None

                # 创建会话
                session_id = str(uuid.uuid4())
                session = CameraSession(session_id, client_id, camera_info)
                session.is_active = True  # 激活会话
                self.sessions[session_id] = session

                # 更新摄像头状态
                camera_info.status = CameraStatus.CONNECTED

                logger.info(f"创建摄像头会话成功: session_id={session_id}, client_id={client_id}, camera_id={camera_id}")
                return session_id

        except Exception as e:
            logger.error(f"创建摄像头会话失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None
    
    def close_camera_session(self, session_id: str) -> bool:
        """关闭摄像头会话"""
        try:
            with self._lock:
                session = self.sessions.get(session_id)
                if session:
                    session.camera_info.status = CameraStatus.AVAILABLE
                    session.is_active = False
                    del self.sessions[session_id]
                    logger.info(f"关闭摄像头会话: {session_id}")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"关闭摄像头会话失败: {e}")
            return False
    
    def update_session_frame(self, session_id: str, frame_data: str) -> bool:
        """
        更新会话的画面数据

        Args:
            session_id: 会话ID
            frame_data: Base64编码的画面数据

        Returns:
            bool: 更新是否成功
        """
        try:
            with self._lock:
                session = self.sessions.get(session_id)
                if session:
                    session.last_frame_data = frame_data
                    session.last_frame_time = datetime.now(timezone.utc)
                    session.camera_info.status = CameraStatus.STREAMING
                    session.is_active = True  # 确保会话处于活跃状态
                    logger.debug(f"更新会话 {session_id} 画面数据，长度: {len(frame_data)}")
                    return True
                else:
                    logger.warning(f"尝试更新不存在的会话: {session_id}")
                    return False

        except Exception as e:
            logger.error(f"更新会话画面失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
    
    def get_session_frame(self, session_id: str) -> Optional[str]:
        """获取会话的最新画面（原始数据）"""
        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                return session.last_frame_data
            return None

    def get_session_frame_with_transforms(self, session_id: str) -> Optional[str]:
        """获取会话的最新画面（应用变换后）"""
        frame_data = self.get_session_frame(session_id)
        if frame_data:
            return self.apply_transformations(session_id, frame_data)
        return None

    def apply_transformations(self, session_id: str, frame_data: str) -> Optional[str]:
        """
        应用画面变换（旋转、缩放等）

        Args:
            session_id: 会话ID
            frame_data: Base64编码的原始画面

        Returns:
            Optional[str]: 变换后的Base64画面数据
        """
        try:
            with self._lock:
                session = self.sessions.get(session_id)
                if not session:
                    logger.warning(f"会话不存在: {session_id}")
                    return None

                # 获取当前变换参数
                current_rotation = session.current_rotation
                current_zoom = session.current_zoom
                quality = session.settings.get("quality", 80)

            # 检查是否需要变换
            if current_rotation == 0 and current_zoom == 1.0:
                logger.debug(f"会话 {session_id} 无需变换")
                return frame_data

            # 解码图像
            try:
                image_data = base64.b64decode(frame_data)
                image = Image.open(BytesIO(image_data))
            except Exception as e:
                logger.error(f"图像解码失败: {e}")
                return frame_data

            # 应用旋转
            if current_rotation != 0:
                try:
                    image = image.rotate(-current_rotation, expand=True)
                    logger.debug(f"应用旋转: {current_rotation}°")
                except Exception as e:
                    logger.error(f"旋转变换失败: {e}")

            # 应用缩放
            if current_zoom != 1.0:
                try:
                    width, height = image.size
                    new_width = int(width * current_zoom)
                    new_height = int(height * current_zoom)
                    image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    logger.debug(f"应用缩放: {current_zoom}x ({width}x{height} -> {new_width}x{new_height})")
                except Exception as e:
                    logger.error(f"缩放变换失败: {e}")

            # 重新编码
            try:
                buffer = BytesIO()
                image.save(buffer, format='JPEG', quality=quality)
                transformed_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

                logger.debug(f"变换完成: session_id={session_id}, 原始长度={len(frame_data)}, 变换后长度={len(transformed_data)}")
                return transformed_data
            except Exception as e:
                logger.error(f"图像编码失败: {e}")
                return frame_data

        except Exception as e:
            logger.error(f"应用画面变换失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return frame_data  # 返回原始数据

    def set_session_rotation(self, session_id: str, angle: int) -> bool:
        """设置会话的旋转角度"""
        if angle not in [0, 90, 180, 270]:
            logger.warning(f"无效的旋转角度: {angle}，只支持 0, 90, 180, 270")
            return False

        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                session.current_rotation = angle
                logger.debug(f"设置会话 {session_id} 旋转角度: {angle}°")
                return True
            else:
                logger.warning(f"设置旋转失败，会话不存在: {session_id}")
                return False

    def set_session_zoom(self, session_id: str, zoom_factor: float) -> bool:
        """设置会话的缩放因子"""
        if not (0.1 <= zoom_factor <= 5.0):
            logger.warning(f"无效的缩放因子: {zoom_factor}，范围应为 0.1-5.0")
            return False

        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                session.current_zoom = zoom_factor
                logger.debug(f"设置会话 {session_id} 缩放因子: {zoom_factor}x")
                return True
            else:
                logger.warning(f"设置缩放失败，会话不存在: {session_id}")
                return False

    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话状态"""
        with self._lock:
            session = self.sessions.get(session_id)
            if session:
                return {
                    "session_id": session_id,
                    "client_id": session.client_id,
                    "camera_id": session.camera_info.camera_id,
                    "camera_name": session.camera_info.name,
                    "status": session.camera_info.status,
                    "is_active": session.is_active,
                    "current_rotation": session.current_rotation,
                    "current_zoom": session.current_zoom,
                    "resolution": session.camera_info.resolution,
                    "last_frame_time": session.last_frame_time.isoformat() if session.last_frame_time else None,
                    "settings": session.settings
                }
            return None

    def cleanup_inactive_sessions(self, timeout_seconds: int = 300):
        """清理不活跃的会话"""
        current_time = datetime.now(timezone.utc)
        inactive_sessions = []

        with self._lock:
            for session_id, session in self.sessions.items():
                if session.last_frame_time:
                    time_diff = (current_time - session.last_frame_time).total_seconds()
                    if time_diff > timeout_seconds:
                        inactive_sessions.append(session_id)

        for session_id in inactive_sessions:
            self.close_camera_session(session_id)
            logger.info(f"清理不活跃会话: {session_id}")

    def capture_image(self, session_id: str, save_path: Optional[str] = None,
                     filename: Optional[str] = None, apply_transforms: bool = True) -> Dict[str, Any]:
        """
        捕获并保存图像

        Args:
            session_id: 会话ID
            save_path: 保存路径，不指定则使用默认路径 'captured_images'
            filename: 文件名，不指定则使用北京时间自动生成
            apply_transforms: 是否应用当前的变换设置（旋转、缩放等）

        Returns:
            Dict[str, Any]: 包含捕获结果的字典
        """
        try:
            # 检查会话是否存在
            with self._lock:
                session = self.sessions.get(session_id)
                if not session:
                    return {
                        "success": False,
                        "message": f"会话不存在: {session_id}",
                        "error_code": "SESSION_NOT_FOUND"
                    }

                if not session.last_frame_data:
                    return {
                        "success": False,
                        "message": "会话中没有可用的画面数据",
                        "error_code": "NO_FRAME_DATA"
                    }

            # 获取画面数据
            if apply_transforms:
                frame_data = self.apply_transformations(session_id, session.last_frame_data)
                if not frame_data:
                    frame_data = session.last_frame_data
                    logger.warning(f"变换应用失败，使用原始画面数据")
            else:
                frame_data = session.last_frame_data

            # 设置默认保存路径
            if not save_path:
                save_path = "captured_images"

            # 确保保存目录存在
            os.makedirs(save_path, exist_ok=True)

            # 生成文件名（使用北京时间）
            if not filename:
                beijing_tz = pytz.timezone('Asia/Shanghai')
                beijing_time = datetime.now(beijing_tz)
                filename = f"capture_{beijing_time.strftime('%Y%m%d_%H%M%S')}.jpg"

            # 确保文件名有正确的扩展名
            if not filename.lower().endswith(('.jpg', '.jpeg', '.png')):
                filename += '.jpg'

            # 完整文件路径
            file_path = os.path.join(save_path, filename)

            # 解码并保存图像
            try:
                image_data = base64.b64decode(frame_data)
                image = Image.open(BytesIO(image_data))

                # 保存图像
                image.save(file_path, format='JPEG', quality=90)

                # 获取文件信息
                file_size = os.path.getsize(file_path)
                image_size = f"{image.width}x{image.height}"
                capture_time = datetime.now(timezone.utc).isoformat()

                logger.info(f"图像捕获成功: {file_path}, 尺寸: {image_size}, 大小: {file_size} bytes")

                return {
                    "success": True,
                    "message": "图像捕获成功",
                    "file_path": file_path,
                    "filename": filename,
                    "capture_time": capture_time,
                    "image_size": image_size,
                    "file_size": file_size
                }

            except Exception as e:
                logger.error(f"图像保存失败: {e}")
                return {
                    "success": False,
                    "message": f"图像保存失败: {str(e)}",
                    "error_code": "SAVE_FAILED"
                }

        except Exception as e:
            logger.error(f"图像捕获过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "success": False,
                "message": f"图像捕获失败: {str(e)}",
                "error_code": "CAPTURE_FAILED"
            }


# 全局摄像头管理器实例
_camera_manager_instance: Optional[ServerCameraManager] = None


def get_camera_manager() -> ServerCameraManager:
    """
    获取摄像头管理器单例

    Returns:
        ServerCameraManager: 摄像头管理器实例
    """
    global _camera_manager_instance
    if _camera_manager_instance is None:
        _camera_manager_instance = ServerCameraManager()
    return _camera_manager_instance
