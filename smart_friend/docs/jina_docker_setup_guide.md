# Jina Embedding v4 Docker Setup Guide

## Overview

This guide explains how to set up and use Jina Embedding v4 with Docker containers instead of API calls. This approach provides:

- **No API costs** - Run embeddings locally
- **Better performance** - No network latency
- **Privacy** - Data stays on your machine
- **Reliability** - No external API dependencies

## Prerequisites

### 1. Docker Installation

Install Docker on your system:

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install docker.io docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

**macOS:**
```bash
brew install docker docker-compose
# Or download Docker Desktop from https://docker.com
```

**Windows:**
Download and install Docker Desktop from https://docker.com

### 2. System Requirements

- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 2GB+ free space for Docker images
- **CPU**: Multi-core recommended for better performance

## Quick Start

### Method 1: Using Docker Compose (Recommended)

1. **Start the services:**
```bash
cd smart_friend
docker-compose up -d jina-embeddings
```

2. **Check if it's running:**
```bash
docker-compose ps
curl http://localhost:8080/health
```

3. **Test embedding generation:**
```bash
curl -X POST http://localhost:8080/encode \
  -H "Content-Type: application/json" \
  -d '{"data": [{"text": "Hello world"}]}'
```

### Method 2: Using Setup Script

1. **Run the setup script:**
```bash
cd smart_friend
python scripts/setup_jina_docker.py setup
```

2. **Follow the prompts:**
   - Choose Jina version (v1/v2/v3)
   - Enable GPU acceleration (if available)

### Method 3: Manual Docker Commands

1. **Pull the image:**
```bash
docker pull jinaai/jina-embeddings-v3:latest
```

2. **Run the container:**
```bash
docker run -d \
  --name jina-embeddings-v4 \
  -p 8080:8080 \
  --restart unless-stopped \
  --memory=4g \
  jinaai/jina-embeddings-v3:latest
```

3. **Wait for startup and test:**
```bash
sleep 10
curl http://localhost:8080/health
```

## Configuration

### Environment Variables

Set these in your environment or `.env` file:

```bash
# Jina Docker Configuration
JINA_DOCKER_HOST=localhost
JINA_DOCKER_PORT=8080

# System Configuration
USE_MOCK_MODE=false
ENABLE_EMBEDDING_CACHE=true
CACHE_EXPIRY_HOURS=24
```

### Docker Compose Configuration

Edit `docker-compose.yml` to customize:

```yaml
services:
  jina-embeddings:
    image: jinaai/jina-embeddings-v3:latest
    ports:
      - "8080:8080"
    environment:
      - JINA_LOG_LEVEL=INFO
    deploy:
      resources:
        limits:
          memory: 4G
```

## Available Jina Models

| Model | Docker Image | Dimensions | Use Case |
|-------|-------------|------------|----------|
| Jina v3 | `jinaai/jina-embeddings-v3:latest` | 768 | Latest, best performance |
| Jina v2 | `jinaai/jina-embeddings-v2:latest` | 768 | Stable, good performance |
| Jina v1 | `jinaai/jina-embeddings-v1:latest` | 512 | Legacy support |

## GPU Support (Optional)

For better performance with GPU:

1. **Install NVIDIA Docker:**
```bash
# Ubuntu/Debian
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt-get update && sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

2. **Use GPU image:**
```bash
docker run -d \
  --name jina-embeddings-v4-gpu \
  --gpus all \
  -p 8080:8080 \
  jinaai/jina-embeddings-v3:latest-gpu
```

## Usage in Code

### Basic Usage

```python
from openmanus import initialize_system

# Initialize with Docker auto-start
planner = initialize_system(auto_start_docker=True)

# Generate embeddings
texts = ["Hello world", "How are you?"]
embeddings = planner.embedding_client.get_embeddings(texts)
print(f"Generated {len(embeddings)} embeddings")
```

### Manual Container Management

```python
from openmanus import JinaEmbeddingClient

client = JinaEmbeddingClient()

# Start container
if client.start_docker_container():
    print("Container started successfully")
    
    # Generate embeddings
    embedding = client.get_single_embedding("Test text")
    print(f"Embedding dimension: {len(embedding)}")
    
    # Stop container when done
    client.stop_docker_container()
```

### With Caching

The system automatically caches embeddings to improve performance:

```python
# First call - generates embedding
embedding1 = client.get_single_embedding("Hello world")

# Second call - retrieved from cache
embedding2 = client.get_single_embedding("Hello world")

# Clear expired cache
client.clear_expired_cache()
```

## Management Commands

### Using Setup Script

```bash
# Full setup
python scripts/setup_jina_docker.py setup

# Start container
python scripts/setup_jina_docker.py start v3

# Stop container
python scripts/setup_jina_docker.py stop

# Check status
python scripts/setup_jina_docker.py status

# Test embedding
python scripts/setup_jina_docker.py test

# View logs
python scripts/setup_jina_docker.py logs
```

### Using Docker Compose

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs jina-embeddings

# Restart service
docker-compose restart jina-embeddings
```

### Manual Docker Commands

```bash
# Check running containers
docker ps

# View logs
docker logs jina-embeddings-v4

# Stop container
docker stop jina-embeddings-v4

# Remove container
docker rm jina-embeddings-v4

# Remove image
docker rmi jinaai/jina-embeddings-v3:latest
```

## Troubleshooting

### Common Issues

1. **Container won't start:**
   - Check if port 8080 is available: `netstat -tlnp | grep 8080`
   - Try different port: `docker run -p 8081:8080 ...`

2. **Out of memory:**
   - Reduce memory limit: `--memory=2g`
   - Close other applications
   - Use smaller batch sizes

3. **Health check fails:**
   - Wait longer for startup (can take 30-60 seconds)
   - Check container logs: `docker logs jina-embeddings-v4`

4. **Permission denied:**
   - Add user to docker group: `sudo usermod -aG docker $USER`
   - Restart terminal/system

### Debug Mode

Enable debug logging:

```bash
export DEBUG=1
python smart_friend/openmanus.py
```

### Container Health Check

```bash
# Check if container is healthy
curl -f http://localhost:8080/health

# Test embedding generation
curl -X POST http://localhost:8080/encode \
  -H "Content-Type: application/json" \
  -d '{"data": [{"text": "test"}]}'
```

## Performance Optimization

### 1. Memory Settings

```bash
# Increase container memory
docker run --memory=8g --memory-swap=8g ...
```

### 2. Batch Processing

```python
# Process texts in batches for better performance
batch_size = 32
for i in range(0, len(texts), batch_size):
    batch = texts[i:i+batch_size]
    embeddings.extend(client.get_embeddings(batch))
```

### 3. Caching

```python
# Enable caching for repeated texts
ENABLE_EMBEDDING_CACHE = True
CACHE_EXPIRY_HOURS = 24
```

## Security Considerations

1. **Network Access**: Container only accessible from localhost by default
2. **Data Privacy**: All processing happens locally
3. **Resource Limits**: Set memory and CPU limits to prevent resource exhaustion

## Integration with OpenManus

The enhanced OpenManus framework automatically:

1. **Detects** if Docker container is running
2. **Starts** container if needed
3. **Caches** embeddings for performance
4. **Fallbacks** to mock mode if container fails
5. **Manages** container lifecycle

## Next Steps

1. **Test the setup** with the provided test script
2. **Integrate** with your existing OpenManus workflow
3. **Monitor** performance and adjust settings as needed
4. **Scale** by running multiple containers if needed

For more advanced configurations and troubleshooting, see the main documentation.
