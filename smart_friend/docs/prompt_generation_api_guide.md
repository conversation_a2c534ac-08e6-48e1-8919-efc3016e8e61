# Prompt生成API使用指南

## 概述

Prompt生成API是一个整合功能，它将儿童个人档案、今日学习任务和近期学习表现数据合并成结构化的prompt，供后续的大模型使用。

## API端点

### 1. 生成Prompt (POST)

**端点**: `POST /api/v1/prompt-generation/generate`

**描述**: 通过POST请求生成综合学习prompt

**请求体**:
```json
{
  "child_id": 1,
  "days_back": 7,
  "include_today_homework": true,
  "include_recent_completion": true,
  "subject_filter": "数学",
  "prompt_template": "自定义模板（可选）"
}
```

**参数说明**:
- `child_id` (必填): 儿童ID
- `days_back` (可选): 回溯天数，获取近期学习数据（1-30天，默认7天）
- `include_today_homework` (可选): 是否包含今日作业（默认true）
- `include_recent_completion` (可选): 是否包含近期完成情况（默认true）
- `subject_filter` (可选): 学科筛选，只获取特定学科的数据
- `prompt_template` (可选): 自定义prompt模板

### 2. 生成Prompt (GET)

**端点**: `GET /api/v1/prompt-generation/generate/{child_id}`

**描述**: 通过GET请求快速生成prompt

**查询参数**:
- `days_back`: 回溯天数（默认7天）
- `include_today_homework`: 是否包含今日作业（默认true）
- `include_recent_completion`: 是否包含近期完成情况（默认true）
- `subject_filter`: 学科筛选（可选）

### 3. 数据预览

**端点**: `GET /api/v1/prompt-generation/preview/{child_id}`

**描述**: 预览将要使用的原始数据，用于调试和验证

**查询参数**:
- `days_back`: 回溯天数（默认7天）
- `subject_filter`: 学科筛选（可选）

## 响应格式

### 成功响应

```json
{
  "child_id": 1,
  "generated_at": "2024-01-15T10:30:00Z",
  "child_profile": {
    "id": 1,
    "name": "小明",
    "nickname": "明明",
    "age": 8,
    "academic_level": "小学二年级",
    "school_name": "实验小学",
    "learning_style": "视觉型学习者",
    "attention_span_minutes": 25,
    "personality_traits": "活泼好动，好奇心强",
    "favorite_subjects": "数学，科学",
    "disliked_subjects": "语文",
    "good_at_subjects": "数学，英语",
    "weak_at_subjects": "语文写作"
  },
  "today_homework": [
    {
      "plan_id": "plan_123",
      "subject": "数学",
      "task_name": "加减法练习",
      "time_slot": "19:00 - 19:30",
      "sub_tasks": ["口算练习", "应用题"],
      "difficulty": "计算速度需要提升",
      "status": "pending",
      "confidence_index": 4
    }
  ],
  "recent_completion": [
    {
      "date": "2024-01-14",
      "subject": "数学",
      "activity_type": "homework",
      "completion_rate": 95.0,
      "accuracy_rate": 88.0,
      "total_duration_minutes": 45.0,
      "concentration_level": 4,
      "enjoyment_rating": 4
    }
  ],
  "structured_prompt": "## 学生基本信息\n姓名: 小明\n昵称: 明明\n年龄: 8岁\n...",
  "prompt_sections": {
    "basic_info": {...},
    "learning_characteristics": {...},
    "subject_preferences": {...},
    "today_tasks": [...],
    "recent_performance": [...]
  },
  "data_summary": {
    "child_name": "小明",
    "today_homework_count": 1,
    "recent_records_count": 5,
    "avg_completion_rate": 92.5,
    "avg_accuracy_rate": 85.2,
    "total_study_time": 180.0
  }
}
```

## 使用示例

### Python示例

```python
import requests

# 基本用法
response = requests.post(
    "http://localhost:8001/api/v1/prompt-generation/generate",
    json={
        "child_id": 1,
        "days_back": 7
    }
)

if response.status_code == 200:
    result = response.json()
    print("生成的Prompt:")
    print(result["structured_prompt"])
    print(f"\n数据摘要: {result['data_summary']}")
else:
    print(f"错误: {response.json()}")

# 带筛选的用法
response = requests.get(
    "http://localhost:8001/api/v1/prompt-generation/generate/1",
    params={
        "days_back": 5,
        "subject_filter": "数学"
    }
)

# 数据预览
preview = requests.get(
    "http://localhost:8001/api/v1/prompt-generation/preview/1",
    params={"days_back": 7}
)
```

### curl示例

```bash
# POST方式生成prompt
curl -X POST "http://localhost:8001/api/v1/prompt-generation/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "child_id": 1,
    "days_back": 7,
    "subject_filter": "数学"
  }'

# GET方式生成prompt
curl "http://localhost:8001/api/v1/prompt-generation/generate/1?days_back=7&subject_filter=数学"

# 数据预览
curl "http://localhost:8001/api/v1/prompt-generation/preview/1?days_back=7"
```

## 自定义模板

你可以使用自定义模板来控制prompt的格式。模板支持以下变量：

- `{child_name}`: 儿童姓名
- `{child_nickname}`: 昵称
- `{child_age}`: 年龄
- `{academic_level}`: 学业等级
- `{school_name}`: 学校名称
- `{learning_style}`: 学习风格
- `{attention_span}`: 注意力持续时间
- `{personality_traits}`: 性格特点
- `{favorite_subjects}`: 喜欢的科目
- `{disliked_subjects}`: 不喜欢的科目
- `{good_at_subjects}`: 擅长的科目
- `{weak_at_subjects}`: 薄弱的科目
- `{today_homework_count}`: 今日作业数量
- `{recent_records_count}`: 近期记录数量

### 自定义模板示例

```json
{
  "child_id": 1,
  "prompt_template": "学生{child_name}（{child_age}岁）的学习情况：\n擅长：{good_at_subjects}\n薄弱：{weak_at_subjects}\n今日任务：{today_homework_count}项\n近期记录：{recent_records_count}条"
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `404`: 未找到指定儿童的数据
- `500`: 服务器内部错误
- `503`: InfluxDB服务不可用

### 错误响应示例

```json
{
  "detail": "未找到儿童ID 999 的相关数据或生成失败"
}
```

## 注意事项

1. **数据依赖**: 此API依赖于user_management和planning模块的数据
2. **性能考虑**: 大量历史数据可能影响响应时间，建议合理设置days_back参数
3. **数据完整性**: 如果某些数据缺失，API仍会生成prompt，但相应部分会为空
4. **缓存**: 目前不支持缓存，每次请求都会重新查询数据库
