#!/usr/bin/env python3
"""
豆包模型API测试脚本
测试生成计划表的prompt效果

使用方法:
python tests/test_doubao_prompt.py --child_id 1
"""

import json
import sys
import argparse
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from core.prompt_generation.service import PromptGenerationService
from core.prompt_generation.schemas import TaskPromptRequest


# 注意：DoubaoAPIClient类已被重构，现在使用新的DoubaoService
# 保留此类仅为兼容性，建议使用新的DoubaoService

class DoubaoAPIClient:
    """豆包模型API客户端（已重构，使用新的DoubaoService）"""

    def __init__(self):
        # 使用新的豆包服务
        from service.doubao_service import get_doubao_service
        self.doubao_service = get_doubao_service()

        # 保留原有属性以兼容现有代码
        self.api_key = self.doubao_service.api_key
        self.base_url = self.doubao_service.base_url
        self.model_name = self.doubao_service.model_name

    def call_doubao_api(self, prompt: str, max_tokens: int = 4000) -> Dict[str, Any]:
        """
        调用豆包模型API（重构版本）

        Args:
            prompt: 输入的prompt
            max_tokens: 最大token数

        Returns:
            API响应结果
        """
        try:
            print(f"🚀 正在调用豆包模型API...")
            print(f"📡 URL: {self.base_url}/chat/completions")
            print(f"🤖 模型: {self.model_name}")
            print(f"📝 Prompt长度: {len(prompt)} 字符")

            # 使用新的服务调用API
            result = self.doubao_service.simple_chat(
                prompt=prompt,
                max_tokens=max_tokens
            )

            if result.get("success"):
                print(f"✅ API调用成功!")
            else:
                print(f"❌ API调用失败: {result.get('error', '未知错误')}")

            return result

        except Exception as e:
            print(f"❌ API调用异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "response_text": ""
            }


class PromptTester:
    """Prompt测试器"""
    
    def __init__(self):
        self.doubao_client = DoubaoAPIClient()
        self.prompt_service = None
    
    def init_services(self):
        """初始化服务"""
        try:
            # PromptGenerationService不需要传入数据库会话
            self.prompt_service = PromptGenerationService()
            print("✅ 服务初始化成功")
        except Exception as e:
            print(f"❌ 服务初始化失败: {e}")
            raise
    
    def generate_prompt(self, child_id: int, days_back: int = 7) -> Optional[str]:
        """
        生成任务计划表prompt
        
        Args:
            child_id: 儿童ID
            days_back: 回溯天数
            
        Returns:
            生成的prompt字符串
        """
        try:
            print(f"📋 正在生成儿童ID {child_id} 的任务计划表prompt...")
            
            # 构建请求
            request = TaskPromptRequest(
                child_id=child_id,
                days_back=days_back,
                include_yesterday_tasks=True,
                template_type="task_prompt"
            )
            
            # 生成prompt
            result = self.prompt_service.generate_task_prompt(request)

            if result and result.final_prompt:
                print(f"✅ Prompt生成成功!")
                print(f"📊 Prompt长度: {len(result.final_prompt)} 字符")
                return result.final_prompt
            else:
                print(f"❌ Prompt生成失败: 未找到相关数据")
                return None
                
        except Exception as e:
            print(f"❌ Prompt生成异常: {str(e)}")
            return None
    
    def test_prompt_with_doubao(self, child_id: int, days_back: int = 7, save_results: bool = True):
        """
        测试prompt与豆包模型的效果
        
        Args:
            child_id: 儿童ID
            days_back: 回溯天数
            save_results: 是否保存结果到文件
        """
        print("=" * 80)
        print("🧪 豆包模型Prompt测试开始")
        print("=" * 80)
        
        # 1. 初始化服务
        self.init_services()
        
        # 2. 生成prompt
        prompt = self.generate_prompt(child_id, days_back)
        if not prompt:
            print("❌ 无法生成prompt，测试终止")
            return
        
        # 3. 调用豆包模型
        print("\n" + "=" * 50)
        print("🤖 调用豆包模型API")
        print("=" * 50)
        
        api_result = self.doubao_client.call_doubao_api(prompt)
        
        # 4. 展示结果
        print("\n" + "=" * 50)
        print("📊 测试结果")
        print("=" * 50)
        
        if api_result["success"]:
            print("✅ 豆包模型调用成功!")
            
            # 显示使用情况
            if api_result.get("usage"):
                usage = api_result["usage"]
                print(f"📈 Token使用情况:")
                print(f"   - 输入Token: {usage.get('prompt_tokens', 'N/A')}")
                print(f"   - 输出Token: {usage.get('completion_tokens', 'N/A')}")
                print(f"   - 总Token: {usage.get('total_tokens', 'N/A')}")
            
            # 显示生成的任务计划
            response_text = api_result["response_text"]
            print(f"\n🎯 生成的任务计划表:")
            print("-" * 50)
            print(response_text)
            print("-" * 50)
            
            # 尝试解析JSON格式
            self._analyze_response_format(response_text)
            
        else:
            print("❌ 豆包模型调用失败!")
            print(f"错误信息: {api_result['error']}")
        
        # 5. 保存结果
        if save_results:
            self._save_test_results(child_id, prompt, api_result)
        
        print("\n" + "=" * 80)
        print("🧪 测试完成")
        print("=" * 80)
    
    def _analyze_response_format(self, response_text: str):
        """分析响应格式"""
        print(f"\n🔍 响应格式分析:")
        
        # 检查是否包含JSON格式
        if "```json" in response_text.lower() or "[" in response_text:
            print("✅ 响应包含JSON格式内容")
            
            # 尝试提取和解析JSON
            try:
                # 简单的JSON提取逻辑
                start_idx = response_text.find("[")
                end_idx = response_text.rfind("]") + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = response_text[start_idx:end_idx]
                    parsed_json = json.loads(json_str)
                    print(f"✅ JSON解析成功，包含 {len(parsed_json)} 个任务")
                    
                    # 检查必要字段
                    required_fields = ["task_name", "time_slot", "subject"]
                    for i, task in enumerate(parsed_json):
                        missing_fields = [field for field in required_fields if field not in task]
                        if missing_fields:
                            print(f"⚠️  任务 {i+1} 缺少字段: {missing_fields}")
                        else:
                            print(f"✅ 任务 {i+1} 格式正确: {task.get('task_name', 'N/A')}")
                            
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
        else:
            print("⚠️  响应不包含JSON格式，可能需要调整prompt")
    
    def _save_test_results(self, child_id: int, prompt: str, api_result: Dict[str, Any]):
        """保存测试结果到文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"doubao_test_results_{child_id}_{timestamp}.json"
            filepath = Path("tests/results") / filename
            
            # 确保目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # 准备保存的数据
            test_data = {
                "test_info": {
                    "child_id": child_id,
                    "timestamp": timestamp,
                    "model_name": self.doubao_client.model_name
                },
                "prompt": prompt,
                "api_result": api_result
            }
            
            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 测试结果已保存到: {filepath}")
            
        except Exception as e:
            print(f"⚠️  保存测试结果失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="豆包模型Prompt测试")
    parser.add_argument("--child_id", type=int, required=True, help="儿童ID")
    parser.add_argument("--days_back", type=int, default=7, help="回溯天数 (默认7天)")
    parser.add_argument("--no_save", action="store_true", help="不保存测试结果")
    
    args = parser.parse_args()
    
    # 创建测试器并运行测试
    tester = PromptTester()
    tester.test_prompt_with_doubao(
        child_id=args.child_id,
        days_back=args.days_back,
        save_results=not args.no_save
    )


if __name__ == "__main__":
    main()
