#!/usr/bin/env python3
"""
真实ASR功能测试

使用真实的豆包(火山引擎)API密钥测试新的模块化ASR工具集
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from backend.utils.asr_utils import (
    ASRUtils,
    ASRClientFactory,
    ASRManager,
    ASRTextProcessor,
    ASRAudioProcessor
)


def test_asr_client_creation():
    """测试ASR客户端创建"""
    print("=" * 60)
    print("测试ASR客户端创建")
    print("=" * 60)
    
    try:
        # 使用真实的API密钥创建豆包ASR客户端
        print("\n1. 创建豆包ASR客户端...")
        client = ASRClientFactory.create_client(
            'doubao', 
            '5311525929', 
            'DRNTjbbfC1QcfDrTndiSSBdTr23F0-23'
        )
        print(f"   ✓ 客户端类型: {type(client).__name__}")
        print(f"   ✓ APP Key: {client.app_key}")
        print(f"   ✓ Access Key: {client.access_key[:10]}...")
        
        # 测试连接
        print("\n2. 测试连接到ASR服务...")
        if client.connect():
            print("   ✓ 成功连接到豆包ASR服务")
            
            # 测试断开连接
            if client.disconnect():
                print("   ✓ 成功断开ASR服务连接")
            else:
                print("   ✗ 断开连接失败")
        else:
            print("   ✗ 连接到ASR服务失败")
            
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")


def test_asr_manager_with_config():
    """测试使用配置文件的ASR管理器"""
    print("=" * 60)
    print("测试ASR管理器（使用配置文件）")
    print("=" * 60)
    
    try:
        # 使用配置文件创建ASR管理器
        print("\n1. 创建ASR管理器...")
        config_path = "backend/config/config.ini"
        manager = ASRManager(config_path=config_path)
        print(f"   ✓ ASR管理器创建成功")
        
        # 获取状态
        status = manager.get_recognition_status()
        print(f"   ✓ 初始状态: {status}")
        
        # 测试连接（不启动识别）
        print("\n2. 测试ASR服务连接...")
        if manager.asr_client:
            if manager.asr_client.connect():
                print("   ✓ ASR服务连接成功")
                
                # 断开连接
                if manager.asr_client.disconnect():
                    print("   ✓ ASR服务断开成功")
            else:
                print("   ✗ ASR服务连接失败")
        else:
            print("   ✗ ASR客户端未初始化")
            
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")


def test_text_processing():
    """测试文本处理功能"""
    print("=" * 60)
    print("测试文本处理功能")
    print("=" * 60)
    
    processor = ASRTextProcessor()
    
    # 测试TTS预处理
    print("\n1. TTS文本预处理测试:")
    test_texts = [
        "这是代码：```python\nprint('hello')\n```结束",
        "测试(括号)、顿号；分号：冒号",
        "Hello, world! How are you?",
        "数学：1+2=3，编程：a/b*c"
    ]
    
    for text in test_texts:
        result = processor.preprocess_text_for_tts(text)
        print(f"   原文本: {text}")
        print(f"   处理后: {result}")
        print()
    
    # 测试标点符号添加
    print("2. 智能标点符号添加测试:")
    test_texts = [
        "你好世界这是一个测试",
        "今天天气很好我们去公园吧",
        "请问现在几点了"
    ]
    
    for text in test_texts:
        result = processor.add_punctuation(text)
        print(f"   原文本: {text}")
        print(f"   添加标点后: {result}")
        print()


def test_audio_processing():
    """测试音频处理功能"""
    print("=" * 60)
    print("测试音频处理功能")
    print("=" * 60)
    
    import numpy as np
    
    processor = ASRAudioProcessor()
    
    # 测试静音检测
    print("\n1. 静音检测测试:")
    
    # 创建静音数据
    silence_data = np.zeros(1000, dtype=np.float32)
    is_silence = processor.is_silence(silence_data)
    print(f"   静音数据检测结果: {is_silence} (应该为True)")
    
    # 创建有声音的数据
    sound_data = np.random.normal(0, 0.8, 1000).astype(np.float32)
    is_silence = processor.is_silence(sound_data)
    print(f"   有声音数据检测结果: {is_silence} (应该为False)")
    
    # 测试音频格式转换
    print("\n2. 音频格式转换测试:")
    
    # numpy数组转换
    audio_data = np.random.normal(0, 0.5, 100).astype(np.float32)
    converted = processor.convert_audio_format(audio_data)
    print(f"   原始数据长度: {len(audio_data)}")
    print(f"   转换后长度: {len(converted)} bytes")
    print(f"   转换比例: {len(converted) / len(audio_data)} (应该为2.0)")
    
    # bytes数据转换
    audio_bytes = b"test_audio_data"
    converted_bytes = processor.convert_audio_format(audio_bytes)
    print(f"   bytes数据转换正确: {converted_bytes == audio_bytes}")


def test_asr_recognition_simulation():
    """测试ASR识别功能（模拟音频数据）"""
    print("=" * 60)
    print("测试ASR识别功能（模拟音频数据）")
    print("=" * 60)
    
    try:
        # 创建ASR管理器
        print("\n1. 创建ASR管理器...")
        config_path = "backend/config/config.ini"
        manager = ASRManager(config_path=config_path)
        
        # 识别结果收集
        recognition_results = []
        recognition_event = threading.Event()
        
        def on_recognition_result(text: str, is_final: bool):
            """识别结果回调函数"""
            if is_final:
                print(f"   ✓ 最终识别结果: {text}")
                recognition_results.append(text)
                recognition_event.set()
            else:
                print(f"   → 实时识别中: {text}")
        
        print("\n2. 启动语音识别...")
        if manager.start_recognition(callback=on_recognition_result):
            print("   ✓ 语音识别已启动")
            
            # 模拟发送音频数据
            print("\n3. 模拟发送音频数据...")
            import numpy as np
            
            # 发送一些模拟音频数据
            for i in range(5):
                # 创建模拟音频数据（有一些噪声）
                audio_data = np.random.normal(0, 0.3, 9600).astype(np.float32)  # 0.6秒的音频
                
                if manager.send_audio_data(audio_data):
                    print(f"   ✓ 发送音频块 {i+1}/5")
                else:
                    print(f"   ✗ 发送音频块 {i+1}/5 失败")
                
                time.sleep(0.7)  # 等待0.7秒
            
            # 等待识别结果（最多等待10秒）
            print("\n4. 等待识别结果...")
            if recognition_event.wait(timeout=10):
                print(f"   ✓ 收到识别结果: {len(recognition_results)} 个")
            else:
                print("   ⚠ 未收到识别结果（可能是音频数据不足或网络问题）")
            
            # 停止识别
            print("\n5. 停止语音识别...")
            if manager.stop_recognition():
                print("   ✓ 语音识别已停止")
            else:
                print("   ✗ 停止语音识别失败")
                
        else:
            print("   ✗ 启动语音识别失败")
            
    except Exception as e:
        print(f"   ✗ 测试失败: {e}")


def main():
    """主函数"""
    print("ASR Utils 真实功能测试")
    print("使用豆包(火山引擎)API密钥")
    print("=" * 80)
    
    # 运行各项测试
    test_text_processing()
    test_audio_processing()
    test_asr_client_creation()
    test_asr_manager_with_config()
    
    # 询问是否进行识别测试
    print("\n" + "=" * 80)
    print("注意：下面的测试将连接到真实的ASR服务并发送音频数据")
    print("这可能会消耗API配额。是否继续？(y/N): ", end="")
    
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            test_asr_recognition_simulation()
        else:
            print("跳过ASR识别测试")
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    
    print("\n" + "=" * 80)
    print("测试完成！")
    print("=" * 80)


if __name__ == "__main__":
    main()
