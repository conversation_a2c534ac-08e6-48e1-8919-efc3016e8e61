#!/usr/bin/env python3
"""
Test script for Enhanced OpenManus Framework
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from openmanus import (
    OpenManusPlanner,
    JinaEmbeddingClient,
    DatasetManager,
    IntentClassifier,
    SummaryReportManager,
    initialize_system
)

def test_jina_embeddings():
    """Test Jina embedding functionality"""
    print("🧪 Testing Jina Embeddings...")

    client = JinaEmbeddingClient()

    # Test Docker container health
    if not client.check_health():
        print("⚠️ Jina Docker container not running, attempting to start...")
        if client.start_docker_container():
            print("✅ Docker container started successfully")
        else:
            print("⚠️ Using mock mode for testing")
    else:
        print("✅ Jina Docker container is healthy")

    # Test single embedding
    text = "Hello, how are you today?"
    embedding = client.get_single_embedding(text)
    print(f"✅ Single embedding generated: {len(embedding)} dimensions")

    # Test batch embeddings
    texts = ["Good morning", "How to solve math problems", "I need help with homework"]
    embeddings = client.get_embeddings(texts)
    print(f"✅ Batch embeddings generated: {len(embeddings)} embeddings")

    # Test caching
    print("🧪 Testing embedding cache...")
    client.get_single_embedding(text)  # Should be cached
    print(f"✅ Cache test completed")

    # Test cache statistics
    if client.cache_enabled:
        try:
            conn = sqlite3.connect(client.cache_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM embedding_cache")
            cache_count = cursor.fetchone()[0]
            conn.close()
            print(f"💾 Cache contains {cache_count} entries")
        except Exception as e:
            print(f"💾 Cache statistics unavailable: {e}")

    return True

def test_docker_container():
    """Test Docker container management"""
    print("\n🧪 Testing Docker Container Management...")

    client = JinaEmbeddingClient()

    # Test health check
    health_status = client.check_health()
    print(f"✅ Health check: {'Healthy' if health_status else 'Not available'}")

    # Test container start (if not running)
    if not health_status:
        print("🚀 Attempting to start Docker container...")
        start_success = client.start_docker_container()
        if start_success:
            print("✅ Docker container started successfully")
        else:
            print("⚠️ Failed to start Docker container (this is OK for testing)")

    # Test embedding generation with Docker
    if client.check_health():
        print("🧪 Testing Docker-based embedding generation...")
        test_texts = ["Docker test", "Container embedding"]
        embeddings = client._generate_embeddings_from_docker(test_texts)
        print(f"✅ Generated {len(embeddings)} embeddings via Docker")

        # Validate embedding format
        if embeddings and len(embeddings[0]) == client.embedding_dim:
            print(f"✅ Embedding dimensions correct: {len(embeddings[0])}")
        else:
            print(f"⚠️ Unexpected embedding dimensions")

    return True

def test_dataset_manager():
    """Test dataset management functionality"""
    print("\n🧪 Testing Dataset Manager...")
    
    manager = DatasetManager()
    
    # Add test entries
    entries = [
        ("How to solve quadratic equations", {"subject": "math", "grade": 9}),
        ("Writing essay introduction tips", {"subject": "english", "grade": 8}),
        ("Science experiment procedures", {"subject": "science", "grade": 7})
    ]
    
    entry_ids = []
    for content, metadata in entries:
        entry_id = manager.add_dataset_entry(content, metadata)
        entry_ids.append(entry_id)
    
    print(f"✅ Added {len(entry_ids)} dataset entries")
    
    # Test search
    search_results = manager.search_similar_entries("math homework help", top_k=2)
    print(f"✅ Search returned {len(search_results)} results")
    
    if search_results:
        print(f"   Top result: {search_results[0]['content'][:50]}... (similarity: {search_results[0]['similarity']:.3f})")
    
    return True

def test_intent_classification():
    """Test intent classification functionality"""
    print("\n🧪 Testing Intent Classification...")
    
    classifier = IntentClassifier()
    
    # Generate embeddings for intent data first
    classifier.dataset_manager.generate_embeddings_for_intent_data()
    
    # Test classification
    test_texts = [
        "Can you help me with my homework?",
        "Let's talk about something fun!",
        "I need to change my study schedule",
        "How do I solve this math problem?",
        "What did I accomplish today?"
    ]
    
    for text in test_texts:
        result = classifier.classify_intent(text)
        print(f"   '{text[:30]}...' -> {result['predicted_intention']} ({result['confidence']:.2f})")
    
    # Show statistics
    stats = classifier.get_intent_statistics()
    print(f"✅ Intent classification ready with {stats['total_examples']} examples")
    
    return True

def test_summary_reports():
    """Test summary report functionality"""
    print("\n🧪 Testing Summary Reports...")
    
    manager = SummaryReportManager()
    
    # Test data
    sample_data = {
        "completion_rate_percentage": "85%",
        "most_focused_times_or_tasks": "Morning math sessions",
        "total_study_time_and_active_engagement": "2.5 hours with 90% engagement",
        "highest_and_lowest_scoring_tasks": "Math: 95%, English: 78%",
        "celebrated_efforts_or_improvements": "Great progress in algebra!"
    }
    
    # Create report
    report = manager.create_summary_report(sample_data)
    print(f"✅ Created summary report with timestamp: {report.timestamp}")
    
    # Save report
    report_id = manager.save_report(report)
    print(f"✅ Saved report with ID: {report_id}")
    
    # Get recent reports
    recent_reports = manager.get_recent_reports(limit=3)
    print(f"✅ Retrieved {len(recent_reports)} recent reports")
    
    return True

def test_enhanced_planner():
    """Test the enhanced OpenManus planner"""
    print("\n🧪 Testing Enhanced OpenManus Planner...")
    
    planner = OpenManusPlanner()
    
    # Test different task types
    test_cases = [
        ("intent_classification", {"text": "I need help with my homework"}),
        ("semantic_search", {"query": "math problems", "top_k": 3}),
        ("research_and_summarize", {"query": "How to study effectively"}),
    ]
    
    for task, params in test_cases:
        try:
            result = planner.process_task(task, params)
            print(f"✅ Task '{task}' completed successfully")
            if isinstance(result, str) and len(result) > 100:
                print(f"   Result: {result[:100]}...")
            else:
                print(f"   Result: {result}")
        except Exception as e:
            print(f"❌ Task '{task}' failed: {e}")
    
    return True

def test_integration():
    """Test full system integration"""
    print("\n🧪 Testing Full System Integration...")
    
    try:
        # Initialize system
        planner = initialize_system()
        print("✅ System initialization completed")
        
        # Test a complete workflow
        user_query = "I'm struggling with my math homework and need a study plan"
        
        # 1. Classify intent
        intent_result = planner.intent_classifier.classify_intent(user_query)
        print(f"✅ Intent classified: {intent_result['predicted_intention']}")
        
        # 2. Search for relevant content
        search_results = planner.dataset_manager.search_similar_entries(user_query, top_k=2)
        print(f"✅ Found {len(search_results)} relevant entries")
        
        # 3. Generate response
        response = planner.process_task("research_and_summarize", {"query": user_query})
        print(f"✅ Generated response: {response[:100] if isinstance(response, str) else str(response)[:100]}...")
        
        # 4. Create summary report
        learning_data = {
            "completion_rate_percentage": "80%",
            "most_focused_times_or_tasks": "Math problem solving",
            "total_study_time_and_active_engagement": "1.5 hours"
        }
        
        report_result = planner.generate_summary_report(learning_data)
        print(f"✅ Summary report generated: {report_result['output']['report_id']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced OpenManus Framework Tests\n")
    
    tests = [
        ("Jina Embeddings", test_jina_embeddings),
        ("Docker Container", test_docker_container),
        ("Dataset Manager", test_dataset_manager),
        ("Intent Classification", test_intent_classification),
        ("Summary Reports", test_summary_reports),
        ("Enhanced Planner", test_enhanced_planner),
        ("Full Integration", test_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            print(f"Running: {test_name}")
            print('='*60)
            
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {passed}/{total} tests passed")
    print('='*60)
    
    if passed == total:
        print("🎉 All tests passed! The Enhanced OpenManus Framework is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the configuration and try again.")

if __name__ == "__main__":
    main()
