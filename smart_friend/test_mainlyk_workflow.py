# -*- coding: utf-8 -*-
"""
测试MainLYK工作流程
验证从多模态输入到计划表生成的完整流程
"""

import asyncio
import logging
import json
import base64
import os
from pathlib import Path

# 导入主工作流程
from mainlyk import (
    SmartFriendWorkflow,
    process_text_task,
    process_voice_task,
    process_image_task
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class WorkflowTester:
    """
    工作流程测试器
    """
    
    def __init__(self):
        """初始化测试器"""
        self.test_child_id = 1
        self.workflow = SmartFriendWorkflow()
        logger.info("工作流程测试器初始化完成")
    
    async def test_text_input(self):
        """测试文本输入工作流程"""
        print("\n" + "=" * 60)
        print("🔤 测试文本输入工作流程")
        print("=" * 60)
        
        test_texts = [
            "今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》",
            "英语作业是单词表第5页，科学作业是观察植物生长记录",
            "完成语文阅读理解第二课，数学计算题20道，英语听力练习30分钟"
        ]
        
        for i, text in enumerate(test_texts, 1):
            print(f"\n📝 测试用例 {i}:")
            print(f"输入文本: {text}")
            
            try:
                result = await process_text_task(text, self.test_child_id, enable_tts=False)
                self._print_result(result)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
    
    async def test_voice_input(self):
        """测试语音输入工作流程"""
        print("\n" + "=" * 60)
        print("🎤 测试语音输入工作流程")
        print("=" * 60)
        
        # 创建模拟音频数据
        mock_audio_data = b"mock_audio_data_for_testing" * 100  # 模拟音频数据
        
        print("📝 测试用例:")
        print("输入: 模拟音频数据")
        
        try:
            result = await process_voice_task(mock_audio_data, self.test_child_id, enable_tts=False)
            self._print_result(result)
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    async def test_image_input(self):
        """测试图像输入工作流程"""
        print("\n" + "=" * 60)
        print("📷 测试图像输入工作流程")
        print("=" * 60)
        
        # 创建模拟图像数据（base64编码）
        mock_image_base64 = self._create_mock_image_base64()
        
        test_cases = [
            {"use_multimodal": True, "description": "使用多模态模型"},
            {"use_multimodal": False, "description": "使用OCR模型"}
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📝 测试用例 {i}: {case['description']}")
            print("输入: 模拟图像数据")
            
            try:
                result = await process_image_task(
                    mock_image_base64, 
                    self.test_child_id, 
                    use_multimodal=case["use_multimodal"],
                    enable_tts=False
                )
                self._print_result(result)
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
    
    async def test_tts_output(self):
        """测试TTS输出功能"""
        print("\n" + "=" * 60)
        print("🔊 测试TTS输出功能")
        print("=" * 60)
        
        test_text = "今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》"
        
        print("📝 测试用例:")
        print(f"输入文本: {test_text}")
        print("启用TTS: 是")
        
        try:
            result = await process_text_task(test_text, self.test_child_id, enable_tts=True)
            self._print_result(result)
            
            # 检查TTS输出
            if "tts_output" in result.get("steps", {}):
                tts_result = result["steps"]["tts_output"]
                print(f"\n🔊 TTS输出结果: {'✅ 成功' if tts_result.get('success') else '❌ 失败'}")
                if tts_result.get("success"):
                    print(f"   消息: {tts_result.get('message', '')}")
                
            if "tts_summary" in result:
                print(f"\n📝 TTS摘要: {result['tts_summary'][:100]}...")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
    
    async def test_error_handling(self):
        """测试错误处理"""
        print("\n" + "=" * 60)
        print("⚠️ 测试错误处理")
        print("=" * 60)
        
        error_cases = [
            {"type": "text", "data": "", "description": "空文本输入"},
            {"type": "invalid", "data": "test", "description": "无效输入类型"},
            {"type": "image", "data": "invalid_base64", "description": "无效图像数据"}
        ]
        
        for i, case in enumerate(error_cases, 1):
            print(f"\n📝 错误测试用例 {i}: {case['description']}")
            
            try:
                input_data = {
                    "type": case["type"],
                    "data": case["data"],
                    "enable_tts": False
                }
                
                result = await self.workflow.process_workflow(input_data, self.test_child_id)
                
                if result.get("success"):
                    print("⚠️ 预期失败但成功了")
                else:
                    print(f"✅ 正确处理错误: {result.get('error', '未知错误')}")
                    
            except Exception as e:
                print(f"✅ 正确捕获异常: {e}")
    
    def _print_result(self, result: dict):
        """打印测试结果"""
        print(f"\n📊 执行结果:")
        print(f"   状态: {'✅ 成功' if result.get('success') else '❌ 失败'}")
        print(f"   学生ID: {result.get('child_id', 'N/A')}")
        print(f"   开始时间: {result.get('start_time', 'N/A')}")
        print(f"   结束时间: {result.get('end_time', 'N/A')}")
        
        if result.get("error"):
            print(f"   错误信息: {result['error']}")
            return
        
        # 显示各步骤结果
        steps = result.get("steps", {})
        
        # 输入处理
        input_result = steps.get("input_processing", {})
        print(f"   📝 输入处理: {'✅ 成功' if input_result.get('success') else '❌ 失败'}")
        if input_result.get("success"):
            print(f"      解析任务数: {input_result.get('total_tasks', 0)}")
            print(f"      存储任务数: {input_result.get('stored_tasks', 0)}")
        
        # Prompt生成
        prompt_result = steps.get("prompt_generation")
        print(f"   🎯 Prompt生成: {'✅ 成功' if prompt_result else '❌ 失败'}")
        
        # 计划表生成
        schedule_result = steps.get("schedule_generation", {})
        print(f"   📅 计划表生成: {'✅ 成功' if schedule_result.get('success') else '❌ 失败'}")
        if schedule_result.get("success"):
            content = schedule_result.get("schedule_content", "")
            print(f"      内容预览: {content[:100]}...")
    
    def _create_mock_image_base64(self) -> str:
        """创建模拟的base64图像数据"""
        # 创建一个简单的1x1像素PNG图像的base64编码
        # 这是一个透明的1x1像素PNG图像
        mock_png_base64 = (
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        )
        return mock_png_base64
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行MainLYK工作流程测试")
        print("=" * 80)
        
        try:
            # 测试文本输入
            await self.test_text_input()
            
            # 测试语音输入
            await self.test_voice_input()
            
            # 测试图像输入
            await self.test_image_input()
            
            # 测试TTS输出
            await self.test_tts_output()
            
            # 测试错误处理
            await self.test_error_handling()
            
            print("\n" + "=" * 80)
            print("✅ 所有测试完成")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            logger.error(f"测试失败: {e}", exc_info=True)


async def main():
    """主函数"""
    tester = WorkflowTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
