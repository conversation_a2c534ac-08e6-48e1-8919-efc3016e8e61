#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Smart Friend - Standard Mode

This is the main entry point for the Smart Friend application
running in standard mode (without OpenManus enhancements).

Features:
- Core AI services (Doubao, ASR, TTS)
- User management and learning analytics
- Task planning and daily learning tracking
- Voice interaction and multimodal input
- Real-time communication via Socket.IO

Usage:
    python main_standard.py

Server will start on: http://localhost:8003
API Documentation: http://localhost:8003/docs
Web Interface: http://localhost:8003/static/aiChild.html
"""

import os
import sys
import time
import asyncio
import threading
import uvicorn
from pathlib import Path

# Set environment for standard mode
os.environ["APP_MODE"] = "standard"
os.environ["ENABLE_OPENMANUS"] = "false"

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app_factory import create_app
from config.app_config import get_app_config
from backend.utils.logging import setup_logger
from service.socketio_service import init_socketio_service
from api.v1.endpoints.asr_socketio_api import create_socketio_handlers, connect_asr_service

# Initialize logging
logger = setup_logger('main_standard')

# Global variables for ASR
asr_client = None
asr_connected = False
is_recording = False
last_speech_time = time.time()
silence_timeout = 1.5


def initialize_services():
    """Initialize additional services"""
    logger.info("🔧 Initializing additional services...")
    
    try:
        # Initialize Socket.IO service
        socketio_service = init_socketio_service()
        logger.info("✅ Socket.IO service initialized")
        
        # Integrate ASR Socket.IO handlers
        create_socketio_handlers(socketio_service.sio)
        logger.info("✅ ASR Socket.IO handlers created")
        
        # Auto-connect ASR service
        async def connect_asr():
            try:
                asr_result = await connect_asr_service()
                if asr_result["success"]:
                    logger.info(f"✅ ASR service connected: {asr_result['message']}")
                else:
                    logger.warning(f"⚠️ ASR service connection failed: {asr_result['message']}")
            except Exception as e:
                logger.error(f"❌ ASR service connection error: {e}")
        
        # Run ASR connection in background
        def run_asr_connection():
            asyncio.run(connect_asr())
        
        threading.Thread(target=run_asr_connection, daemon=True).start()
        
    except Exception as e:
        logger.error(f"❌ Service initialization error: {e}")


def main():
    """Main application entry point"""
    config = get_app_config()
    
    print("🚀 Smart Friend - Standard Mode")
    print("=" * 50)
    print(f"📋 Application: {config.PROJECT_NAME}")
    print(f"📋 Version: {config.VERSION}")
    print(f"📋 Mode: {config.APP_MODE.value.upper()}")
    print(f"📍 Server: http://{config.HOST}:{config.PORT}")
    print(f"📖 API Documentation: http://localhost:{config.PORT}/docs")
    print(f"📱 Web Interface: http://localhost:{config.PORT}/static/aiChild.html")
    print("=" * 50)
    
    # Create application
    app = create_app()
    
    # Initialize services
    initialize_services()
    
    logger.info("🌐 Starting Smart Friend server in standard mode...")
    
    # Start server
    uvicorn.run(
        app,
        host=config.HOST,
        port=config.PORT,
        log_level=config.LOG_LEVEL.lower(),
        access_log=True
    )


if __name__ == "__main__":
    main()
