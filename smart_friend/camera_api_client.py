#!/usr/bin/env python3
"""
摄像头API客户端库
用于其他模块接入摄像头FastAPI接口

使用示例:
    from camera_api_client import CameraAPIClient
    
    client = CameraAPIClient("http://localhost:8003/api/v1/camera")
    session_id = client.create_session("my_module", "camera_001")
    client.set_transform(session_id, rotation=90, zoom=1.5)
    image_data = client.get_frame(session_id)
"""

import requests
import base64
import json
import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime
import time
from io import BytesIO
from PIL import Image

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CameraAPIError(Exception):
    """摄像头API异常"""
    pass


class CameraAPIClient:
    """摄像头API客户端
    
    提供完整的摄像头API接口调用功能，包括：
    - 摄像头注册和会话管理
    - 图像上传和获取
    - 图像变换（旋转、缩放）
    - 状态查询和健康检查
    """
    
    def __init__(self, base_url: str = "http://localhost:8003/api/v1/camera", 
                 timeout: int = 30, max_retries: int = 3):
        """初始化客户端
        
        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 创建会话，复用连接
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'CameraAPIClient/1.0'
        })
        
        logger.info(f"初始化摄像头API客户端: {self.base_url}")
    
    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """统一请求方法，包含重试和错误处理"""
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.request(
                    method, url, timeout=self.timeout, **kwargs
                )
                response.raise_for_status()
                
                # 解析JSON响应
                result = response.json()
                
                # 检查业务逻辑成功状态
                if 'success' in result and not result['success']:
                    raise CameraAPIError(f"API调用失败: {result.get('message', '未知错误')}")
                
                logger.debug(f"API调用成功: {method} {endpoint}")
                return result
                
            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"请求失败，{wait_time}秒后重试 (第{attempt+1}次): {e}")
                    time.sleep(wait_time)
                    continue
                else:
                    raise CameraAPIError(f"网络请求失败: {str(e)}")
            except json.JSONDecodeError as e:
                raise CameraAPIError(f"响应解析失败: {str(e)}")
            except Exception as e:
                raise CameraAPIError(f"未知错误: {str(e)}")
    
    # ==================== 基础接口 ====================
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查
        
        Returns:
            包含服务状态信息的字典
        """
        try:
            result = self._request('GET', '/health')
            logger.info(f"服务健康状态: {result.get('service_status', 'unknown')}")
            return result
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            raise
    
    def get_cameras(self) -> List[Dict[str, Any]]:
        """获取摄像头列表
        
        Returns:
            摄像头信息列表
        """
        try:
            result = self._request('GET', '/cameras')
            cameras = result.get('cameras', [])
            logger.info(f"获取到 {len(cameras)} 个摄像头")
            return cameras
        except Exception as e:
            logger.error(f"获取摄像头列表失败: {e}")
            raise
    
    # ==================== 摄像头注册 ====================
    
    def register_cameras(self, client_id: str, cameras: List[Dict[str, Any]]) -> bool:
        """注册摄像头
        
        Args:
            client_id: 客户端ID
            cameras: 摄像头信息列表，每个摄像头包含:
                - camera_id: 摄像头ID
                - name: 摄像头名称
                - resolution: 分辨率 (如 "640x480")
                - stream_type: 流类型 (如 "http", "websocket")
        
        Returns:
            注册是否成功
        """
        try:
            data = {
                "client_id": client_id,
                "cameras": cameras
            }
            result = self._request('POST', '/register', json=data)
            success = result.get('success', False)
            
            if success:
                logger.info(f"成功注册 {len(cameras)} 个摄像头，客户端: {client_id}")
            else:
                logger.warning(f"摄像头注册失败: {result.get('message', '未知原因')}")
            
            return success
        except Exception as e:
            logger.error(f"注册摄像头失败: {e}")
            raise
    
    # ==================== 会话管理 ====================
    
    def create_session(self, client_id: str, camera_id: str) -> str:
        """创建摄像头会话
        
        Args:
            client_id: 客户端ID
            camera_id: 摄像头ID
        
        Returns:
            会话ID
        """
        try:
            data = {
                "client_id": client_id,
                "camera_id": camera_id
            }
            result = self._request('POST', '/session/create', json=data)
            session_id = result.get('session_id')
            
            if not session_id:
                raise CameraAPIError("未获取到有效的会话ID")
            
            logger.info(f"成功创建会话: {session_id}")
            return session_id
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise
    
    def close_session(self, session_id: str) -> bool:
        """关闭会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            关闭是否成功
        """
        try:
            result = self._request('POST', f'/session/{session_id}/close')
            success = result.get('success', False)
            
            if success:
                logger.info(f"成功关闭会话: {session_id}")
            else:
                logger.warning(f"关闭会话失败: {session_id}")
            
            return success
        except Exception as e:
            logger.error(f"关闭会话失败: {e}")
            raise
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态
        
        Args:
            session_id: 会话ID
        
        Returns:
            会话状态信息
        """
        try:
            result = self._request('GET', f'/session/{session_id}/status')
            logger.debug(f"获取会话状态: {session_id}")
            return result
        except Exception as e:
            logger.error(f"获取会话状态失败: {e}")
            raise
    
    # ==================== 帧数据操作 ====================
    
    def update_frame(self, session_id: str, frame_data: Union[str, bytes, Image.Image], 
                    test_mode: bool = False) -> bool:
        """上传帧数据
        
        Args:
            session_id: 会话ID
            frame_data: 图像数据，支持:
                - str: base64编码的图像数据
                - bytes: 原始图像字节数据
                - PIL.Image: PIL图像对象
            test_mode: 是否为测试模式（不保存数据）
        
        Returns:
            上传是否成功
        """
        try:
            # 转换图像数据为base64
            if isinstance(frame_data, str):
                # 已经是base64字符串
                base64_data = frame_data
            elif isinstance(frame_data, bytes):
                # 字节数据转base64
                base64_data = base64.b64encode(frame_data).decode('utf-8')
            elif isinstance(frame_data, Image.Image):
                # PIL图像转base64
                buffer = BytesIO()
                frame_data.save(buffer, format='JPEG', quality=85)
                base64_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
            else:
                raise CameraAPIError(f"不支持的图像数据类型: {type(frame_data)}")
            
            data = {
                "session_id": session_id,
                "frame_data": base64_data,
                "test_mode": test_mode
            }
            
            result = self._request('POST', '/frame/update', json=data)
            success = result.get('success', False)
            
            if success:
                mode_text = "测试模式" if test_mode else "正常模式"
                logger.debug(f"成功上传帧数据 ({mode_text}): {session_id}")
            
            return success
        except Exception as e:
            logger.error(f"上传帧数据失败: {e}")
            raise
    
    def get_frame(self, session_id: str, apply_transforms: bool = True, 
                 return_format: str = 'base64') -> Optional[Union[str, bytes, Image.Image]]:
        """获取帧数据
        
        Args:
            session_id: 会话ID
            apply_transforms: 是否应用图像变换
            return_format: 返回格式 ('base64', 'bytes', 'pil')
        
        Returns:
            图像数据，格式根据return_format参数决定
        """
        try:
            params = {'apply_transforms': apply_transforms}
            result = self._request('GET', f'/frame/{session_id}', params=params)
            
            image_data = result.get('image_data')
            if not image_data:
                logger.warning(f"未获取到图像数据: {session_id}")
                return None
            
            # 根据要求的格式返回数据
            if return_format == 'base64':
                return image_data
            elif return_format == 'bytes':
                return base64.b64decode(image_data)
            elif return_format == 'pil':
                image_bytes = base64.b64decode(image_data)
                return Image.open(BytesIO(image_bytes))
            else:
                raise CameraAPIError(f"不支持的返回格式: {return_format}")
                
        except Exception as e:
            logger.error(f"获取帧数据失败: {e}")
            raise
    
    # ==================== 图像变换 ====================
    
    def set_transform(self, session_id: str, rotation: Optional[int] = None, 
                     zoom: Optional[float] = None) -> bool:
        """设置图像变换
        
        Args:
            session_id: 会话ID
            rotation: 旋转角度 (0, 90, 180, 270)
            zoom: 缩放倍数 (0.1 - 3.0)
        
        Returns:
            设置是否成功
        """
        try:
            data = {"session_id": session_id}
            
            if rotation is not None:
                if rotation not in [0, 90, 180, 270]:
                    raise CameraAPIError(f"无效的旋转角度: {rotation}，支持: 0, 90, 180, 270")
                data["rotation"] = rotation
            
            if zoom is not None:
                if not (0.1 <= zoom <= 3.0):
                    raise CameraAPIError(f"无效的缩放倍数: {zoom}，范围: 0.1-3.0")
                data["zoom"] = zoom
            
            if len(data) == 1:  # 只有session_id
                raise CameraAPIError("至少需要指定rotation或zoom参数")
            
            result = self._request('POST', '/transform', json=data)
            success = result.get('success', False)
            
            if success:
                transform_info = []
                if rotation is not None:
                    transform_info.append(f"旋转{rotation}°")
                if zoom is not None:
                    transform_info.append(f"缩放{zoom}x")
                logger.info(f"成功设置变换: {', '.join(transform_info)}")
            
            return success
        except Exception as e:
            logger.error(f"设置图像变换失败: {e}")
            raise
    
    # ==================== 便捷方法 ====================
    
    def quick_setup(self, client_id: str, camera_name: str = "API摄像头", 
                   resolution: str = "640x480") -> str:
        """快速设置：注册摄像头并创建会话
        
        Args:
            client_id: 客户端ID
            camera_name: 摄像头名称
            resolution: 分辨率
        
        Returns:
            会话ID
        """
        camera_id = f"{client_id}_camera"
        
        # 注册摄像头
        cameras = [{
            "camera_id": camera_id,
            "name": camera_name,
            "resolution": resolution,
            "stream_type": "http"
        }]
        
        self.register_cameras(client_id, cameras)
        
        # 创建会话
        session_id = self.create_session(client_id, camera_id)
        
        logger.info(f"快速设置完成，会话ID: {session_id}")
        return session_id
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理资源"""
        self.session.close()
        logger.info("摄像头API客户端已关闭")


# ==================== 使用示例 ====================

def example_usage():
    """使用示例"""
    
    # 使用上下文管理器确保资源清理
    with CameraAPIClient("http://localhost:8003/api/v1/camera") as client:
        try:
            # 1. 健康检查
            health = client.health_check()
            print(f"服务状态: {health.get('service_status')}")
            
            # 2. 快速设置
            session_id = client.quick_setup("example_module", "示例摄像头")
            print(f"会话ID: {session_id}")
            
            # 3. 创建测试图像
            test_image = Image.new('RGB', (640, 480), color='blue')
            
            # 4. 上传图像（测试模式）
            success = client.update_frame(session_id, test_image, test_mode=True)
            print(f"上传结果: {success}")
            
            # 5. 设置图像变换
            client.set_transform(session_id, rotation=90, zoom=1.5)
            print("变换设置成功")
            
            # 6. 获取变换后的图像
            transformed_image = client.get_frame(session_id, return_format='pil')
            if transformed_image:
                print(f"获取到变换后图像: {transformed_image.size}")
            
            # 7. 关闭会话
            client.close_session(session_id)
            print("会话已关闭")
            
        except CameraAPIError as e:
            print(f"摄像头API错误: {e}")
        except Exception as e:
            print(f"其他错误: {e}")


if __name__ == "__main__":
    example_usage()
