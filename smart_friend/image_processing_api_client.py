#!/usr/bin/env python3
"""
图像预处理API客户端库
用于其他模块接入图像预处理FastAPI接口

使用示例:
    from image_processing_api_client import ImageProcessingAPIClient
    
    client = ImageProcessingAPIClient("http://localhost:8003/api/v1/image-processing")
    
    # 灰度化处理
    result = client.grayscale(image_data, method="weighted")
    
    # 模糊处理
    result = client.blur(image_data, method="gaussian", kernel_size=5, sigma_x=1.0)
    
    # 图像增强
    result = client.enhance(image_data, enhancement_type="brightness", brightness=20.0)
"""

import requests
import base64
import json
import logging
from typing import Optional, Dict, Any, Union, List
from datetime import datetime
import time
from io import BytesIO
from PIL import Image
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageProcessingAPIError(Exception):
    """图像处理API异常类

    用于封装图像处理API调用过程中发生的各种异常情况，
    包括网络错误、参数错误、服务器错误等。

    继承自Python标准异常类Exception，可以像普通异常一样使用。
    """
    pass


class ImageProcessingResult:
    """图像处理结果封装类

    封装图像处理API的返回结果，提供统一的结果访问接口。
    包含处理状态、处理后的图像数据、处理时间、算法信息等。

    主要功能：
    - 解析API响应数据
    - 提供多种格式的图像数据获取方法
    - 支持将处理结果保存为文件
    - 提供友好的字符串表示

    Attributes:
        success (bool): 处理是否成功
        message (str): 处理结果消息
        processed_image (str): 处理后的图像数据(base64格式)
        processing_time (float): 处理耗时(秒)
        original_size (str): 原始图像尺寸
        processed_size (str): 处理后图像尺寸
        algorithm_info (dict): 算法详细信息
    """
    
    def __init__(self, response_data: Dict[str, Any]):
        self.success = response_data.get('success', False)
        self.message = response_data.get('message', '')
        self.processed_image = response_data.get('processed_image')
        self.processing_time = response_data.get('processing_time', 0.0)
        self.original_size = response_data.get('original_size', '')
        self.processed_size = response_data.get('processed_size', '')
        self.algorithm_info = response_data.get('algorithm_info', {})
        
    def get_image_data(self, format: str = 'base64') -> Optional[Union[str, bytes, Image.Image]]:
        """获取处理后的图像数据

        支持多种格式的图像数据获取，方便不同场景下的使用。

        Args:
            format (str): 返回格式，支持以下选项：
                - 'base64': 返回base64编码的字符串（默认）
                - 'bytes': 返回原始字节数据
                - 'pil': 返回PIL.Image对象，便于进一步处理

        Returns:
            Optional[Union[str, bytes, Image.Image]]:
                根据format参数返回对应格式的图像数据，
                如果没有处理后的图像数据则返回None

        Raises:
            ImageProcessingAPIError: 当指定不支持的格式时抛出异常
        """
        if not self.processed_image:
            return None
            
        if format == 'base64':
            return self.processed_image
        elif format == 'bytes':
            return base64.b64decode(self.processed_image)
        elif format == 'pil':
            image_bytes = base64.b64decode(self.processed_image)
            return Image.open(BytesIO(image_bytes))
        else:
            raise ImageProcessingAPIError(f"不支持的返回格式: {format}")
    
    def save_image(self, filepath: str, quality: int = 90) -> bool:
        """保存处理后的图像到文件

        将处理后的图像数据保存为文件，支持PNG和JPEG格式。
        会自动创建目标目录（如果不存在），并根据文件扩展名选择保存格式。

        Args:
            filepath (str): 保存文件的完整路径，包含文件名和扩展名
                支持的扩展名：.png, .jpg, .jpeg
            quality (int, optional): JPEG格式的压缩质量，范围1-100，
                数值越高质量越好但文件越大。默认为90。
                对PNG格式无效。

        Returns:
            bool: 保存成功返回True，失败返回False

        Note:
            - 如果目标目录不存在，会自动创建
            - PNG格式保存时会忽略quality参数
            - 如果没有处理后的图像数据，直接返回False
        """
        try:
            if not self.processed_image:
                return False
                
            image = self.get_image_data('pil')
            if image:
                # 确保目录存在
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                
                # 根据文件扩展名保存
                if filepath.lower().endswith('.png'):
                    image.save(filepath, 'PNG')
                else:
                    image.save(filepath, 'JPEG', quality=quality)
                return True
            return False
        except Exception as e:
            logger.error(f"保存图像失败: {e}")
            return False
    
    def __str__(self):
        return f"ImageProcessingResult(success={self.success}, time={self.processing_time:.3f}s, size={self.processed_size})"


class ImageProcessingAPIClient:
    """图像处理API客户端
    
    提供完整的图像预处理API接口调用功能，包括：
    - 图像灰度化处理
    - 图像模糊处理  
    - 图像增强处理
    - 健康检查和状态查询
    """
    
    def __init__(self, base_url: str = "http://localhost:8003/api/v1/image-processing", 
                 timeout: int = 30, max_retries: int = 3):
        """初始化客户端
        
        Args:
            base_url: API基础URL
            timeout: 请求超时时间（秒）
            max_retries: 最大重试次数
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        
        # 创建会话，复用连接
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'ImageProcessingAPIClient/1.0'
        })
        
        logger.info(f"初始化图像处理API客户端: {self.base_url}")
    
    def _request(self, method: str, endpoint: str, **kwargs) -> Dict[Any, Any]:
        """统一请求方法，包含重试和错误处理

        内部方法，用于统一处理所有API请求。实现了自动重试机制、
        错误处理和响应解析功能。

        Args:
            method (str): HTTP请求方法 ('GET', 'POST', 'PUT', 'DELETE')
            endpoint (str): API端点路径，如 '/health', '/grayscale'
            **kwargs: 传递给requests的额外参数

        Returns:
            Dict[Any, Any]: 解析后的JSON响应数据

        Raises:
            ImageProcessingAPIError: 当请求失败、响应解析失败或达到最大重试次数时
        """
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.request(
                    method, url, timeout=self.timeout, **kwargs
                )
                response.raise_for_status()
                
                # 解析JSON响应
                result = response.json()
                
                logger.debug(f"API调用成功: {method} {endpoint}")
                return result
                
            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"请求失败，{wait_time}秒后重试 (第{attempt+1}次): {e}")
                    time.sleep(wait_time)
                    continue
                else:
                    raise ImageProcessingAPIError(f"网络请求失败: {str(e)}")
            except json.JSONDecodeError as e:
                raise ImageProcessingAPIError(f"响应解析失败: {str(e)}")
            except Exception as e:
                raise ImageProcessingAPIError(f"未知错误: {str(e)}")
    
    def _prepare_image_data(self, image_data: Union[str, bytes, Image.Image]) -> str:
        """准备图像数据为base64格式

        内部方法，将各种格式的图像数据统一转换为base64字符串格式，
        以便通过API传输。支持文件路径、字节数据、PIL图像对象等多种输入格式。

        Args:
            image_data (Union[str, bytes, Image.Image]): 图像数据，支持：
                - str: 文件路径或base64字符串
                - bytes: 原始图像字节数据
                - Image.Image: PIL图像对象

        Returns:
            str: base64编码的图像数据字符串

        Raises:
            ImageProcessingAPIError: 当输入数据类型不支持时
        """
        if isinstance(image_data, str):
            # 如果是文件路径，读取文件
            if os.path.isfile(image_data):
                with open(image_data, 'rb') as f:
                    return base64.b64encode(f.read()).decode('utf-8')
            # 如果是base64字符串，移除可能的前缀
            if ',' in image_data:
                return image_data.split(',')[1]
            return image_data
        elif isinstance(image_data, bytes):
            # 字节数据转base64
            return base64.b64encode(image_data).decode('utf-8')
        elif isinstance(image_data, Image.Image):
            # PIL图像转base64
            buffer = BytesIO()
            image_data.save(buffer, format='JPEG', quality=85)
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        else:
            raise ImageProcessingAPIError(f"不支持的图像数据类型: {type(image_data)}")
    
    # ==================== 基础接口 ====================
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查

        检查图像处理服务的运行状态，获取服务基本信息。
        通常在开始使用API前调用，确保服务正常运行。

        Returns:
            Dict[str, Any]: 包含服务状态信息的字典，通常包含：
                - service_status: 服务状态 ('running', 'error', etc.)
                - opencv_version: OpenCV版本信息
                - available_methods: 可用的处理方法列表
                - server_time: 服务器时间
                - uptime: 服务运行时间

        Raises:
            ImageProcessingAPIError: 当健康检查请求失败时
        """
        try:
            result = self._request('GET', '/health')
            logger.info(f"图像处理服务状态: {result.get('service_status', 'unknown')}")
            return result
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            raise
    
    def get_available_methods(self) -> Dict[str, List[str]]:
        """获取可用的处理方法

        查询服务端支持的所有图像处理方法和参数选项。
        可用于动态检查服务能力，或在UI中展示可选项。

        Returns:
            Dict[str, List[str]]: 可用方法字典，格式如：
                {
                    "grayscale_methods": ["weighted", "average", "opencv", "luminosity"],
                    "blur_methods": ["gaussian", "box", "median"],
                    "enhancement_types": ["brightness", "contrast", "saturation", "gamma", "auto_enhance"]
                }

        Raises:
            ImageProcessingAPIError: 当获取方法信息失败时
        """
        try:
            result = self.health_check()
            return result.get('available_methods', {})
        except Exception as e:
            logger.error(f"获取可用方法失败: {e}")
            raise

    # ==================== 图像处理接口 ====================

    def grayscale(self, image_data: Union[str, bytes, Image.Image],
                  method: str = "weighted", preserve_alpha: bool = False,
                  return_format: str = "jpeg", quality: int = 90) -> ImageProcessingResult:
        """图像灰度化处理

        Args:
            image_data: 图像数据（文件路径、base64字符串、字节数据或PIL图像）
            method: 灰度化方法 ("weighted", "average", "opencv", "luminosity")
            preserve_alpha: 是否保留透明度通道
            return_format: 返回格式 ("jpeg", "png")
            quality: JPEG质量 (1-100)

        Returns:
            ImageProcessingResult对象
        """
        try:
            base64_data = self._prepare_image_data(image_data)

            request_data = {
                "image_data": base64_data,
                "method": method,
                "preserve_alpha": preserve_alpha,
                "return_format": return_format,
                "quality": quality
            }

            result = self._request('POST', '/grayscale', json=request_data)

            if result.get('success'):
                logger.info(f"灰度化处理成功: method={method}, time={result.get('processing_time', 0):.3f}s")
            else:
                logger.warning(f"灰度化处理失败: {result.get('message', '未知错误')}")

            return ImageProcessingResult(result)

        except Exception as e:
            logger.error(f"灰度化处理失败: {e}")
            raise

    def blur(self, image_data: Union[str, bytes, Image.Image],
             method: str = "gaussian", kernel_size: int = 5, sigma_x: float = 1.0,
             sigma_y: Optional[float] = None, return_format: str = "jpeg",
             quality: int = 90) -> ImageProcessingResult:
        """图像模糊处理

        Args:
            image_data: 图像数据（文件路径、base64字符串、字节数据或PIL图像）
            method: 模糊方法 ("gaussian", "box", "median")
            kernel_size: 核大小 (3-31，必须为奇数)
            sigma_x: X方向标准差 (0.1-10.0)
            sigma_y: Y方向标准差，默认与sigma_x相同
            return_format: 返回格式 ("jpeg", "png")
            quality: JPEG质量 (1-100)

        Returns:
            ImageProcessingResult对象
        """
        try:
            base64_data = self._prepare_image_data(image_data)

            request_data = {
                "image_data": base64_data,
                "method": method,
                "kernel_size": kernel_size,
                "sigma_x": sigma_x,
                "return_format": return_format,
                "quality": quality
            }

            if sigma_y is not None:
                request_data["sigma_y"] = sigma_y

            result = self._request('POST', '/blur', json=request_data)

            if result.get('success'):
                logger.info(f"模糊处理成功: method={method}, kernel={kernel_size}, time={result.get('processing_time', 0):.3f}s")
            else:
                logger.warning(f"模糊处理失败: {result.get('message', '未知错误')}")

            return ImageProcessingResult(result)

        except Exception as e:
            logger.error(f"模糊处理失败: {e}")
            raise

    def enhance(self, image_data: Union[str, bytes, Image.Image],
                enhancement_type: str, brightness: Optional[float] = None,
                contrast: Optional[float] = None, saturation: Optional[float] = None,
                gamma: Optional[float] = None, auto_enhance_strength: Optional[float] = None,
                return_format: str = "jpeg", quality: int = 90) -> ImageProcessingResult:
        """图像增强处理

        Args:
            image_data: 图像数据（文件路径、base64字符串、字节数据或PIL图像）
            enhancement_type: 增强类型 ("brightness", "contrast", "saturation", "gamma", "auto_enhance")
            brightness: 亮度调整 (-100到100)
            contrast: 对比度调整 (0.1到3.0)
            saturation: 饱和度调整 (0.0到3.0)
            gamma: 伽马值 (0.1到3.0)
            auto_enhance_strength: 自动增强强度 (0.1到2.0)
            return_format: 返回格式 ("jpeg", "png")
            quality: JPEG质量 (1-100)

        Returns:
            ImageProcessingResult对象
        """
        try:
            base64_data = self._prepare_image_data(image_data)

            request_data = {
                "image_data": base64_data,
                "enhancement_type": enhancement_type,
                "return_format": return_format,
                "quality": quality
            }

            # 根据增强类型添加对应参数
            if brightness is not None:
                request_data["brightness"] = brightness
            if contrast is not None:
                request_data["contrast"] = contrast
            if saturation is not None:
                request_data["saturation"] = saturation
            if gamma is not None:
                request_data["gamma"] = gamma
            if auto_enhance_strength is not None:
                request_data["auto_enhance_strength"] = auto_enhance_strength

            result = self._request('POST', '/enhancement', json=request_data)

            if result.get('success'):
                logger.info(f"图像增强成功: type={enhancement_type}, time={result.get('processing_time', 0):.3f}s")
            else:
                logger.warning(f"图像增强失败: {result.get('message', '未知错误')}")

            return ImageProcessingResult(result)

        except Exception as e:
            logger.error(f"图像增强失败: {e}")
            raise

    # ==================== 便捷方法 ====================

    def process_image_file(self, input_path: str, output_path: str,
                          algorithm: str, **kwargs) -> ImageProcessingResult:
        """处理图像文件的便捷方法

        一站式图像文件处理方法，从文件读取图像，进行处理，并保存结果。
        适合简单的批处理场景或命令行工具使用。

        Args:
            input_path (str): 输入图像文件的完整路径
            output_path (str): 输出图像文件的完整路径
            algorithm (str): 算法类型，支持：
                - "grayscale": 灰度化处理
                - "blur": 模糊处理
                - "enhancement": 图像增强
            **kwargs: 算法特定参数，根据algorithm类型传入对应参数：
                - grayscale: method, preserve_alpha, return_format, quality
                - blur: method, kernel_size, sigma_x, sigma_y, return_format, quality
                - enhancement: enhancement_type, brightness, contrast, saturation, gamma, etc.

        Returns:
            ImageProcessingResult: 处理结果对象，包含处理状态和结果信息

        Raises:
            ImageProcessingAPIError: 当输入文件不存在、算法类型不支持或处理失败时

        Example:
            >>> client = ImageProcessingAPIClient()
            >>> result = client.process_image_file(
            ...     "input.jpg", "output.jpg",
            ...     "grayscale", method="weighted"
            ... )
        """
        try:
            # 检查输入文件
            if not os.path.isfile(input_path):
                raise ImageProcessingAPIError(f"输入文件不存在: {input_path}")

            # 根据算法类型调用对应方法
            if algorithm == "grayscale":
                result = self.grayscale(input_path, **kwargs)
            elif algorithm == "blur":
                result = self.blur(input_path, **kwargs)
            elif algorithm == "enhancement":
                result = self.enhance(input_path, **kwargs)
            else:
                raise ImageProcessingAPIError(f"不支持的算法类型: {algorithm}")

            # 保存结果
            if result.success:
                success = result.save_image(output_path)
                if success:
                    logger.info(f"处理结果已保存到: {output_path}")
                else:
                    logger.error(f"保存处理结果失败: {output_path}")

            return result

        except Exception as e:
            logger.error(f"处理图像文件失败: {e}")
            raise

    def batch_process(self, image_list: List[Union[str, bytes, Image.Image]],
                     algorithm: str, output_dir: Optional[str] = None,
                     **kwargs) -> List[ImageProcessingResult]:
        """批量处理图像

        对多张图像执行相同的处理算法，支持自动保存到指定目录。
        适合批量处理场景，如批量转换、批量增强等。

        Args:
            image_list (List[Union[str, bytes, Image.Image]]): 图像数据列表，
                每个元素可以是文件路径、字节数据或PIL图像对象
            algorithm (str): 算法类型，支持：
                - "grayscale": 灰度化处理
                - "blur": 模糊处理
                - "enhancement": 图像增强
            output_dir (Optional[str]): 输出目录路径，如果指定则自动保存处理结果。
                文件名格式为：{algorithm}_{序号}_{时间戳}.jpg
            **kwargs: 算法特定参数，参考对应的单张处理方法

        Returns:
            List[ImageProcessingResult]: 处理结果对象列表，
                与输入图像列表一一对应，失败的处理会返回包含错误信息的结果对象

        Note:
            - 批量处理会逐张处理，某张图像失败不会影响其他图像的处理
            - 处理过程中会输出进度日志
            - 最终会输出成功处理的图像数量统计

        Example:
            >>> images = ["img1.jpg", "img2.jpg", "img3.jpg"]
            >>> results = client.batch_process(
            ...     images, "grayscale",
            ...     output_dir="output/", method="weighted"
            ... )
        """
        results = []

        for i, image_data in enumerate(image_list):
            try:
                # 根据算法类型调用对应方法
                if algorithm == "grayscale":
                    result = self.grayscale(image_data, **kwargs)
                elif algorithm == "blur":
                    result = self.blur(image_data, **kwargs)
                elif algorithm == "enhancement":
                    result = self.enhance(image_data, **kwargs)
                else:
                    raise ImageProcessingAPIError(f"不支持的算法类型: {algorithm}")

                results.append(result)

                # 如果指定了输出目录，保存结果
                if output_dir and result.success:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{algorithm}_{i+1}_{timestamp}.jpg"
                    output_path = os.path.join(output_dir, filename)
                    result.save_image(output_path)

                logger.info(f"批量处理进度: {i+1}/{len(image_list)}")

            except Exception as e:
                logger.error(f"批量处理第{i+1}张图像失败: {e}")
                # 创建失败结果
                error_result = ImageProcessingResult({
                    'success': False,
                    'message': str(e),
                    'processed_image': None,
                    'processing_time': 0.0
                })
                results.append(error_result)

        success_count = sum(1 for r in results if r.success)
        logger.info(f"批量处理完成: {success_count}/{len(image_list)} 成功")

        return results

    def __enter__(self):
        """上下文管理器入口

        支持使用with语句来自动管理客户端资源。

        Returns:
            ImageProcessingAPIClient: 返回客户端实例本身

        Example:
            >>> with ImageProcessingAPIClient() as client:
            ...     result = client.grayscale(image_data)
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理资源

        自动关闭HTTP会话，释放网络连接资源。
        无论是否发生异常都会执行清理操作。

        Args:
            exc_type: 异常类型（如果有）
            exc_val: 异常值（如果有）
            exc_tb: 异常追踪信息（如果有）
        """
        self.session.close()
        logger.info("图像处理API客户端已关闭")


# ==================== 使用示例 ====================

def example_usage():
    """使用示例"""

    # 使用上下文管理器确保资源清理
    with ImageProcessingAPIClient("http://localhost:8003/api/v1/image-processing") as client:
        try:
            # 1. 健康检查
            health = client.health_check()
            print(f"服务状态: {health.get('service_status')}")
            print(f"OpenCV版本: {health.get('opencv_version')}")

            # 2. 获取可用方法
            methods = client.get_available_methods()
            print(f"可用方法: {methods}")

            # 3. 创建测试图像
            test_image = Image.new('RGB', (400, 300), color=(255, 128, 64))

            # 4. 灰度化处理
            print("\n=== 灰度化处理 ===")
            gray_result = client.grayscale(test_image, method="weighted")
            print(f"灰度化结果: {gray_result}")
            if gray_result.success:
                gray_result.save_image("output/example_grayscale.jpg")

            # 5. 模糊处理
            print("\n=== 模糊处理 ===")
            blur_result = client.blur(test_image, method="gaussian", kernel_size=15, sigma_x=3.0)
            print(f"模糊处理结果: {blur_result}")
            if blur_result.success:
                blur_result.save_image("output/example_blur.jpg")

            # 6. 图像增强
            print("\n=== 图像增强 ===")
            enhance_result = client.enhance(test_image, enhancement_type="brightness", brightness=30.0)
            print(f"图像增强结果: {enhance_result}")
            if enhance_result.success:
                enhance_result.save_image("output/example_enhancement.jpg")

            # 7. 批量处理示例
            print("\n=== 批量处理 ===")
            test_images = [
                Image.new('RGB', (200, 150), color=(255, 0, 0)),
                Image.new('RGB', (200, 150), color=(0, 255, 0)),
                Image.new('RGB', (200, 150), color=(0, 0, 255))
            ]

            batch_results = client.batch_process(
                test_images,
                algorithm="grayscale",
                output_dir="output/batch",
                method="weighted"
            )

            success_count = sum(1 for r in batch_results if r.success)
            print(f"批量处理完成: {success_count}/{len(batch_results)} 成功")

        except ImageProcessingAPIError as e:
            print(f"图像处理API错误: {e}")
        except Exception as e:
            print(f"其他错误: {e}")


def example_file_processing():
    """文件处理示例"""

    with ImageProcessingAPIClient() as client:
        try:
            # 创建测试图像文件
            test_image = Image.new('RGB', (640, 480), color=(100, 150, 200))
            os.makedirs("input", exist_ok=True)
            test_image.save("input/test_image.jpg")

            # 处理图像文件
            result = client.process_image_file(
                input_path="input/test_image.jpg",
                output_path="output/processed_test.jpg",
                algorithm="enhancement",
                enhancement_type="contrast",
                contrast=1.5
            )

            if result.success:
                print(f"文件处理成功: {result}")
                print(f"处理时间: {result.processing_time:.3f}s")
                print(f"原始尺寸: {result.original_size}")
                print(f"处理后尺寸: {result.processed_size}")
            else:
                print(f"文件处理失败: {result.message}")

        except Exception as e:
            print(f"文件处理示例失败: {e}")


if __name__ == "__main__":
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    os.makedirs("output/batch", exist_ok=True)

    print("🚀 图像处理API客户端示例")
    print("=" * 50)

    # 运行基础示例
    example_usage()

    print("\n" + "=" * 50)

    # 运行文件处理示例
    example_file_processing()

    print("\n🎉 示例运行完成！")
    print("📁 结果文件保存在 output/ 目录")
