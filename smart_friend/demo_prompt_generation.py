#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prompt生成功能演示脚本
展示如何使用新创建的API功能
"""

import json
from datetime import datetime
from typing import List, Dict, Any

# 模拟数据结构
class MockChildProfile:
    """模拟儿童档案"""
    def __init__(self):
        self.id = 1
        self.name = "张小明"
        self.nickname = "小明"
        self.age = 8
        self.academic_level = "小学二年级"
        self.school_name = "实验小学"
        self.learning_style = "视觉型学习者"
        self.attention_span_minutes = 25
        self.personality_traits = "活泼好动，好奇心强，喜欢提问"
        self.favorite_subjects = "数学，科学实验"
        self.disliked_subjects = "语文写作"
        self.good_at_subjects = "数学计算，英语口语"
        self.weak_at_subjects = "语文阅读理解，写作"

class MockHomework:
    """模拟今日作业"""
    def __init__(self, subject, task_name, time_slot, difficulty=None, confidence=None):
        self.subject = subject
        self.task_name = task_name
        self.time_slot = time_slot
        self.difficulty = difficulty
        self.confidence_index = confidence
        self.status = "pending"

class MockCompletion:
    """模拟完成记录"""
    def __init__(self, date, subject, completion_rate, accuracy_rate, concentration, enjoyment):
        self.date = date
        self.subject = subject
        self.completion_rate = completion_rate
        self.accuracy_rate = accuracy_rate
        self.concentration_level = concentration
        self.enjoyment_rating = enjoyment
        self.total_duration_minutes = 45.0

def generate_mock_data():
    """生成模拟数据"""
    # 儿童档案
    child_profile = MockChildProfile()
    
    # 今日作业
    today_homework = [
        MockHomework("数学", "两位数加减法练习", "19:00-19:30", "计算速度需要提升", 4),
        MockHomework("语文", "课文朗读和生字练习", "19:30-20:00", "生字记忆困难", 3),
        MockHomework("英语", "单词拼写和对话练习", "20:00-20:20", None, 5)
    ]
    
    # 近期完成情况
    recent_completion = [
        MockCompletion("2024-01-14", "数学", 95.0, 88.0, 4, 4),
        MockCompletion("2024-01-14", "语文", 80.0, 75.0, 3, 2),
        MockCompletion("2024-01-13", "数学", 90.0, 92.0, 4, 5),
        MockCompletion("2024-01-13", "英语", 100.0, 95.0, 5, 5),
        MockCompletion("2024-01-12", "语文", 75.0, 70.0, 2, 2),
    ]
    
    return child_profile, today_homework, recent_completion

def generate_structured_prompt(child_profile, today_homework, recent_completion):
    """生成结构化prompt"""
    prompt_parts = []
    
    # 1. 学生基本信息
    prompt_parts.append("## 学生基本信息")
    prompt_parts.append(f"姓名: {child_profile.name}")
    prompt_parts.append(f"昵称: {child_profile.nickname}")
    prompt_parts.append(f"年龄: {child_profile.age}岁")
    prompt_parts.append(f"学业等级: {child_profile.academic_level}")
    prompt_parts.append(f"学校: {child_profile.school_name}")
    
    # 2. 学习特征
    prompt_parts.append("\n## 学习特征")
    prompt_parts.append(f"学习风格: {child_profile.learning_style}")
    prompt_parts.append(f"注意力持续时间: {child_profile.attention_span_minutes}分钟")
    prompt_parts.append(f"性格特点: {child_profile.personality_traits}")
    
    # 3. 学科偏好和能力
    prompt_parts.append("\n## 学科偏好和能力")
    prompt_parts.append(f"擅长科目: {child_profile.good_at_subjects}")
    prompt_parts.append(f"薄弱科目: {child_profile.weak_at_subjects}")
    prompt_parts.append(f"喜欢的科目: {child_profile.favorite_subjects}")
    prompt_parts.append(f"不喜欢的科目: {child_profile.disliked_subjects}")
    
    # 4. 今日学习任务
    prompt_parts.append("\n## 今日学习任务")
    for i, homework in enumerate(today_homework, 1):
        prompt_parts.append(f"{i}. {homework.subject}")
        prompt_parts.append(f"   任务: {homework.task_name}")
        prompt_parts.append(f"   时间: {homework.time_slot}")
        if homework.difficulty:
            prompt_parts.append(f"   难点: {homework.difficulty}")
        if homework.confidence_index:
            prompt_parts.append(f"   信心指数: {homework.confidence_index}/5")
    
    # 5. 近期学习表现
    prompt_parts.append("\n## 近期学习表现")
    
    # 按学科分组统计
    subject_stats = {}
    for record in recent_completion:
        subject = record.subject
        if subject not in subject_stats:
            subject_stats[subject] = {
                'count': 0,
                'total_completion': 0,
                'total_accuracy': 0,
                'total_concentration': 0,
                'total_enjoyment': 0,
                'total_time': 0
            }
        
        stats = subject_stats[subject]
        stats['count'] += 1
        stats['total_completion'] += record.completion_rate
        stats['total_accuracy'] += record.accuracy_rate
        stats['total_concentration'] += record.concentration_level
        stats['total_enjoyment'] += record.enjoyment_rating
        stats['total_time'] += record.total_duration_minutes
    
    # 计算平均值并输出
    for subject, stats in subject_stats.items():
        count = stats['count']
        prompt_parts.append(f"\n{subject} (共{count}次学习):")
        prompt_parts.append(f"  平均完成率: {stats['total_completion']/count:.1f}%")
        prompt_parts.append(f"  平均正确率: {stats['total_accuracy']/count:.1f}%")
        prompt_parts.append(f"  平均专注度: {stats['total_concentration']/count:.1f}/5")
        prompt_parts.append(f"  平均享受程度: {stats['total_enjoyment']/count:.1f}/5")
        prompt_parts.append(f"  总学习时间: {stats['total_time']:.0f}分钟")
    
    return "\n".join(prompt_parts)

def generate_data_summary(child_profile, today_homework, recent_completion):
    """生成数据摘要"""
    # 基本统计
    summary = {
        'child_name': child_profile.name,
        'today_homework_count': len(today_homework),
        'recent_records_count': len(recent_completion),
        'subjects_covered': list(set(r.subject for r in recent_completion)),
        'avg_completion_rate': sum(r.completion_rate for r in recent_completion) / len(recent_completion),
        'avg_accuracy_rate': sum(r.accuracy_rate for r in recent_completion) / len(recent_completion),
        'avg_concentration_level': sum(r.concentration_level for r in recent_completion) / len(recent_completion),
        'total_study_time': sum(r.total_duration_minutes for r in recent_completion)
    }
    
    # 今日任务统计
    today_subjects = list(set(h.subject for h in today_homework))
    summary['today_subjects'] = today_subjects
    
    confidence_indices = [h.confidence_index for h in today_homework if h.confidence_index]
    if confidence_indices:
        summary['avg_confidence_index'] = sum(confidence_indices) / len(confidence_indices)
    
    return summary

def demo_api_usage():
    """演示API使用方式"""
    print("=" * 80)
    print("Prompt生成API功能演示")
    print("=" * 80)
    
    # 生成模拟数据
    child_profile, today_homework, recent_completion = generate_mock_data()
    
    print("1. 数据概览")
    print("-" * 40)
    print(f"学生: {child_profile.name} ({child_profile.nickname})")
    print(f"今日作业: {len(today_homework)}项")
    print(f"近期记录: {len(recent_completion)}条")
    
    # 生成结构化prompt
    structured_prompt = generate_structured_prompt(child_profile, today_homework, recent_completion)
    
    print("\n2. 生成的结构化Prompt")
    print("-" * 40)
    print(structured_prompt)
    
    # 生成数据摘要
    data_summary = generate_data_summary(child_profile, today_homework, recent_completion)
    
    print("\n3. 数据统计摘要")
    print("-" * 40)
    print(json.dumps(data_summary, ensure_ascii=False, indent=2))
    
    # API调用示例
    print("\n4. API调用示例")
    print("-" * 40)
    
    print("POST请求示例:")
    api_request = {
        "child_id": 1,
        "days_back": 7,
        "include_today_homework": True,
        "include_recent_completion": True,
        "subject_filter": None
    }
    print(f"curl -X POST 'http://localhost:8001/api/v1/prompt-generation/generate' \\")
    print(f"  -H 'Content-Type: application/json' \\")
    print(f"  -d '{json.dumps(api_request)}'")
    
    print("\nGET请求示例:")
    print("curl 'http://localhost:8001/api/v1/prompt-generation/generate/1?days_back=7'")
    
    print("\n数据预览示例:")
    print("curl 'http://localhost:8001/api/v1/prompt-generation/preview/1?days_back=7'")
    
    # 自定义模板示例
    print("\n5. 自定义模板示例")
    print("-" * 40)
    
    custom_template = """
学生{child_name}（{child_age}岁）的个性化学习建议：

基本情况：
- 学习风格：{learning_style}
- 注意力时长：{attention_span}分钟
- 性格特点：{personality_traits}

学科能力：
- 擅长：{good_at_subjects}
- 薄弱：{weak_at_subjects}
- 喜欢：{favorite_subjects}
- 不喜欢：{disliked_subjects}

今日任务：共{today_homework_count}项
近期表现：共{recent_records_count}条记录

建议：根据以上信息，为该学生制定个性化的学习计划和指导策略。
"""
    
    # 模拟模板变量替换
    template_vars = {
        'child_name': child_profile.name,
        'child_age': child_profile.age,
        'learning_style': child_profile.learning_style,
        'attention_span': child_profile.attention_span_minutes,
        'personality_traits': child_profile.personality_traits,
        'good_at_subjects': child_profile.good_at_subjects,
        'weak_at_subjects': child_profile.weak_at_subjects,
        'favorite_subjects': child_profile.favorite_subjects,
        'disliked_subjects': child_profile.disliked_subjects,
        'today_homework_count': len(today_homework),
        'recent_records_count': len(recent_completion)
    }
    
    custom_result = custom_template
    for key, value in template_vars.items():
        custom_result = custom_result.replace(f"{{{key}}}", str(value))
    
    print("自定义模板结果:")
    print(custom_result)
    
    print("\n" + "=" * 80)
    print("演示完成！")
    print("=" * 80)
    
    print("\n📋 功能总结:")
    print("✓ 整合儿童个人档案信息")
    print("✓ 获取今日学习任务")
    print("✓ 分析近期学习表现")
    print("✓ 生成结构化prompt")
    print("✓ 支持自定义模板")
    print("✓ 提供数据统计摘要")
    print("✓ 完整的REST API接口")
    
    print("\n🚀 使用场景:")
    print("• 为AI教学助手提供学生背景信息")
    print("• 生成个性化学习建议")
    print("• 制定针对性的教学计划")
    print("• 分析学习进度和问题")
    print("• 家长沟通和反馈")

if __name__ == "__main__":
    demo_api_usage()
