version: '3.8'

services:
  # Jina Embedding v3/v4 Service
  jina-embeddings:
    image: jinaai/jina-embeddings-v3:latest
    container_name: jina-embeddings-v4
    ports:
      - "${JINA_DOCKER_PORT:-8080}:8080"
    environment:
      - JINA_LOG_LEVEL=INFO
      - JINA_WORKSPACE=/workspace
    volumes:
      - jina_workspace:/workspace
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
    networks:
      - jina-network

  # Alternative: Jina with GPU support (uncomment if you have GPU)
  # jina-embeddings-gpu:
  #   image: jinaai/jina-embeddings-v3:latest-gpu
  #   container_name: jina-embeddings-v4-gpu
  #   ports:
  #     - "${JINA_DOCKER_PORT:-8080}:8080"
  #   environment:
  #     - JIN<PERSON>_LOG_LEVEL=INFO
  #     - CUDA_VISIBLE_DEVICES=0
  #   volumes:
  #     - jina_workspace:/workspace
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: 1
  #             capabilities: [gpu]
  #   networks:
  #     - jina-network

  # Optional: Redis for caching embeddings
  redis-cache:
    image: redis:7-alpine
    container_name: jina-redis-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    networks:
      - jina-network

  # Optional: Monitoring with Prometheus metrics
  # prometheus:
  #   image: prom/prometheus:latest
  #   container_name: jina-prometheus
  #   ports:
  #     - "9090:9090"
  #   volumes:
  #     - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
  #     - prometheus_data:/prometheus
  #   command:
  #     - '--config.file=/etc/prometheus/prometheus.yml'
  #     - '--storage.tsdb.path=/prometheus'
  #     - '--web.console.libraries=/etc/prometheus/console_libraries'
  #     - '--web.console.templates=/etc/prometheus/consoles'
  #   networks:
  #     - jina-network

volumes:
  jina_workspace:
    driver: local
  redis_data:
    driver: local
  # prometheus_data:
  #   driver: local

networks:
  jina-network:
    driver: bridge
