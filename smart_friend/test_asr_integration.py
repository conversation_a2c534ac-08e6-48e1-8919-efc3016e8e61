# -*- coding: utf-8 -*-
"""
测试ASR集成功能
验证真实ASR服务的调用和降级方案
"""

import asyncio
import logging
import tempfile
import os
import wave
import numpy as np
from mainlyk import RealASRProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ASRTester:
    """ASR功能测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 使用真实的火山引擎配置
        self.app_key = "5311525929"
        self.access_key = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"
        
        self.volcano_processor = RealASRProcessor(
            app_key=self.app_key,
            access_key=self.access_key,
            service_type="volcano"
        )
        
        self.doubao_processor = RealASRProcessor(
            app_key=self.app_key,
            access_key=self.access_key,
            service_type="doubao"
        )
        
        logger.info("ASR测试器初始化完成")
    
    def create_test_audio_file(self, duration: float = 2.0, sample_rate: int = 16000) -> str:
        """
        创建测试音频文件
        
        Args:
            duration: 音频时长（秒）
            sample_rate: 采样率
            
        Returns:
            str: 临时音频文件路径
        """
        try:
            # 生成正弦波音频数据（模拟语音）
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            
            # 混合多个频率模拟语音特征
            frequencies = [440, 880, 1320]  # A4, A5, E6
            audio_data = np.zeros_like(t)
            
            for freq in frequencies:
                audio_data += 0.3 * np.sin(2 * np.pi * freq * t)
            
            # 添加一些噪声模拟真实语音
            noise = 0.1 * np.random.normal(0, 1, len(audio_data))
            audio_data += noise
            
            # 归一化
            audio_data = audio_data / np.max(np.abs(audio_data))
            
            # 转换为16位整数
            audio_data = (audio_data * 32767).astype(np.int16)
            
            # 创建临时WAV文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            # 写入WAV文件
            with wave.open(temp_path, 'wb') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())
            
            logger.info(f"创建测试音频文件: {temp_path}, 时长: {duration}秒")
            return temp_path
            
        except Exception as e:
            logger.error(f"创建测试音频文件失败: {e}")
            return None
    
    async def test_volcano_asr(self):
        """测试火山引擎ASR"""
        print("\n" + "=" * 60)
        print("🌋 测试火山引擎ASR")
        print("=" * 60)
        
        try:
            # 检查服务可用性
            is_available = self.volcano_processor.is_service_available()
            print(f"服务可用性: {'✅ 可用' if is_available else '❌ 不可用'}")
            
            # 创建测试音频
            test_audio_path = self.create_test_audio_file(duration=3.0)
            if not test_audio_path:
                print("❌ 创建测试音频失败")
                return
            
            try:
                print("🎤 开始ASR识别...")
                result = await self.volcano_processor.recognize_audio_file(test_audio_path)
                
                if result:
                    print(f"✅ 识别成功: {result}")
                else:
                    print("❌ 识别失败")
                    error = self.volcano_processor.get_last_error()
                    if error:
                        print(f"   错误信息: {error}")
                
            finally:
                # 清理测试文件
                if os.path.exists(test_audio_path):
                    os.remove(test_audio_path)
                    
        except Exception as e:
            print(f"❌ 测试火山引擎ASR失败: {e}")
    
    async def test_doubao_asr(self):
        """测试豆包ASR"""
        print("\n" + "=" * 60)
        print("🤖 测试豆包ASR")
        print("=" * 60)
        
        try:
            # 检查服务可用性
            is_available = self.doubao_processor.is_service_available()
            print(f"服务可用性: {'✅ 可用' if is_available else '❌ 不可用'}")
            
            # 创建测试音频
            test_audio_path = self.create_test_audio_file(duration=3.0)
            if not test_audio_path:
                print("❌ 创建测试音频失败")
                return
            
            try:
                print("🎤 开始ASR识别...")
                result = await self.doubao_processor.recognize_audio_file(test_audio_path)
                
                if result:
                    print(f"✅ 识别成功: {result}")
                else:
                    print("❌ 识别失败")
                    error = self.doubao_processor.get_last_error()
                    if error:
                        print(f"   错误信息: {error}")
                
            finally:
                # 清理测试文件
                if os.path.exists(test_audio_path):
                    os.remove(test_audio_path)
                    
        except Exception as e:
            print(f"❌ 测试豆包ASR失败: {e}")
    
    async def test_fallback_recognition(self):
        """测试降级识别方案"""
        print("\n" + "=" * 60)
        print("🔄 测试降级识别方案")
        print("=" * 60)
        
        try:
            # 创建测试音频
            test_audio_path = self.create_test_audio_file(duration=2.0)
            if not test_audio_path:
                print("❌ 创建测试音频失败")
                return
            
            try:
                print("🎤 开始降级识别...")
                result = await self.volcano_processor._fallback_recognition(test_audio_path)
                
                if result:
                    print(f"✅ 降级识别成功: {result}")
                else:
                    print("❌ 降级识别失败")
                
            finally:
                # 清理测试文件
                if os.path.exists(test_audio_path):
                    os.remove(test_audio_path)
                    
        except Exception as e:
            print(f"❌ 测试降级识别失败: {e}")
    
    async def test_audio_data_recognition(self):
        """测试音频数据识别"""
        print("\n" + "=" * 60)
        print("📊 测试音频数据识别")
        print("=" * 60)
        
        try:
            # 创建测试音频
            test_audio_path = self.create_test_audio_file(duration=2.0)
            if not test_audio_path:
                print("❌ 创建测试音频失败")
                return
            
            try:
                # 读取音频数据
                with open(test_audio_path, 'rb') as f:
                    audio_data = f.read()
                
                print(f"📁 音频数据大小: {len(audio_data)} bytes")
                print("🎤 开始识别音频数据...")
                
                result = await self.volcano_processor.recognize_audio_data(audio_data)
                
                if result:
                    print(f"✅ 识别成功: {result}")
                else:
                    print("❌ 识别失败")
                
            finally:
                # 清理测试文件
                if os.path.exists(test_audio_path):
                    os.remove(test_audio_path)
                    
        except Exception as e:
            print(f"❌ 测试音频数据识别失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始ASR集成测试")
        print("=" * 80)
        
        try:
            # 测试火山引擎ASR
            await self.test_volcano_asr()
            
            # 测试豆包ASR
            await self.test_doubao_asr()
            
            # 测试降级方案
            await self.test_fallback_recognition()
            
            # 测试音频数据识别
            await self.test_audio_data_recognition()
            
            print("\n" + "=" * 80)
            print("✅ ASR集成测试完成")
            
        except Exception as e:
            print(f"\n❌ 测试过程中发生错误: {e}")
            logger.error(f"测试失败: {e}", exc_info=True)


async def main():
    """主函数"""
    tester = ASRTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
