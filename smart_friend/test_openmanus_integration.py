#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test OpenManus Integration with Main System

This script tests the OpenManus system integration and ensures it works
properly with the main FastAPI application.
"""

import sys
import time
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_openmanus_standalone():
    """Test OpenManus as a standalone system"""
    print("🧪 Testing OpenManus Standalone System")
    print("=" * 50)
    
    try:
        from openmanus import initialize_system
        
        # Initialize system
        print("🚀 Initializing system...")
        planner = initialize_system()
        print("✅ System initialized successfully!")
        
        # Test different types of inputs
        test_cases = [
            {
                "input": "Hello! How are you today?",
                "expected_intent": "daily_chat",
                "description": "Casual greeting"
            },
            {
                "input": "Can you help me create a study plan for mathematics?",
                "expected_intent": "study_create_plan",
                "description": "Study plan creation"
            },
            {
                "input": "I need to change my homework schedule",
                "expected_intent": "study_modify_plan", 
                "description": "Schedule modification"
            }
        ]
        
        print(f"\n🎯 Testing {len(test_cases)} scenarios...")
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Test {i}: {test_case['description']}")
            print(f"Input: '{test_case['input']}'")
            
            try:
                # Process input
                result = planner.process_user_input(test_case['input'])
                
                # Display results
                intent = result['intent']
                print(f"✅ Intent: {intent['predicted_intention']} (confidence: {intent['confidence']:.2f})")
                print(f"✅ Task Type: {result['task_type']}")
                print(f"✅ Response: {result['final_response'][:100]}...")
                
                # Check if intent matches expected
                if intent['predicted_intention'] == test_case['expected_intent']:
                    print("✅ Intent classification: CORRECT")
                else:
                    print(f"⚠️  Intent classification: Expected {test_case['expected_intent']}, got {intent['predicted_intention']}")
                    
            except Exception as e:
                print(f"❌ Error in test case {i}: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_integration():
    """Test OpenManus API integration"""
    print("\n🌐 Testing API Integration")
    print("=" * 50)
    
    try:
        # Test if the API endpoints are accessible
        from api.v1.endpoints.openmanus_api import get_openmanus_planner
        
        print("🔗 Testing API endpoint access...")
        planner = get_openmanus_planner()
        print("✅ API planner access successful!")
        
        # Test a simple API call
        test_input = "Hello from API test"
        result = planner.process_user_input(test_input)
        print(f"✅ API processing successful: {result['intent']['predicted_intention']}")
        
        return True
        
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_integration():
    """Test integration with main.py components"""
    print("\n🏗️  Testing Main.py Integration")
    print("=" * 50)
    
    try:
        # Test if main components can import OpenManus
        print("📦 Testing imports...")
        
        # Test Doubao service integration
        from service.doubao_service import DoubaoService
        doubao = DoubaoService()
        print("✅ Doubao service import successful")
        
        # Test if OpenManus can be imported in main context
        from openmanus import OpenManusPlanner
        planner = OpenManusPlanner()
        print("✅ OpenManus planner import successful")
        
        # Test basic functionality
        test_response = planner.client.chat("Test connection")
        if "choices" in test_response:
            print("✅ Doubao connection through OpenManus successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Main integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_interactive_demo():
    """Run an interactive demo"""
    print("\n🎮 Interactive Demo")
    print("=" * 50)
    print("Type 'quit' to exit, or try these examples:")
    print("  - Hello, how are you?")
    print("  - Help me study math")
    print("  - I need to change my schedule")
    print("  - /stats (show statistics)")
    
    try:
        from openmanus import initialize_system
        planner = initialize_system()
        
        while True:
            user_input = input("\n🤖 You: ").strip()
            
            if user_input.lower() in ('quit', 'exit', 'q'):
                print("👋 Goodbye!")
                break
                
            if user_input == '/stats':
                stats = planner.intent_classifier.get_intent_statistics()
                print(f"\n📊 Intent Statistics:")
                print(f"Total Examples: {stats['total_examples']}")
                for intent, info in stats['classes'].items():
                    print(f"  {intent}: {info['count']} examples")
                continue
            
            if not user_input:
                continue
                
            try:
                result = planner.process_user_input(user_input)
                print(f"\n🎯 Intent: {result['intent']['predicted_intention']} ({result['intent']['confidence']:.2f})")
                print(f"📋 Task: {result['task_type']}")
                print(f"🤖 Response: {result['final_response']}")
                
            except Exception as e:
                print(f"❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Main test function"""
    print("🚀 OpenManus Integration Test Suite")
    print("=" * 60)
    
    # Run tests
    tests = [
        ("Standalone System", test_openmanus_standalone),
        ("API Integration", test_api_integration), 
        ("Main Integration", test_main_integration)
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        results[test_name] = test_func()
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 60)
    passed = 0
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    # Offer interactive demo
    if passed == len(tests):
        print("\n🎉 All tests passed! System is ready.")
        demo_choice = input("\nWould you like to run the interactive demo? (y/n): ").strip().lower()
        if demo_choice in ('y', 'yes'):
            run_interactive_demo()
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
