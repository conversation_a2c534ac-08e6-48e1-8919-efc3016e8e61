#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
语音API端点模块

提供统一的ASR和TTS服务的HTTP API接口，可以被其他服务调用
包含语音识别(ASR)和语音合成(TTS)的完整功能
"""

import os
import sys
import logging
import json
import base64
import tempfile
import threading
import time
from typing import Optional, Dict, Any
from flask import Flask, request, jsonify, send_file, redirect, render_template_string
from flask_socketio import SocketIO
from flask_cors import CORS

# 添加项目根目录到Python路径
# 从 api/v1/endpoints/voice_api.py 到项目根目录需要向上3级
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
# 确保使用当前工作目录作为项目根目录
if not os.path.exists(os.path.join(project_root, 'backend')):
    project_root = os.getcwd()
sys.path.insert(0, project_root)
print(f"当前文件: {current_file}")
print(f"项目根目录: {project_root}")
print(f"当前工作目录: {os.getcwd()}")
print(f"backend目录存在: {os.path.exists(os.path.join(project_root, 'backend'))}")
print(f"Python路径: {sys.path[:3]}")  # 只显示前3个路径

# 导入ASR相关模块
from backend.utils.volcano_asr_client import VolcanoASRClient
# 导入TTS相关模块
from service.tts_service import get_tts_service, init_tts_service, play_tts_text, generate_tts_file, get_tts_status
# 导入语音服务
from utils.asr_thread import web_voice_service

# 配置日志
logger = logging.getLogger(__name__)

# 全局ASR客户端实例
asr_client = None
asr_connected = False

# ASR配置
ASR_APP_KEY = "5311525929"
ASR_ACCESS_KEY = "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"

# ASR识别结果存储
asr_results = []
asr_results_lock = threading.Lock()


def asr_result_callback(text: str, is_final: bool):
    """
    ASR识别结果回调函数
    
    Args:
        text (str): 识别的文本
        is_final (bool): 是否为最终结果
    """
    global asr_results
    
    timestamp = time.time()
    result = {
        'text': text,
        'is_final': is_final,
        'timestamp': timestamp
    }
    
    with asr_results_lock:
        asr_results.append(result)
        # 保留最近100个结果
        if len(asr_results) > 100:
            asr_results = asr_results[-100:]
    
    logger.info(f"ASR识别结果: {text} (final: {is_final})")


def register_voice_routes(app: Flask, socketio: SocketIO = None):
    """
    注册语音相关的API路由
    
    Args:
        app (Flask): Flask应用实例
        socketio (SocketIO): SocketIO实例，可选
    """
    
    # ==================== 根路径和文档 ====================

    @app.route('/', methods=['GET'])
    def index():
        """语音API服务首页"""
        html_content = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>语音API服务</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { color: #333; text-align: center; margin-bottom: 30px; }
                h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
                .api-section { margin: 20px 0; }
                .endpoint { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
                .method { display: inline-block; padding: 4px 8px; border-radius: 3px; color: white; font-weight: bold; margin-right: 10px; }
                .get { background-color: #28a745; }
                .post { background-color: #007bff; }
                .path { font-family: monospace; font-weight: bold; }
                .description { color: #666; margin-top: 5px; }
                .status-link { display: inline-block; margin: 20px 0; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; }
                .status-link:hover { background: #218838; }
                .example { background: #f1f3f4; padding: 15px; border-radius: 5px; margin: 10px 0; }
                .example code { font-family: monospace; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎤🔊 语音API服务</h1>
                <p style="text-align: center; color: #666; font-size: 18px;">
                    统一的ASR（语音识别）和TTS（语音合成）HTTP API接口
                </p>

                <a href="/api/voice/status" class="status-link">📊 查看服务状态</a>

                <div class="api-section">
                    <h2>🎤 ASR (语音识别) API</h2>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/asr/connect</span>
                        <div class="description">连接ASR服务</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/asr/disconnect</span>
                        <div class="description">断开ASR连接</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/asr/start</span>
                        <div class="description">启动语音识别</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/asr/stop</span>
                        <div class="description">停止语音识别</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/asr/send_audio</span>
                        <div class="description">发送音频数据（Base64编码的PCM数据）</div>
                    </div>

                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <span class="path">/api/voice/asr/results</span>
                        <div class="description">获取识别结果</div>
                    </div>

                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <span class="path">/api/voice/asr/status</span>
                        <div class="description">获取ASR状态</div>
                    </div>
                </div>

                <div class="api-section">
                    <h2>🔊 TTS (语音合成) API</h2>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/tts/init</span>
                        <div class="description">初始化TTS服务</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/tts/play</span>
                        <div class="description">播放TTS语音</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/tts/generate</span>
                        <div class="description">生成TTS音频文件</div>
                    </div>

                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <span class="path">/api/voice/tts/download/&lt;filename&gt;</span>
                        <div class="description">下载音频文件</div>
                    </div>

                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <span class="path">/api/voice/tts/status</span>
                        <div class="description">获取TTS状态</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/tts/cleanup</span>
                        <div class="description">清理临时文件</div>
                    </div>
                </div>

                <div class="api-section">
                    <h2>🎯 综合 API</h2>

                    <div class="endpoint">
                        <span class="method get">GET</span>
                        <span class="path">/api/voice/status</span>
                        <div class="description">获取整体状态</div>
                    </div>

                    <div class="endpoint">
                        <span class="method post">POST</span>
                        <span class="path">/api/voice/speech_to_speech</span>
                        <div class="description">语音转语音（ASR + TTS）</div>
                    </div>
                </div>

                <div class="api-section">
                    <h2>💡 使用示例</h2>

                    <div class="example">
                        <h3>获取服务状态</h3>
                        <code>curl -X GET http://***********:5002/api/voice/status</code>
                    </div>

                    <div class="example">
                        <h3>播放TTS语音</h3>
                        <code>curl -X POST http://***********:5002/api/voice/tts/play \\<br>
                        &nbsp;&nbsp;-H "Content-Type: application/json" \\<br>
                        &nbsp;&nbsp;-d '{"text": "你好，这是语音合成测试"}'</code>
                    </div>

                    <div class="example">
                        <h3>连接ASR服务</h3>
                        <code>curl -X POST http://***********:5002/api/voice/asr/connect</code>
                    </div>
                </div>

                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
                    <p>📖 详细文档: <code>api/voice_api_examples.md</code></p>
                    <p>🧪 测试脚本: <code>python api/test_voice_api.py</code></p>
                    <p>🎬 演示脚本: <code>python api/demo_voice_api.py</code></p>
                </div>
            </div>
        </body>
        </html>
        """
        return html_content

    @app.route('/docs', methods=['GET'])
    def docs():
        """API文档页面（重定向到首页）"""
        return redirect('/')

    # ==================== ASR相关API ====================
    
    @app.route('/api/voice/asr/connect', methods=['POST'])
    def asr_connect():
        """连接ASR服务API端点"""
        global asr_client, asr_connected
        
        try:
            if asr_connected:
                return jsonify({'success': True, 'message': '已经连接到ASR服务'})
            
            # 创建ASR客户端
            asr_client = VolcanoASRClient(
                app_key=ASR_APP_KEY,
                access_key=ASR_ACCESS_KEY,
                sample_rate=16000,
                channels=1,
                callback=asr_result_callback
            )
            
            # 连接到ASR服务
            success = asr_client.connect()
            if success:
                asr_connected = True
                logger.info("成功连接到火山引擎ASR服务")
                return jsonify({'success': True, 'message': '成功连接到语音识别服务'})
            else:
                logger.error("连接到火山引擎ASR服务失败")
                return jsonify({'success': False, 'message': '连接到语音识别服务失败'})
        
        except Exception as e:
            logger.error(f"连接ASR服务时出错: {e}")
            return jsonify({'success': False, 'message': f'连接失败: {str(e)}'})
    
    @app.route('/api/voice/asr/disconnect', methods=['POST'])
    def asr_disconnect():
        """断开ASR服务连接API端点"""
        global asr_client, asr_connected
        
        try:
            if asr_client and asr_connected:
                asr_client.stop_recognition()
                asr_client.disconnect()
                asr_connected = False
                logger.info("ASR连接已断开")
                return jsonify({'success': True, 'message': 'ASR连接已断开'})
            else:
                return jsonify({'success': True, 'message': 'ASR服务未连接'})
        
        except Exception as e:
            logger.error(f"断开ASR连接时出错: {e}")
            return jsonify({'success': False, 'message': f'断开连接失败: {str(e)}'})
    
    @app.route('/api/voice/asr/start', methods=['POST'])
    def asr_start():
        """启动语音识别API端点"""
        global asr_client, asr_connected, asr_results
        
        try:
            if not asr_connected or not asr_client:
                return jsonify({'success': False, 'message': '请先连接到语音识别服务'}), 400
            
            # 清空之前的识别结果
            with asr_results_lock:
                asr_results.clear()
            
            # 启动语音识别
            success = asr_client.start_recognition()
            if success:
                logger.info("语音识别已启动")
                return jsonify({'success': True, 'message': '语音识别已启动'})
            else:
                logger.error("启动语音识别失败")
                return jsonify({'success': False, 'message': '启动语音识别失败'})
        
        except Exception as e:
            logger.error(f"启动语音识别时出错: {e}")
            return jsonify({'success': False, 'message': f'启动语音识别失败: {str(e)}'})
    
    @app.route('/api/voice/asr/stop', methods=['POST'])
    def asr_stop():
        """停止语音识别API端点"""
        global asr_client, asr_connected
        
        try:
            if asr_client and asr_connected:
                asr_client.stop_recognition()
                logger.info("语音识别已停止")
                return jsonify({'success': True, 'message': '语音识别已停止'})
            else:
                return jsonify({'success': True, 'message': '语音识别服务未运行'})
        
        except Exception as e:
            logger.error(f"停止语音识别时出错: {e}")
            return jsonify({'success': False, 'message': f'停止语音识别失败: {str(e)}'})
    
    @app.route('/api/voice/asr/send_audio', methods=['POST'])
    def asr_send_audio():
        """发送音频数据到ASR服务API端点"""
        global asr_client, asr_connected
        
        try:
            if not asr_connected or not asr_client:
                return jsonify({'success': False, 'message': '请先连接并启动语音识别服务'}), 400
            
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求内容类型错误，需要 application/json'}), 400
            
            data = request.get_json()
            audio_data = data.get('audio_data', '')
            
            if not audio_data:
                return jsonify({'success': False, 'message': '未提供音频数据'}), 400
            
            # 解码base64音频数据
            try:
                audio_bytes = base64.b64decode(audio_data)
            except Exception as e:
                return jsonify({'success': False, 'message': f'音频数据解码失败: {str(e)}'}), 400
            
            # 发送音频数据到ASR服务
            asr_client.send_audio(audio_bytes)
            
            return jsonify({'success': True, 'message': '音频数据已发送'})
        
        except Exception as e:
            logger.error(f"发送音频数据时出错: {e}")
            return jsonify({'success': False, 'message': f'发送音频数据失败: {str(e)}'})
    
    @app.route('/api/voice/asr/results', methods=['GET'])
    def asr_get_results():
        """获取ASR识别结果API端点"""
        global asr_results
        
        try:
            with asr_results_lock:
                # 获取所有结果的副本
                results = asr_results.copy()
            
            return jsonify({
                'success': True,
                'results': results,
                'count': len(results)
            })
        
        except Exception as e:
            logger.error(f"获取ASR结果时出错: {e}")
            return jsonify({'success': False, 'message': f'获取识别结果失败: {str(e)}'})
    
    @app.route('/api/voice/asr/status', methods=['GET'])
    def asr_status():
        """获取ASR服务状态API端点"""
        global asr_client, asr_connected
        
        try:
            status = {
                'connected': asr_connected,
                'client_exists': asr_client is not None,
                'recognition_active': asr_client.is_recognizing if asr_client else False,
                'web_service_active': web_voice_service.recognition_active if hasattr(web_voice_service, 'recognition_active') else False
            }
            
            return jsonify({
                'success': True,
                'status': status
            })
        
        except Exception as e:
            logger.error(f"获取ASR状态时出错: {e}")
            return jsonify({'success': False, 'message': f'获取状态失败: {str(e)}'})
    

    # ==================== TTS相关API ====================

    @app.route('/api/voice/tts/init', methods=['POST'])
    def tts_init():
        """初始化TTS管理器API端点"""
        try:
            success = init_tts_service(socketio)

            if success:
                logger.info("TTS管理器初始化成功")
                return jsonify({'success': True, 'message': 'TTS管理器初始化成功'})
            else:
                logger.error("TTS管理器初始化失败")
                return jsonify({'success': False, 'message': 'TTS管理器初始化失败'})

        except Exception as e:
            logger.error(f"初始化TTS管理器时出错: {e}")
            return jsonify({'success': False, 'message': f'初始化TTS管理器失败: {str(e)}'})

    @app.route('/api/voice/tts/play', methods=['POST'])
    def tts_play():
        """TTS语音播放API端点"""
        try:
            if not request.is_json:
                logger.error("请求内容类型不是 application/json")
                return jsonify({'success': False, 'message': '请求内容类型错误，需要 application/json'}), 400

            data = request.get_json()
            text = data.get('text', '')
            async_mode = data.get('async_mode', True)
            return_audio = data.get('return_audio', False)  # 新增参数：是否返回音频数据

            if not text:
                logger.error("未提供要播放的文本")
                return jsonify({'success': False, 'message': '未提供要播放的文本'}), 400

            logger.info(f"收到TTS播放请求: {text} (return_audio: {return_audio})")

            if return_audio:
                # 生成音频文件并返回音频数据
                from service.tts_service import generate_tts_file
                import base64
                import os

                audio_file = generate_tts_file(text)
                if audio_file and os.path.exists(audio_file):
                    try:
                        with open(audio_file, 'rb') as f:
                            audio_data = f.read()

                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

                        logger.info(f"TTS音频数据生成成功: {text[:30]}... (大小: {len(audio_data)} bytes)")

                        return jsonify({
                            'success': True,
                            'message': f'语音数据生成成功: {text}',
                            'audio_data': audio_base64,
                            'audio_format': 'mp3',
                            'audio_size': len(audio_data),
                            'file_path': audio_file
                        })
                    except Exception as e:
                        logger.error(f"读取音频文件失败: {e}")
                        return jsonify({'success': False, 'message': f'读取音频文件失败: {str(e)}'})
                else:
                    logger.error(f"TTS音频文件生成失败: {text}")
                    return jsonify({'success': False, 'message': f'音频文件生成失败: {text}'})
            else:
                # 原有的播放逻辑
                success = play_tts_text(text, async_mode=async_mode)

                if success:
                    logger.info(f"TTS播放请求已处理: {text}")
                    return jsonify({'success': True, 'message': f'语音播放请求已发送: {text}'})
                else:
                    logger.error(f"TTS播放失败: {text}")
                    return jsonify({'success': False, 'message': f'语音播放失败: {text}'})

        except Exception as e:
            logger.error(f"处理TTS播放请求时出错: {e}")
            return jsonify({'success': False, 'message': f'处理TTS播放请求失败: {str(e)}'})

    @app.route('/api/voice/tts/generate', methods=['POST'])
    def tts_generate():
        """生成TTS音频文件API端点"""
        try:
            if not request.is_json:
                logger.error("请求内容类型不是 application/json")
                return jsonify({'success': False, 'message': '请求内容类型错误，需要 application/json'}), 400

            data = request.get_json()
            text = data.get('text', '')
            output_path = data.get('output_path', None)

            if not text:
                logger.error("未提供要转换的文本")
                return jsonify({'success': False, 'message': '未提供要转换的文本'}), 400

            logger.info(f"收到TTS生成请求: {text}")

            # 生成音频文件
            audio_file = generate_tts_file(text, output_path)

            if audio_file:
                logger.info(f"TTS音频文件生成成功: {audio_file}")
                return jsonify({
                    'success': True,
                    'message': '音频文件生成成功',
                    'file_path': audio_file
                })
            else:
                logger.error(f"TTS音频文件生成失败: {text}")
                return jsonify({'success': False, 'message': '音频文件生成失败'})

        except Exception as e:
            logger.error(f"处理TTS生成请求时出错: {e}")
            return jsonify({'success': False, 'message': f'处理TTS生成请求失败: {str(e)}'})

    @app.route('/api/voice/tts/download/<path:filename>', methods=['GET'])
    def tts_download(filename):
        """下载TTS音频文件API端点"""
        try:
            tts_service = get_tts_service()
            file_path = os.path.join(tts_service.temp_audio_dir, filename)

            if not os.path.exists(file_path):
                logger.error(f"音频文件不存在: {file_path}")
                return jsonify({'success': False, 'message': '音频文件不存在'}), 404

            logger.info(f"下载TTS音频文件: {file_path}")
            return send_file(file_path, as_attachment=True, download_name=filename)

        except Exception as e:
            logger.error(f"下载TTS音频文件时出错: {e}")
            return jsonify({'success': False, 'message': f'下载文件失败: {str(e)}'})

    @app.route('/api/voice/tts/status', methods=['GET'])
    def tts_status():
        """获取TTS服务状态API端点"""
        try:
            status = get_tts_status()
            return jsonify({
                'success': True,
                'status': status
            })

        except Exception as e:
            logger.error(f"获取TTS服务状态时出错: {e}")
            return jsonify({'success': False, 'message': f'获取状态失败: {str(e)}'})

    @app.route('/api/voice/tts/cleanup', methods=['POST'])
    def tts_cleanup():
        """清理TTS临时文件API端点"""
        try:
            data = request.get_json() if request.is_json else {}
            max_files = data.get('max_files', 50)

            tts_service = get_tts_service()
            tts_service.cleanup_temp_files(max_files)

            logger.info(f"TTS临时文件清理完成，保留最新 {max_files} 个文件")
            return jsonify({
                'success': True,
                'message': f'临时文件清理完成，保留最新 {max_files} 个文件'
            })

        except Exception as e:
            logger.error(f"清理TTS临时文件时出错: {e}")
            return jsonify({'success': False, 'message': f'清理临时文件失败: {str(e)}'})

    # ==================== 综合语音API ====================

    @app.route('/api/voice/status', methods=['GET'])
    def voice_status():
        """获取语音服务整体状态API端点"""
        try:
            # ASR状态
            asr_status_info = {
                'connected': asr_connected,
                'client_exists': asr_client is not None,
                'recognition_active': asr_client.is_recognizing if asr_client else False,
                'web_service_active': web_voice_service.recognition_active if hasattr(web_voice_service, 'recognition_active') else False
            }

            # TTS状态
            tts_status_info = get_tts_status()

            return jsonify({
                'success': True,
                'asr_status': asr_status_info,
                'tts_status': tts_status_info
            })

        except Exception as e:
            logger.error(f"获取语音服务状态时出错: {e}")
            return jsonify({'success': False, 'message': f'获取状态失败: {str(e)}'})

    @app.route('/api/voice/speech_to_speech', methods=['POST'])
    def speech_to_speech():
        """语音转语音API端点（ASR + TTS）"""
        try:
            if not request.is_json:
                return jsonify({'success': False, 'message': '请求内容类型错误，需要 application/json'}), 400

            data = request.get_json()
            audio_data = data.get('audio_data', '')
            response_text = data.get('response_text', '')
            async_mode = data.get('async_mode', True)

            if not audio_data and not response_text:
                return jsonify({'success': False, 'message': '需要提供音频数据或响应文本'}), 400

            result = {}

            # 如果提供了音频数据，进行ASR识别
            if audio_data:
                if not asr_connected or not asr_client:
                    return jsonify({'success': False, 'message': 'ASR服务未连接'}), 400

                try:
                    # 解码base64音频数据
                    audio_bytes = base64.b64decode(audio_data)

                    # 清空之前的识别结果
                    with asr_results_lock:
                        asr_results.clear()

                    # 启动识别并发送音频
                    asr_client.start_recognition()
                    asr_client.send_audio(audio_bytes)

                    # 等待识别结果（最多等待5秒）
                    wait_time = 0
                    while wait_time < 5:
                        time.sleep(0.1)
                        wait_time += 0.1

                        with asr_results_lock:
                            if asr_results and asr_results[-1]['is_final']:
                                result['asr_text'] = asr_results[-1]['text']
                                break

                    asr_client.stop_recognition()

                except Exception as e:
                    return jsonify({'success': False, 'message': f'ASR处理失败: {str(e)}'})

            # 如果提供了响应文本，进行TTS合成
            if response_text:
                try:
                    success = play_tts_text(response_text, async_mode=async_mode)
                    if success:
                        result['tts_success'] = True
                        result['tts_message'] = '语音合成成功'
                    else:
                        result['tts_success'] = False
                        result['tts_message'] = '语音合成失败'
                except Exception as e:
                    result['tts_success'] = False
                    result['tts_message'] = f'TTS处理失败: {str(e)}'

            return jsonify({
                'success': True,
                'result': result
            })

        except Exception as e:
            logger.error(f"语音转语音处理时出错: {e}")
            return jsonify({'success': False, 'message': f'语音转语音处理失败: {str(e)}'})

    @app.route('/tts-test')
    def tts_test_page():
        """TTS测试页面"""
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS语音测试</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background-color: #f5f5f5; }
        .container { background: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .form-group { margin: 20px 0; }
        label { display: block; margin-bottom: 8px; font-weight: bold; color: #333; }
        textarea { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 16px; box-sizing: border-box; height: 100px; resize: vertical; }
        button { background: #007bff; color: white; border: none; padding: 15px 30px; border-radius: 6px; cursor: pointer; font-size: 16px; margin: 10px 5px; transition: background-color 0.3s; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .result { margin: 20px 0; padding: 15px; border-radius: 6px; font-family: monospace; white-space: pre-wrap; }
        .result.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .result.info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .audio-section { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 6px; border: 1px solid #dee2e6; }
        audio { width: 100%; margin-top: 10px; }
        .status { text-align: center; margin: 20px 0; font-size: 18px; }
        .loading { color: #007bff; }
        .success-status { color: #28a745; }
        .error-status { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔊 TTS语音合成测试</h1>
        <div class="form-group">
            <label for="ttsText">要合成的文本:</label>
            <textarea id="ttsText" placeholder="输入要转换为语音的文本">你好！欢迎使用TTS语音合成服务。这是一个测试演示，请点击播放按钮听取语音效果。</textarea>
        </div>
        <div style="text-align: center;">
            <button onclick="playTTS()" class="btn-success">🎵 生成并播放语音</button>
            <button onclick="checkStatus()">📊 检查服务状态</button>
            <button onclick="clearResult()">🧹 清空结果</button>
        </div>
        <div id="status" class="status"></div>
        <div class="audio-section" id="audioSection" style="display: none;">
            <label>🎵 音频播放器:</label>
            <audio id="audioPlayer" controls>您的浏览器不支持音频播放。</audio>
        </div>
        <div id="result" class="result info" style="display: none;"></div>
    </div>
    <script>
        function showStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        function appendResult(message, type = 'info') {
            const element = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `[${timestamp}] ${message}\\n`;
            if (element.style.display === 'none') {
                element.textContent = newMessage;
            } else {
                element.textContent += newMessage;
            }
            element.className = `result ${type}`;
            element.style.display = 'block';
            element.scrollTop = element.scrollHeight;
        }
        async function playTTS() {
            const text = document.getElementById('ttsText').value.trim();
            if (!text) {
                showResult('❌ 请输入要合成的文本', 'error');
                showStatus('请输入文本', 'error-status');
                return;
            }
            try {
                showStatus('正在生成TTS音频...', 'loading');
                showResult('正在生成TTS音频...', 'info');
                const generateResponse = await fetch('/api/voice/tts/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: text })
                });
                if (!generateResponse.ok) {
                    throw new Error(`HTTP ${generateResponse.status}: ${generateResponse.statusText}`);
                }
                const generateResult = await generateResponse.json();
                if (generateResult.success && generateResult.file_path) {
                    const fileName = generateResult.file_path.split('/').pop();
                    const downloadUrl = `/api/voice/tts/download/${fileName}`;
                    const audioPlayer = document.getElementById('audioPlayer');
                    const audioSection = document.getElementById('audioSection');
                    audioPlayer.src = downloadUrl;
                    audioSection.style.display = 'block';
                    try {
                        await audioPlayer.play();
                        showStatus('✅ 音频播放中...', 'success-status');
                        appendResult(`✅ TTS音频生成成功\\n文本: ${text}\\n文件: ${fileName}\\n💡 音频正在播放`, 'success');
                    } catch (playError) {
                        showStatus('⚠️ 请手动点击播放', 'loading');
                        appendResult(`✅ TTS音频生成成功，请手动点击播放按钮\\n文件: ${fileName}`, 'success');
                    }
                    audioPlayer.addEventListener('ended', () => {
                        showStatus('🎵 播放完成', 'success-status');
                        appendResult('🎵 音频播放完成', 'info');
                    });
                } else {
                    showStatus('❌ 生成失败', 'error-status');
                    showResult(`❌ TTS音频生成失败: ${generateResult.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus('❌ 请求失败', 'error-status');
                showResult(`❌ TTS请求错误: ${error.message}`, 'error');
            }
        }
        async function checkStatus() {
            try {
                showStatus('正在检查服务状态...', 'loading');
                const response = await fetch('/api/voice/status');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                if (result.success) {
                    const ttsStatus = result.tts_status.initialized ? '✅ 已初始化' : '❌ 未初始化';
                    const ttsAvailable = result.tts_status.tts_available ? '✅ 可用' : '❌ 不可用';
                    showStatus('✅ 服务正常', 'success-status');
                    appendResult(`✅ 服务运行正常\\nTTS状态: ${ttsStatus}\\nTTS可用性: ${ttsAvailable}\\n临时文件: ${result.tts_status.temp_files_count} 个`, 'success');
                } else {
                    showStatus('❌ 服务异常', 'error-status');
                    showResult('❌ 服务状态异常', 'error');
                }
            } catch (error) {
                showStatus('❌ 连接失败', 'error-status');
                showResult(`❌ 连接失败: ${error.message}`, 'error');
            }
        }
        function clearResult() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('result').textContent = '';
            showStatus('', '');
        }
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkStatus, 1000);
        });
    </script>
</body>
</html>'''
        return render_template_string(html_content)

    @app.route('/voice-test')
    def voice_test_complete():
        """完整语音功能测试页面"""
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整语音功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background: #fafafa;
        }
        .section h2 {
            color: #555;
            margin-top: 0;
            font-size: 1.8em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        .form-group {
            margin: 20px 0;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, textarea:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }
        textarea {
            height: 120px;
            resize: vertical;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
        }
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #e83e8c);
        }
        .result {
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
        }
        .result.success {
            background: #d4edda;
            border: 2px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 2px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background: #d1ecf1;
            border: 2px solid #bee5eb;
            color: #0c5460;
        }
        .audio-section {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            border: 2px solid #dee2e6;
        }
        audio {
            width: 100%;
            margin-top: 15px;
            border-radius: 8px;
        }
        .status {
            text-align: center;
            margin: 20px 0;
            font-size: 18px;
            font-weight: bold;
            padding: 15px;
            border-radius: 8px;
        }
        .loading {
            background: #cce5ff;
            color: #0066cc;
            border: 2px solid #99d6ff;
        }
        .success-status {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        .error-status {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        .recording {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .mic-button {
            font-size: 24px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .mic-button.recording {
            background: linear-gradient(45deg, #dc3545, #e83e8c);
            animation: pulse 1s infinite;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .recognition-results {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            min-height: 100px;
        }
        .recognition-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .recognition-item.final {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .recognition-item.interim {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤🔊 完整语音功能测试</h1>

        <!-- 服务状态检查 -->
        <div class="section">
            <h2>📊 服务状态</h2>
            <div class="form-group">
                <label for="serverUrl">语音API服务地址:</label>
                <input type="text" id="serverUrl" value="http://localhost:5002" placeholder="http://localhost:5002">
            </div>
            <div class="controls">
                <button onclick="checkServiceStatus()" class="btn-success">🔍 检查服务状态</button>
                <button onclick="clearAllResults()" class="btn-warning">🧹 清空所有结果</button>
            </div>
            <div id="statusResult" class="result info" style="display: none;"></div>
        </div>

        <!-- TTS语音合成区域 -->
        <div class="section">
            <h2>🔊 TTS 语音合成测试</h2>

            <div class="form-group">
                <label for="ttsText">要合成的文本:</label>
                <textarea id="ttsText" placeholder="输入要转换为语音的文本">你好！这是TTS语音合成测试。我可以将文本转换为自然流畅的语音。</textarea>
            </div>

            <div class="controls">
                <button onclick="generateAndPlayTTS()" class="btn-success">🎵 生成并播放语音</button>
                <button onclick="getTTSStatus()">📊 获取TTS状态</button>
            </div>

            <div id="ttsStatus" class="status" style="display: none;"></div>

            <div class="audio-section" id="ttsAudioSection" style="display: none;">
                <label>🎵 TTS音频播放器:</label>
                <audio id="ttsAudioPlayer" controls>
                    您的浏览器不支持音频播放。
                </audio>
            </div>

            <div id="ttsResult" class="result info" style="display: none;"></div>
        </div>

        <!-- ASR语音识别区域 -->
        <div class="section">
            <h2>🎤 ASR 语音识别测试</h2>

            <div class="controls">
                <button onclick="connectASR()" class="btn-success">🔗 连接ASR服务</button>
                <button onclick="startRecording()" id="recordBtn" class="mic-button">🎤</button>
                <button onclick="stopRecording()" class="btn-danger">⏹️ 停止录音</button>
                <button onclick="getASRResults()">📋 获取识别结果</button>
                <button onclick="disconnectASR()" class="btn-danger">🔌 断开连接</button>
            </div>

            <div id="asrStatus" class="status" style="display: none;"></div>

            <div class="recognition-results" id="recognitionResults">
                <p style="color: #666; text-align: center;">语音识别结果将显示在这里...</p>
            </div>

            <div id="asrResult" class="result info" style="display: none;"></div>
        </div>

        <!-- 综合测试区域 -->
        <div class="section">
            <h2>🎯 综合功能测试</h2>

            <div class="controls">
                <button onclick="runFullTest()" class="btn-success">🚀 运行完整测试</button>
                <button onclick="testSpeechToSpeech()" class="btn-warning">🔄 语音转语音测试</button>
            </div>

            <div id="fullTestResult" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let serverUrl = 'http://localhost:5002';
        let isRecording = false;
        let mediaRecorder = null;
        let audioChunks = [];

        // 更新服务器地址
        function updateServerUrl() {
            serverUrl = document.getElementById('serverUrl').value.trim();
            if (!serverUrl.startsWith('http')) {
                serverUrl = 'http://' + serverUrl;
            }
        }

        // 显示状态
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            element.textContent = `[${timestamp}] ${message}`;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }

        // 添加结果
        function appendResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `[${timestamp}] ${message}\\n`;

            if (element.style.display === 'none') {
                element.textContent = newMessage;
            } else {
                element.textContent += newMessage;
            }

            element.className = `result ${type}`;
            element.style.display = 'block';
            element.scrollTop = element.scrollHeight;
        }

        // 检查服务状态
        async function checkServiceStatus() {
            updateServerUrl();

            try {
                showResult('statusResult', '正在检查服务状态...', 'info');

                const response = await fetch('/api/voice/status');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (result.success) {
                    const ttsStatus = result.tts_status.initialized ? '✅ 已初始化' : '❌ 未初始化';
                    const ttsAvailable = result.tts_status.tts_available ? '✅ 可用' : '❌ 不可用';
                    const asrStatus = result.asr_status.connected ? '✅ 已连接' : '⚪ 未连接';

                    showResult('statusResult',
                        `✅ 服务运行正常\\n` +
                        `TTS状态: ${ttsStatus}\\n` +
                        `TTS可用性: ${ttsAvailable}\\n` +
                        `ASR状态: ${asrStatus}\\n` +
                        `临时文件: ${result.tts_status.temp_files_count} 个`,
                        'success');
                } else {
                    showResult('statusResult', '❌ 服务状态异常', 'error');
                }
            } catch (error) {
                showResult('statusResult', `❌ 连接失败: ${error.message}`, 'error');
            }
        }

        // TTS生成并播放语音
        async function generateAndPlayTTS() {
            const text = document.getElementById('ttsText').value.trim();

            if (!text) {
                showResult('ttsResult', '❌ 请输入要合成的文本', 'error');
                showStatus('ttsStatus', '请输入文本', 'error-status');
                return;
            }

            try {
                showStatus('ttsStatus', '正在生成TTS音频...', 'loading');
                showResult('ttsResult', '正在生成TTS音频...', 'info');

                // 生成音频文件
                const generateResponse = await fetch('/api/voice/tts/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: text
                    })
                });

                if (!generateResponse.ok) {
                    throw new Error(`HTTP ${generateResponse.status}: ${generateResponse.statusText}`);
                }

                const generateResult = await generateResponse.json();

                if (generateResult.success && generateResult.file_path) {
                    // 获取文件名
                    const fileName = generateResult.file_path.split('/').pop();

                    // 设置音频播放器
                    const downloadUrl = `/api/voice/tts/download/${fileName}`;
                    const audioPlayer = document.getElementById('ttsAudioPlayer');
                    const audioSection = document.getElementById('ttsAudioSection');

                    audioPlayer.src = downloadUrl;
                    audioSection.style.display = 'block';

                    // 自动播放
                    try {
                        await audioPlayer.play();
                        showStatus('ttsStatus', '✅ 音频播放中...', 'success-status');
                        appendResult('ttsResult', `✅ TTS音频生成成功\\n文本: ${text}\\n文件: ${fileName}\\n💡 音频正在播放`, 'success');
                    } catch (playError) {
                        showStatus('ttsStatus', '⚠️ 请手动点击播放', 'loading');
                        appendResult('ttsResult', `✅ TTS音频生成成功，请手动点击播放按钮\\n文件: ${fileName}`, 'success');
                    }

                    // 播放完成事件
                    audioPlayer.addEventListener('ended', () => {
                        showStatus('ttsStatus', '🎵 播放完成', 'success-status');
                        appendResult('ttsResult', '🎵 音频播放完成', 'info');
                    });

                } else {
                    showStatus('ttsStatus', '❌ 生成失败', 'error-status');
                    showResult('ttsResult', `❌ TTS音频生成失败: ${generateResult.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showStatus('ttsStatus', '❌ 请求失败', 'error-status');
                showResult('ttsResult', `❌ TTS请求错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkServiceStatus, 1000);
        });
    </script>
</body>
</html>'''
        return render_template_string(html_content)

    logger.info("语音API路由已注册")


def create_voice_app():
    """
    创建独立的语音服务Flask应用

    Returns:
        tuple: (Flask应用实例, SocketIO实例)
    """
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'voice_service_secret_key_2024'

    # 配置CORS，允许跨域请求
    CORS(app, origins="*", allow_headers=["Content-Type", "Authorization"], methods=["GET", "POST", "OPTIONS"])

    # 创建SocketIO实例
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

    # 注册语音路由
    register_voice_routes(app, socketio)

    return app, socketio


def init_voice_services(socketio=None):
    """
    初始化语音服务

    Args:
        socketio: SocketIO实例

    Returns:
        dict: 初始化结果
    """
    result = {
        'asr_init': False,
        'tts_init': False,
        'errors': []
    }

    # 初始化TTS服务
    try:
        tts_success = init_tts_service(socketio)
        result['tts_init'] = tts_success
        if tts_success:
            logger.info("TTS服务初始化成功")
        else:
            logger.error("TTS服务初始化失败")
            result['errors'].append("TTS服务初始化失败")
    except Exception as e:
        logger.error(f"TTS服务初始化异常: {e}")
        result['errors'].append(f"TTS服务初始化异常: {str(e)}")

    # ASR服务不需要预初始化，在连接时初始化
    result['asr_init'] = True
    logger.info("ASR服务准备就绪（将在连接时初始化）")

    return result


if __name__ == "__main__":
    """运行独立的语音服务"""
    # 创建应用
    app, socketio = create_voice_app()

    # 初始化语音服务
    init_result = init_voice_services(socketio)

    if init_result['tts_init']:
        logger.info("语音服务初始化成功")
        print("语音服务初始化成功")
    else:
        logger.error(f"语音服务初始化失败: {init_result['errors']}")
        print(f"语音服务初始化失败: {init_result['errors']}")

    # 启动服务
    logger.info("启动语音API服务...")
    print("启动语音API服务...")
    print("ASR API端点:")
    print("  POST /api/voice/asr/connect - 连接ASR服务")
    print("  POST /api/voice/asr/disconnect - 断开ASR连接")
    print("  POST /api/voice/asr/start - 启动语音识别")
    print("  POST /api/voice/asr/stop - 停止语音识别")
    print("  POST /api/voice/asr/send_audio - 发送音频数据")
    print("  GET  /api/voice/asr/results - 获取识别结果")
    print("  GET  /api/voice/asr/status - 获取ASR状态")
    print()
    print("TTS API端点:")
    print("  POST /api/voice/tts/init - 初始化TTS服务")
    print("  POST /api/voice/tts/play - 播放TTS语音")
    print("  POST /api/voice/tts/generate - 生成TTS音频文件")
    print("  GET  /api/voice/tts/download/<filename> - 下载音频文件")
    print("  GET  /api/voice/tts/status - 获取TTS状态")
    print("  POST /api/voice/tts/cleanup - 清理临时文件")
    print()
    print("综合API端点:")
    print("  GET  /api/voice/status - 获取整体状态")
    print("  POST /api/voice/speech_to_speech - 语音转语音")
    print()
    print("服务运行在: http://0.0.0.0:5002")

    socketio.run(app, host='0.0.0.0', port=5002, debug=False, allow_unsafe_werkzeug=True)
