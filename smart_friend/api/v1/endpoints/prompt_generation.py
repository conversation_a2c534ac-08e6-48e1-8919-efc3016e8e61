from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any

from core.planning.schemas import PromptGenerationRequest, PromptGenerationResponse
from service.daily_learning_service import DailyLearningService

router = APIRouter()

@router.post("/generate-prompt", response_model=PromptGenerationResponse)
async def generate_comprehensive_prompt(
    request: PromptGenerationRequest,
    learning_service: DailyLearningService = Depends(get_daily_learning_service)
):
    """
    生成综合Prompt，包含个人档案、强弱学科和作业完成情况
    """
    try:
        result = learning_service.generate_comprehensive_prompt(
            child_id=request.child_id,
            days=request.days,
            include_profile=request.include_profile,
            include_subjects=request.include_subjects,
            include_homework=request.include_homework
        )
        
        return PromptGenerationResponse(**result)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成Prompt失败: {str(e)}"
        )