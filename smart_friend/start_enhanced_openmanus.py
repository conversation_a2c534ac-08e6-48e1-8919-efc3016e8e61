#!/usr/bin/env python3
"""
Enhanced OpenManus Startup Script with Jina Docker Integration

This script provides a simple way to start the Enhanced OpenManus system
with automatic Jina Docker container management.
"""

import sys
import argparse
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

from openmanus import initialize_system, USE_MOCK_MODE

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check required packages
    required_packages = [
        'requests', 'sqlite3', 'sklearn', 'numpy'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages available")
    
    # Check Docker (if not in mock mode)
    if not USE_MOCK_MODE:
        import subprocess
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Docker available: {result.stdout.strip()}")
            else:
                print("⚠️ Docker not available, will use mock mode")
        except:
            print("⚠️ Docker not available, will use mock mode")
    
    return True

def setup_directories():
    """Create necessary directories"""
    print("📁 Setting up directories...")
    
    directories = [
        Path(__file__).parent / "cache",
        Path(__file__).parent / "logs"
    ]
    
    for directory in directories:
        directory.mkdir(exist_ok=True)
        print(f"✅ Directory ready: {directory}")

def start_interactive_mode(planner):
    """Start interactive chat mode"""
    print("\n💬 Enhanced OpenManus Interactive Mode")
    print("=" * 50)
    print("Available commands:")
    print("  - Normal chat: Just type your message")
    print("  - /intent <text>: Classify intent of text")
    print("  - /search <query>: Search dataset")
    print("  - /report: Generate sample summary report")
    print("  - /stats: Show intent classification statistics")
    print("  - /docker: Show Docker container status")
    print("  - /cache: Show cache statistics")
    print("  - /help: Show this help")
    print("  - quit/exit/q: Exit")
    print("=" * 50)
    
    try:
        while True:
            user_input = input("\n🤖 You: ").strip()
            
            if user_input.lower() in ('exit', 'quit', 'q'):
                print("👋 Goodbye!")
                break
            
            elif user_input == '/help':
                print("Available commands:")
                print("  /intent <text> - Classify intent")
                print("  /search <query> - Search dataset")
                print("  /report - Generate summary report")
                print("  /stats - Show statistics")
                print("  /docker - Docker status")
                print("  /cache - Cache statistics")
                
            elif user_input.startswith('/intent '):
                text = user_input[8:]
                result = planner.process_task("intent_classification", {"text": text})
                print(f"🎯 Intent: {result}")
                
            elif user_input.startswith('/search '):
                query = user_input[8:]
                result = planner.process_task("semantic_search", {"query": query})
                print(f"🔍 Search Results: {result}")
                
            elif user_input == '/report':
                sample_data = {
                    "completion_rate_percentage": "88%",
                    "most_focused_times_or_tasks": "Morning study sessions",
                    "total_study_time_and_active_engagement": "2.5 hours with 92% engagement"
                }
                result = planner.process_task("generate_summary_report", {"data": sample_data})
                print(f"📊 Summary Report: {result}")
                
            elif user_input == '/stats':
                stats = planner.intent_classifier.get_intent_statistics()
                print(f"📈 Intent Statistics:")
                print(f"  Total Examples: {stats['total_examples']}")
                for intent, info in stats['classes'].items():
                    percentage = stats['class_distribution'][intent] * 100
                    print(f"  {intent}: {info['count']} examples ({percentage:.1f}%)")
                    
            elif user_input == '/docker':
                health = planner.embedding_client.check_health()
                print(f"🐳 Docker Status: {'Healthy' if health else 'Not available'}")
                if health:
                    print(f"  Endpoint: {planner.embedding_client.base_url}")
                    print(f"  Health URL: {planner.embedding_client.health_endpoint}")
                    
            elif user_input == '/cache':
                if planner.embedding_client.cache_enabled:
                    try:
                        import sqlite3
                        conn = sqlite3.connect(planner.embedding_client.cache_path)
                        cursor = conn.cursor()
                        cursor.execute("SELECT COUNT(*) FROM embedding_cache")
                        cache_count = cursor.fetchone()[0]
                        conn.close()
                        print(f"💾 Cache: {cache_count} entries")
                    except Exception as e:
                        print(f"💾 Cache: Error reading cache - {e}")
                else:
                    print("💾 Cache: Disabled")
                    
            else:
                # Regular chat with intent classification
                try:
                    intent_result = planner.intent_classifier.classify_intent(user_input)
                    intent_info = f"[{intent_result['predicted_intention']} ({intent_result['confidence']:.2f})]"
                    
                    # Process using appropriate task based on intent
                    if intent_result['predicted_intention'] == 'study_create_plan':
                        task = "research_and_summarize"
                    elif intent_result['predicted_intention'] == 'study_modify_plan':
                        task = "semantic_search"
                    else:
                        task = "research_and_summarize"
                    
                    response = planner.process_task(task, {"query": user_input})
                    print(f"\n🤖 Assistant {intent_info}: {response}")
                    
                except Exception as e:
                    print(f"❌ Error processing request: {e}")
                
    except KeyboardInterrupt:
        print("\n👋 Interrupted. Goodbye!")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Enhanced OpenManus with Jina Docker")
    parser.add_argument("--mock", action="store_true", help="Use mock mode (no Docker)")
    parser.add_argument("--no-docker", action="store_true", help="Don't auto-start Docker")
    parser.add_argument("--test", action="store_true", help="Run tests instead of interactive mode")
    
    args = parser.parse_args()
    
    # Override mock mode if requested
    if args.mock:
        import openmanus
        openmanus.USE_MOCK_MODE = True
        print("🎭 Mock mode enabled")
    
    print("🚀 Enhanced OpenManus with Jina Docker Integration")
    print("=" * 60)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Initialize system
    try:
        auto_start_docker = not args.no_docker and not args.mock
        planner = initialize_system(auto_start_docker=auto_start_docker)
        print("✅ System initialization completed successfully!")
        
    except Exception as e:
        print(f"❌ System initialization failed: {e}")
        print("Try running with --mock flag for testing")
        sys.exit(1)
    
    # Run tests or start interactive mode
    if args.test:
        print("\n🧪 Running tests...")
        try:
            from test.test_enhanced_openmanus import main as test_main
            test_main()
        except ImportError:
            print("❌ Test module not found")
            sys.exit(1)
    else:
        start_interactive_mode(planner)

if __name__ == "__main__":
    main()
