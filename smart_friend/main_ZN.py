# FastAPI主入口
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config.config import settings
from core.planning.endpoints import daily_learning, planning
from core.file_upload import endpoints as file_upload
from core.user_management.api import user_management_router
from core.prompt_generation.api import prompt_generation_router

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理",
    version="1.0.0",
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
# 用户信息管理API路由
app.include_router(
    user_management_router,
    prefix=f"{settings.API_V1_STR}/user-management",
)

# InfluxDB每日学习数据路由
app.include_router(
    daily_learning.router,
    prefix=f"{settings.API_V1_STR}/daily-learning",
    tags=["daily-learning"]
)

# InfluxDB学习计划数据路由
app.include_router(
    planning.router,
    prefix=f"{settings.API_V1_STR}/planning",
    tags=["planning"]
)

# 文件上传路由
app.include_router(
    file_upload.router,
    prefix=f"{settings.API_V1_STR}/files",
    tags=["file-upload"]
)

# Prompt生成API路由
app.include_router(
    prompt_generation_router,
    prefix=f"{settings.API_V1_STR}/prompt-generation",
    tags=["prompt-generation"]
)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI Child Learning Assistant API"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

def main():
    """主函数 - 用于调试和启动应用"""
    print("🚀 启动 Smart Friend FastAPI 应用...")
    print(f"📋 应用标题: {app.title}")
    print(f"📋 应用版本: 1.0.0")

    # 打印所有注册的路由
    print("\n📡 已注册的路由:")
    route_count = 0
    file_route_count = 0

    for route in app.routes:
        if hasattr(route, 'path'):
            route_count += 1
            print(f"  - {route.path}")
            if '/files' in route.path:
                file_route_count += 1
                print(f"    ✅ 文件上传路由: {route.path}")

    print(f"\n📊 路由统计:")
    print(f"  - 总路由数: {route_count}")
    print(f"  - 文件上传路由数: {file_route_count}")

    # 检查文件上传模块
    print("\n🔍 检查文件上传模块:")
    try:
        from core.file_upload.config import FileUploadConfig
        config = FileUploadConfig()
        print(f"  ✅ 配置加载成功")
        print(f"  ✅ 允许扩展名: {len(config.ALLOWED_EXTENSIONS)} 个")
        print(f"  ✅ 允许路径: {len(config.ALLOWED_UPLOAD_PATHS)} 个")
    except Exception as e:
        print(f"  ❌ 配置加载失败: {e}")

    try:
        from service.file_upload_service import FileUploadService
        service = FileUploadService()
        print(f"  ✅ 服务初始化成功")
    except Exception as e:
        print(f"  ❌ 服务初始化失败: {e}")        

    print(f"\n🌐 启动服务器...")
    print(f"📍 访问地址: http://localhost:8091")
    print(f"📖 API文档: http://localhost:8091/docs")
    print(f"🔧 文件上传API: http://localhost:8091/api/v1/files/")

    return app


if __name__ == "__main__":
    import uvicorn

    # 调用main函数进行初始化检查
    app_instance = main()

    # 启动服务器
    uvicorn.run(app_instance, host="0.0.0.0", port=8091)