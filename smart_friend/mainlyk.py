# -*- coding: utf-8 -*-
"""
MainLYK.py - 智能学习伙伴主工作流程
实现从多模态输入（语音/文本/图像）到最终计划表生成的完整工作流程

工作流程：
1. 多模态输入处理（语音/文本/图像）
2. 调用task_input_service.py处理任务输入
3. 数据存储到关系数据库
4. 生成每日任务的prompt（core/prompt_generation）
5. 调用豆包模型（api/v1/endpoints/doubao.py）
6. 生成计划表
7. 前端呈现（smart_friend.html）

设计原则：
- 模块化架构，单一职责原则
- 面向对象设计，广泛使用类和继承
- 异步处理，提高性能
- 完整的错误处理和日志记录
"""

import asyncio
import logging
import json
import base64
import tempfile
import os
import time
from typing import Dict, Any, Optional, Union, List
from datetime import datetime, timezone
from pathlib import Path

# 导入ASR和TTS工具类（简化导入，避免依赖问题）
try:
    from backend.utils.tts_utils import SimpleTTSClient
    TTS_AVAILABLE = True
except ImportError:
    TTS_AVAILABLE = False
    print("TTS模块不可用，将使用模拟功能")

# 导入任务输入处理服务
from service.task_input_service import TaskInputService
from service.multimodal_task_input_service import MultimodalTaskInputService

# 导入prompt生成服务
from core.prompt_generation.service import PromptGenerationService
from core.prompt_generation.schemas import TaskPromptRequest

# 导入豆包模型服务
from service.doubao_service import get_doubao_service

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class RealASRProcessor:
    """
    真实的ASR处理器

    使用实际的ASR服务进行语音识别，支持火山引擎和豆包ASR
    """

    def __init__(self, app_key: str, access_key: str, service_type: str = "volcano"):
        """
        初始化ASR处理器

        Args:
            app_key: 应用密钥
            access_key: 访问密钥
            service_type: ASR服务类型 ("volcano" 或 "doubao")
        """
        self.app_key = app_key
        self.access_key = access_key
        self.service_type = service_type.lower()
        self.asr_client = None
        self.recognition_result = None
        self.recognition_complete = False
        self.recognition_error = None

        logger.info(f"真实ASR处理器初始化完成，服务类型: {self.service_type}")

    def _initialize_asr_client(self):
        """初始化ASR客户端"""
        try:
            # 导入ASR工具类
            from backend.utils.asr_utils import VolcanoASRClient, DoubaoASRClient

            if self.service_type == "volcano":
                self.asr_client = VolcanoASRClient(
                    app_key=self.app_key,
                    access_key=self.access_key,
                    sample_rate=16000,
                    channels=1,
                    enable_punctuation=True,
                    enable_itn=True
                )
            elif self.service_type == "doubao":
                self.asr_client = DoubaoASRClient(
                    app_key=self.app_key,
                    access_key=self.access_key,
                    sample_rate=16000,
                    channels=1
                )
            else:
                raise ValueError(f"不支持的ASR服务类型: {self.service_type}")

            logger.info(f"{self.service_type.upper()} ASR客户端初始化成功")
            return True

        except Exception as e:
            logger.error(f"初始化ASR客户端失败: {e}")
            return False

    async def recognize_audio_file(self, audio_file_path: str) -> Optional[str]:
        """
        识别音频文件

        Args:
            audio_file_path: 音频文件路径

        Returns:
            Optional[str]: 识别结果
        """
        try:
            logger.info(f"开始识别音频文件: {audio_file_path}")

            # 检查文件是否存在
            if not os.path.exists(audio_file_path):
                logger.error(f"音频文件不存在: {audio_file_path}")
                return None

            # 初始化ASR客户端
            if not self.asr_client:
                if not self._initialize_asr_client():
                    logger.error("ASR客户端初始化失败，使用模拟结果")
                    return await self._fallback_recognition(audio_file_path)

            # 读取音频文件
            try:
                audio_data = self._load_audio_file(audio_file_path)
                if audio_data is None:
                    logger.error("音频文件加载失败")
                    return await self._fallback_recognition(audio_file_path)

                # 执行实际的ASR识别
                result = await self._perform_real_asr(audio_data)

                if result:
                    logger.info(f"ASR识别成功: {result[:50]}...")
                    return result
                else:
                    logger.warning("ASR识别失败，使用模拟结果")
                    return await self._fallback_recognition(audio_file_path)

            except Exception as e:
                logger.error(f"ASR识别过程出错: {e}")
                return await self._fallback_recognition(audio_file_path)

        except Exception as e:
            logger.error(f"识别音频文件失败: {e}")
            return None

    async def recognize_audio_data(self, audio_data: bytes) -> Optional[str]:
        """
        识别音频数据

        Args:
            audio_data: 音频数据

        Returns:
            Optional[str]: 识别结果
        """
        try:
            # 保存到临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name

            try:
                # 调用文件识别
                return await self.recognize_audio_file(temp_path)
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        except Exception as e:
            logger.error(f"识别音频数据失败: {e}")
            return None

    def _load_audio_file(self, audio_file_path: str) -> Optional[bytes]:
        """
        加载音频文件

        Args:
            audio_file_path: 音频文件路径

        Returns:
            Optional[bytes]: 音频数据
        """
        try:
            with open(audio_file_path, 'rb') as f:
                audio_data = f.read()

            # 如果是WAV文件，需要处理格式
            if audio_file_path.lower().endswith('.wav'):
                # 简单的WAV文件处理，跳过文件头
                if len(audio_data) > 44:  # WAV文件头通常是44字节
                    audio_data = audio_data[44:]  # 跳过WAV文件头

            logger.info(f"音频文件加载成功，数据大小: {len(audio_data)} bytes")
            return audio_data

        except Exception as e:
            logger.error(f"加载音频文件失败: {e}")
            return None

    async def _perform_real_asr(self, audio_data: bytes) -> Optional[str]:
        """
        执行真实的ASR识别

        Args:
            audio_data: 音频数据

        Returns:
            Optional[str]: 识别结果
        """
        try:
            # 重置识别状态
            self.recognition_result = None
            self.recognition_complete = False
            self.recognition_error = None

            # 定义结果回调函数
            def on_recognition_result(text: str, is_final: bool):
                if is_final and text.strip():
                    self.recognition_result = text.strip()
                    self.recognition_complete = True
                    logger.info(f"收到最终识别结果: {text[:50]}...")

            # 连接ASR服务
            if not self.asr_client.connect():
                logger.error("连接ASR服务失败")
                return None

            try:
                # 启动识别
                if not self.asr_client.start_recognition(on_recognition_result):
                    logger.error("启动ASR识别失败")
                    return None

                # 分块发送音频数据
                chunk_size = 1024  # 每次发送1KB
                for i in range(0, len(audio_data), chunk_size):
                    chunk = audio_data[i:i + chunk_size]
                    self.asr_client.send_audio(chunk)
                    await asyncio.sleep(0.01)  # 小延迟模拟实时发送

                # 停止识别
                self.asr_client.stop_recognition()

                # 等待识别完成
                timeout = 10  # 10秒超时
                start_time = time.time()
                while not self.recognition_complete and time.time() - start_time < timeout:
                    await asyncio.sleep(0.1)

                if self.recognition_complete and self.recognition_result:
                    return self.recognition_result
                else:
                    logger.warning("ASR识别超时或无结果")
                    return None

            finally:
                # 断开连接
                self.asr_client.disconnect()

        except Exception as e:
            logger.error(f"执行ASR识别失败: {e}")
            self.recognition_error = str(e)
            return None

    async def _fallback_recognition(self, audio_file_path: str) -> Optional[str]:
        """
        降级识别方案（使用模拟结果）

        Args:
            audio_file_path: 音频文件路径

        Returns:
            Optional[str]: 模拟识别结果
        """
        try:
            logger.info("使用降级识别方案")

            # 获取文件大小用于生成不同的模拟结果
            file_size = os.path.getsize(audio_file_path)

            # 模拟处理时间
            processing_time = min(max(file_size / 10000, 1), 3)  # 1-3秒
            await asyncio.sleep(processing_time)

            # 模拟结果库
            mock_results = [
                "今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》",
                "英语作业是单词表第5页，科学作业是观察植物生长记录",
                "完成语文阅读理解第二课，数学计算题20道，英语听力练习30分钟",
                "背诵课文第二段，完成数学应用题，练习英语对话",
                "物理作业是实验报告，化学作业是元素周期表背诵",
                "历史作业是第五章总结，地理作业是地图标注练习"
            ]

            # 根据文件大小和时间选择结果
            result_index = (file_size + int(time.time())) % len(mock_results)
            result = mock_results[result_index]

            logger.info(f"降级识别完成: {result[:50]}...")
            return result

        except Exception as e:
            logger.error(f"降级识别失败: {e}")
            return "今天有学习任务需要完成"  # 最基本的默认结果

    def get_last_error(self) -> Optional[str]:
        """获取最后一次识别的错误信息"""
        return self.recognition_error

    def is_service_available(self) -> bool:
        """检查ASR服务是否可用"""
        try:
            if not self.asr_client:
                return self._initialize_asr_client()
            return True
        except Exception:
            return False


class MultimodalInputProcessor:
    """
    多模态输入处理器

    负责处理语音、文本、图像三种输入源
    """

    def __init__(self):
        """初始化多模态输入处理器"""
        self.asr_processor = None
        self.tts_manager = None
        self.task_input_service = TaskInputService()
        self.multimodal_service = MultimodalTaskInputService()

        # ASR配置
        self.asr_config = {
            "app_key": "5311525929",
            "access_key": "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23"
        }

        # TTS配置
        self.tts_config = {
            "app_id": "5311525929",
            "token": "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
            "speaker": "zh_female_shuangkuaisisi_moon_bigtts"
        }

        logger.info("多模态输入处理器初始化完成")

    def initialize_asr(self) -> bool:
        """
        初始化ASR服务

        Returns:
            bool: 是否成功初始化
        """
        try:
            self.asr_processor = RealASRProcessor(
                app_key=self.asr_config["app_key"],
                access_key=self.asr_config["access_key"],
                service_type="volcano"  # 默认使用火山引擎
            )
            logger.info("ASR服务初始化成功")
            return True
        except Exception as e:
            logger.error(f"ASR服务初始化失败: {e}")
            return False

    def initialize_tts(self) -> bool:
        """
        初始化TTS服务

        Returns:
            bool: 是否成功初始化
        """
        try:
            if TTS_AVAILABLE:
                self.tts_manager = SimpleTTSClient(
                    app_id=self.tts_config["app_id"],
                    token=self.tts_config["token"],
                    speaker=self.tts_config["speaker"]
                )
                logger.info("TTS服务初始化成功")
                return True
            else:
                logger.warning("TTS模块不可用，使用模拟TTS")
                self.tts_manager = "mock_tts"  # 模拟TTS管理器
                return True
        except Exception as e:
            logger.error(f"TTS服务初始化失败: {e}")
            return False

    async def process_voice_input(self, audio_data: bytes, child_id: int) -> Dict[str, Any]:
        """
        处理语音输入

        Args:
            audio_data: 音频数据
            child_id: 学生ID

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的语音输入")

            # 初始化ASR（如果未初始化）
            if not self.asr_processor:
                if not self.initialize_asr():
                    return {
                        "success": False,
                        "message": "ASR服务初始化失败",
                        "input_type": "voice"
                    }

            # 语音转文字
            voice_text = await self._audio_to_text(audio_data)

            if not voice_text:
                return {
                    "success": False,
                    "message": "语音识别失败，未能提取文字内容",
                    "input_type": "voice"
                }

            logger.info(f"语音识别结果: {voice_text[:100]}...")

            # 调用任务输入服务处理
            result = await self.task_input_service.process_voice_input(child_id, voice_text)

            return result

        except Exception as e:
            logger.error(f"处理语音输入时发生错误: {e}")
            return {
                "success": False,
                "message": f"处理语音输入失败: {str(e)}",
                "input_type": "voice"
            }

    async def process_text_input(self, text_content: str, child_id: int) -> Dict[str, Any]:
        """
        处理文本输入

        Args:
            text_content: 文本内容
            child_id: 学生ID

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的文本输入")

            # 直接调用任务输入服务处理
            result = await self.task_input_service.process_text_input(child_id, text_content)

            return result

        except Exception as e:
            logger.error(f"处理文本输入时发生错误: {e}")
            return {
                "success": False,
                "message": f"处理文本输入失败: {str(e)}",
                "input_type": "text"
            }

    async def process_image_input(self, image_data: Union[str, bytes], child_id: int,
                                use_multimodal: bool = True) -> Dict[str, Any]:
        """
        处理图像输入

        Args:
            image_data: 图像数据（base64字符串或字节数据）
            child_id: 学生ID
            use_multimodal: 是否使用多模态模型（默认True）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的图像输入，使用多模态: {use_multimodal}")

            if use_multimodal:
                # 使用多模态模型处理
                result = await self.multimodal_service.process_image_input(child_id, image_data)
            else:
                # 使用OCR处理
                result = await self.task_input_service.process_image_input(child_id, image_data)

            return result

        except Exception as e:
            logger.error(f"处理图像输入时发生错误: {e}")
            return {
                "success": False,
                "message": f"处理图像输入失败: {str(e)}",
                "input_type": "image"
            }

    async def _audio_to_text(self, audio_data: bytes) -> Optional[str]:
        """
        将音频数据转换为文字

        Args:
            audio_data: 音频数据

        Returns:
            Optional[str]: 识别的文字，失败返回None
        """
        try:
            logger.info("开始语音识别处理...")

            # 保存音频数据到临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_audio_path = temp_file.name

            try:
                # 使用ASR处理器进行语音识别
                if self.asr_processor:
                    # 检查ASR服务是否可用
                    if self.asr_processor.is_service_available():
                        logger.info("使用真实ASR服务进行识别")
                        recognition_result = await self.asr_processor.recognize_audio_file(temp_audio_path)
                    else:
                        logger.warning("ASR服务不可用，使用降级方案")
                        recognition_result = await self.asr_processor._fallback_recognition(temp_audio_path)

                    if recognition_result:
                        logger.info(f"语音识别成功: {recognition_result[:100]}...")
                        return recognition_result

                # 如果ASR完全失败，返回默认结果
                logger.warning("所有ASR方案都失败，使用默认结果")
                await asyncio.sleep(1)  # 模拟处理时间
                return "今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》"

            finally:
                # 清理临时文件
                if os.path.exists(temp_audio_path):
                    os.remove(temp_audio_path)

        except Exception as e:
            logger.error(f"语音转文字失败: {e}")
            return None




class PromptGenerator:
    """
    Prompt生成器

    负责生成每日任务的prompt
    """

    def __init__(self):
        """初始化Prompt生成器"""
        self.prompt_service = PromptGenerationService()
        logger.info("Prompt生成器初始化完成")

    async def generate_task_prompt(self, child_id: int, days_back: int = 7,
                                 include_yesterday_tasks: bool = True) -> Optional[Dict[str, Any]]:
        """
        生成任务计划表的prompt

        Args:
            child_id: 学生ID
            days_back: 回溯天数
            include_yesterday_tasks: 是否包含昨日任务

        Returns:
            Optional[Dict]: 生成的prompt数据，失败返回None
        """
        try:
            logger.info(f"开始为学生{child_id}生成任务prompt")

            # 构建请求
            request = TaskPromptRequest(
                child_id=child_id,
                days_back=days_back,
                include_yesterday_tasks=include_yesterday_tasks,
                template_type="task_prompt"
            )

            # 生成prompt
            result = self.prompt_service.generate_task_prompt(request)

            if result:
                logger.info(f"成功为学生{child_id}生成任务prompt")
                return result.model_dump()
            else:
                logger.error(f"为学生{child_id}生成任务prompt失败")
                return None

        except Exception as e:
            logger.error(f"生成任务prompt时发生错误: {e}")
            return None


class ScheduleGenerator:
    """
    计划表生成器

    负责调用豆包模型生成计划表
    """

    def __init__(self):
        """初始化计划表生成器"""
        self.doubao_service = get_doubao_service()
        logger.info("计划表生成器初始化完成")

    async def generate_schedule(self, prompt_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        生成计划表

        Args:
            prompt_data: prompt数据

        Returns:
            Optional[Dict]: 生成的计划表，失败返回None
        """
        try:
            logger.info("开始调用豆包模型生成计划表")

            # 提取最终prompt
            final_prompt = prompt_data.get("final_prompt", "")

            if not final_prompt:
                logger.error("未找到有效的prompt内容")
                return None

            # 调用豆包模型
            result = self.doubao_service.simple_chat(
                prompt=final_prompt,
                temperature=0.7,
                max_tokens=4000
            )

            if result.get("success", False):
                logger.info("豆包模型成功生成计划表")
                return {
                    "success": True,
                    "schedule_content": result.get("response_text", ""),
                    "model": result.get("model", ""),
                    "timestamp": result.get("timestamp", ""),
                    "prompt_data": prompt_data
                }
            else:
                logger.error(f"豆包模型调用失败: {result.get('error', '未知错误')}")
                return {
                    "success": False,
                    "error": result.get("error", "未知错误"),
                    "prompt_data": prompt_data
                }

        except Exception as e:
            logger.error(f"生成计划表时发生错误: {e}")
            return {
                "success": False,
                "error": str(e),
                "prompt_data": prompt_data
            }


class TTSOutputProcessor:
    """
    TTS输出处理器

    负责将计划表内容转换为语音输出
    """

    def __init__(self):
        """初始化TTS输出处理器"""
        self.tts_manager = None

        # TTS配置
        self.tts_config = {
            "app_id": "5311525929",
            "token": "DRNTjbbfC1QcfDrTndiSSBdTr23F0-23",
            "speaker": "zh_female_shuangkuaisisi_moon_bigtts"
        }

        logger.info("TTS输出处理器初始化完成")

    def initialize_tts(self) -> bool:
        """
        初始化TTS服务

        Returns:
            bool: 是否成功初始化
        """
        try:
            if TTS_AVAILABLE:
                self.tts_manager = SimpleTTSClient(
                    app_id=self.tts_config["app_id"],
                    token=self.tts_config["token"],
                    speaker=self.tts_config["speaker"]
                )
                logger.info("TTS服务初始化成功")
                return True
            else:
                logger.warning("TTS模块不可用，使用模拟TTS")
                self.tts_manager = "mock_tts"  # 模拟TTS管理器
                return True
        except Exception as e:
            logger.error(f"TTS服务初始化失败: {e}")
            return False

    async def text_to_speech(self, text: str, output_file: Optional[str] = None) -> Dict[str, Any]:
        """
        将文本转换为语音

        Args:
            text: 要转换的文本
            output_file: 输出文件路径（可选）

        Returns:
            Dict: 处理结果
        """
        try:
            logger.info("开始TTS语音合成")

            # 初始化TTS（如果未初始化）
            if not self.tts_manager:
                if not self.initialize_tts():
                    return {
                        "success": False,
                        "message": "TTS服务初始化失败"
                    }

            # 预处理文本
            processed_text = self._preprocess_text_for_tts(text)

            if TTS_AVAILABLE and self.tts_manager != "mock_tts":
                # 使用真实TTS
                if output_file:
                    # 保存为文件
                    success = self.tts_manager.text_to_speech_file(processed_text, output_file)
                    return {
                        "success": success,
                        "message": f"语音已保存到: {output_file}" if success else "语音保存失败",
                        "output_file": output_file if success else None
                    }
                else:
                    # 直接播放
                    success = self.tts_manager.text_to_speech_play(processed_text)
                    return {
                        "success": success,
                        "message": "语音播放完成" if success else "语音播放失败"
                    }
            else:
                # 使用模拟TTS
                logger.info(f"模拟TTS处理: {processed_text[:50]}...")
                await asyncio.sleep(1)  # 模拟处理时间

                if output_file:
                    # 模拟保存文件
                    return {
                        "success": True,
                        "message": f"模拟语音已保存到: {output_file}",
                        "output_file": output_file,
                        "note": "这是模拟TTS输出"
                    }
                else:
                    # 模拟播放
                    return {
                        "success": True,
                        "message": "模拟语音播放完成",
                        "note": "这是模拟TTS输出"
                    }

        except Exception as e:
            logger.error(f"TTS语音合成时发生错误: {e}")
            return {
                "success": False,
                "message": f"TTS语音合成失败: {str(e)}"
            }

    def _preprocess_text_for_tts(self, text: str) -> str:
        """
        预处理文本以优化TTS输出

        Args:
            text: 原始文本

        Returns:
            str: 处理后的文本
        """
        try:
            import re

            # 移除markdown格式
            # 移除代码块
            text = re.sub(r'```[\s\S]*?```', '', text)

            # 移除内联代码
            text = re.sub(r'`[^`]*`', '', text)

            # 移除链接
            text = re.sub(r'\[([^\]]*)\]\([^\)]*\)', r'\1', text)

            # 移除HTML标签
            text = re.sub(r'<[^>]*>', '', text)

            # 移除特殊符号，保留中文标点
            text = re.sub(r'[📅🕐✓💡🎯⚡📝🎤📷❌]', '', text)

            # 处理时间格式，使其更适合语音播报
            text = re.sub(r'(\d{1,2}):(\d{2})\s*-\s*(\d{1,2}):(\d{2})',
                         r'\1点\2分到\3点\4分', text)

            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text).strip()

            # 限制文本长度，避免TTS处理时间过长
            if len(text) > 1000:
                text = text[:1000] + "..."
                logger.info("文本过长，已截断")

            return text

        except Exception as e:
            logger.error(f"文本预处理失败: {e}")
            return text

    async def generate_tts_summary(self, schedule_content: str) -> str:
        """
        生成适合TTS播报的计划表摘要

        Args:
            schedule_content: 完整的计划表内容

        Returns:
            str: TTS摘要文本
        """
        try:
            import re

            # 提取关键信息
            summary_parts = []

            # 添加开场白
            summary_parts.append("您好，为您播报今日学习计划。")

            # 提取时间安排
            time_pattern = r'(\d{1,2}):(\d{2})\s*-\s*(\d{1,2}):(\d{2})\s*([^\n]+)'
            time_matches = re.findall(time_pattern, schedule_content)

            if time_matches:
                summary_parts.append("今日时间安排如下：")
                for match in time_matches[:4]:  # 最多播报4个时间段
                    start_hour, start_min, end_hour, end_min, task = match
                    task_clean = re.sub(r'[✓📝🎯]', '', task).strip()
                    summary_parts.append(
                        f"{start_hour}点{start_min}分到{end_hour}点{end_min}分，{task_clean}。"
                    )

            # 提取学习建议
            if "学习建议" in schedule_content or "💡" in schedule_content:
                summary_parts.append("学习建议：请按时完成作业，注意劳逸结合。")

            # 结束语
            summary_parts.append("祝您学习愉快！")

            return " ".join(summary_parts)

        except Exception as e:
            logger.error(f"生成TTS摘要失败: {e}")
            return "今日学习计划已生成，请查看详细内容。"


class SmartFriendWorkflow:
    """
    智能学习伙伴主工作流程

    整合所有组件，实现完整的工作流程
    """

    def __init__(self):
        """初始化主工作流程"""
        self.input_processor = MultimodalInputProcessor()
        self.prompt_generator = PromptGenerator()
        self.schedule_generator = ScheduleGenerator()
        self.tts_processor = TTSOutputProcessor()

        logger.info("智能学习伙伴主工作流程初始化完成")

    async def process_workflow(self, input_data: Dict[str, Any], child_id: int) -> Dict[str, Any]:
        """
        执行完整的工作流程

        Args:
            input_data: 输入数据，包含type和data字段
            child_id: 学生ID

        Returns:
            Dict: 工作流程执行结果
        """
        try:
            logger.info(f"开始执行学生{child_id}的完整工作流程")

            workflow_result = {
                "child_id": child_id,
                "start_time": datetime.now(timezone.utc).isoformat(),
                "steps": {},
                "success": False
            }

            # 步骤1: 多模态输入处理
            logger.info("步骤1: 处理多模态输入")
            input_result = await self._process_input(input_data, child_id)
            workflow_result["steps"]["input_processing"] = input_result

            if not input_result.get("success", False):
                workflow_result["error"] = "输入处理失败"
                return workflow_result

            # 步骤2: 生成prompt
            logger.info("步骤2: 生成任务prompt")
            prompt_result = await self.prompt_generator.generate_task_prompt(child_id)
            workflow_result["steps"]["prompt_generation"] = prompt_result

            if not prompt_result:
                workflow_result["error"] = "Prompt生成失败"
                return workflow_result

            # 步骤3: 生成计划表
            logger.info("步骤3: 生成计划表")
            schedule_result = await self.schedule_generator.generate_schedule(prompt_result)
            workflow_result["steps"]["schedule_generation"] = schedule_result

            if not schedule_result or not schedule_result.get("success", False):
                workflow_result["error"] = "计划表生成失败"
                return workflow_result

            # 步骤4: TTS输出（可选）
            if input_data.get("enable_tts", False):
                logger.info("步骤4: TTS语音输出")

                # 生成适合TTS的摘要
                tts_summary = await self.tts_processor.generate_tts_summary(
                    schedule_result["schedule_content"]
                )

                # 执行TTS
                tts_result = await self.tts_processor.text_to_speech(tts_summary)
                workflow_result["steps"]["tts_output"] = tts_result
                workflow_result["tts_summary"] = tts_summary

            workflow_result["success"] = True
            workflow_result["end_time"] = datetime.now(timezone.utc).isoformat()

            logger.info(f"学生{child_id}的工作流程执行完成")
            return workflow_result

        except Exception as e:
            logger.error(f"执行工作流程时发生错误: {e}")
            workflow_result["error"] = str(e)
            workflow_result["end_time"] = datetime.now(timezone.utc).isoformat()
            return workflow_result

    async def _process_input(self, input_data: Dict[str, Any], child_id: int) -> Dict[str, Any]:
        """
        处理输入数据

        Args:
            input_data: 输入数据
            child_id: 学生ID

        Returns:
            Dict: 处理结果
        """
        input_type = input_data.get("type", "").lower()
        data = input_data.get("data", "")

        if input_type == "voice":
            # 处理语音输入
            if isinstance(data, str):
                # 如果是base64编码的音频数据
                try:
                    audio_data = base64.b64decode(data)
                except Exception as e:
                    logger.error(f"解码音频数据失败: {e}")
                    return {"success": False, "message": "音频数据格式错误"}
            else:
                audio_data = data

            return await self.input_processor.process_voice_input(audio_data, child_id)

        elif input_type == "text":
            # 处理文本输入
            return await self.input_processor.process_text_input(data, child_id)

        elif input_type == "image":
            # 处理图像输入
            use_multimodal = input_data.get("use_multimodal", True)
            return await self.input_processor.process_image_input(data, child_id, use_multimodal)

        else:
            return {
                "success": False,
                "message": f"不支持的输入类型: {input_type}"
            }


# 便捷函数
async def process_voice_task(audio_data: bytes, child_id: int, enable_tts: bool = False) -> Dict[str, Any]:
    """
    处理语音任务输入的便捷函数

    Args:
        audio_data: 音频数据
        child_id: 学生ID
        enable_tts: 是否启用TTS输出

    Returns:
        Dict: 处理结果
    """
    workflow = SmartFriendWorkflow()
    input_data = {
        "type": "voice",
        "data": audio_data,
        "enable_tts": enable_tts
    }
    return await workflow.process_workflow(input_data, child_id)


async def process_text_task(text_content: str, child_id: int, enable_tts: bool = False) -> Dict[str, Any]:
    """
    处理文本任务输入的便捷函数

    Args:
        text_content: 文本内容
        child_id: 学生ID
        enable_tts: 是否启用TTS输出

    Returns:
        Dict: 处理结果
    """
    workflow = SmartFriendWorkflow()
    input_data = {
        "type": "text",
        "data": text_content,
        "enable_tts": enable_tts
    }
    return await workflow.process_workflow(input_data, child_id)


async def process_image_task(image_data: Union[str, bytes], child_id: int,
                           use_multimodal: bool = True, enable_tts: bool = False) -> Dict[str, Any]:
    """
    处理图像任务输入的便捷函数

    Args:
        image_data: 图像数据
        child_id: 学生ID
        use_multimodal: 是否使用多模态模型
        enable_tts: 是否启用TTS输出

    Returns:
        Dict: 处理结果
    """
    workflow = SmartFriendWorkflow()
    input_data = {
        "type": "image",
        "data": image_data,
        "use_multimodal": use_multimodal,
        "enable_tts": enable_tts
    }
    return await workflow.process_workflow(input_data, child_id)


async def main():
    """
    主函数 - 演示工作流程
    """
    logger.info("智能学习伙伴主工作流程启动")

    # 示例：处理文本输入
    test_child_id = 1
    test_text = "今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》，英语作业是单词表第5页"

    try:
        result = await process_text_task(test_text, test_child_id, enable_tts=False)

        print("=" * 60)
        print("工作流程执行结果:")
        print("=" * 60)
        print(f"学生ID: {result['child_id']}")
        print(f"执行状态: {'成功' if result['success'] else '失败'}")
        print(f"开始时间: {result['start_time']}")
        print(f"结束时间: {result.get('end_time', '未完成')}")

        if result['success']:
            # 显示各步骤结果
            steps = result.get('steps', {})

            # 输入处理结果
            input_result = steps.get('input_processing', {})
            print(f"\n📝 输入处理: {'✅ 成功' if input_result.get('success') else '❌ 失败'}")
            if input_result.get('success'):
                print(f"   解析任务数: {input_result.get('total_tasks', 0)}")
                print(f"   存储任务数: {input_result.get('stored_tasks', 0)}")

            # Prompt生成结果
            prompt_result = steps.get('prompt_generation')
            print(f"\n🎯 Prompt生成: {'✅ 成功' if prompt_result else '❌ 失败'}")

            # 计划表生成结果
            schedule_result = steps.get('schedule_generation', {})
            print(f"\n📅 计划表生成: {'✅ 成功' if schedule_result.get('success') else '❌ 失败'}")
            if schedule_result.get('success'):
                schedule_content = schedule_result.get('schedule_content', '')
                print(f"   计划表内容预览: {schedule_content[:200]}...")

            # TTS输出结果
            tts_result = steps.get('tts_output')
            if tts_result:
                print(f"\n🔊 TTS输出: {'✅ 成功' if tts_result.get('success') else '❌ 失败'}")
        else:
            print(f"\n❌ 错误信息: {result.get('error', '未知错误')}")

    except Exception as e:
        logger.error(f"主函数执行失败: {e}")
        print(f"❌ 主函数执行失败: {e}")


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())