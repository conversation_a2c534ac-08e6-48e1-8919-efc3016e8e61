2025-06-11 15:07:33,174 - __main__ - INFO - 启动 Smart Friend 系统
2025-06-11 15:07:33,175 - database.connection - ERROR - Database connection failed: Not an executable object: 'SELECT 1'
2025-06-11 15:07:33,175 - __main__ - ERROR - 数据库连接失败
2025-06-11 15:07:33,175 - __main__ - ERROR - 数据库设置失败，程序退出
2025-06-11 15:07:56,324 - __main__ - INFO - 启动 Smart Friend 系统
2025-06-11 15:07:56,397 - database.connection - INFO - Database tables created successfully
2025-06-11 15:07:56,397 - __main__ - INFO - 数据库初始化成功
2025-06-11 15:07:56,415 - __main__ - INFO - 创建孩子档案: 小明 (ID: 1)
2025-06-11 15:07:56,420 - __main__ - INFO - 创建兴趣: 数学
2025-06-11 15:07:56,423 - __main__ - INFO - 创建兴趣: 足球
2025-06-11 15:07:56,427 - __main__ - INFO - 创建兴趣: 绘画
2025-06-11 15:07:56,432 - __main__ - INFO - 创建技能: 逻辑思维
2025-06-11 15:07:56,435 - __main__ - INFO - 创建技能: 团队合作
2025-06-11 15:07:56,439 - __main__ - INFO - 创建技能: 创意表达
2025-06-11 15:07:56,439 - __main__ - INFO - 示例数据创建完成
2025-06-11 15:07:56,445 - __main__ - INFO - === 完整档案 ===
2025-06-11 15:07:56,445 - __main__ - INFO - 孩子: 小明, 年龄: 10
2025-06-11 15:07:56,445 - __main__ - INFO - 兴趣数量: 3
2025-06-11 15:07:56,446 - __main__ - INFO - 技能数量: 3
2025-06-11 15:07:56,448 - __main__ - INFO - === 档案摘要 ===
2025-06-11 15:07:56,448 - __main__ - INFO - 孩子信息: {'id': 1, 'name': '小明', 'age': 10, 'grade': '三年级'}
2025-06-11 15:07:56,448 - __main__ - INFO - 主要兴趣: [{'name': '数学', 'level': 8}, {'name': '足球', 'level': 7}, {'name': '绘画', 'level': 6}]
2025-06-11 15:07:56,448 - __main__ - INFO - 需要提升的技能: [{'name': '逻辑思维', 'current': 6, 'target': 8}, {'name': '团队合作', 'current': 5, 'target': 7}, {'name': '创意表达', 'current': 7, 'target': 8}]
2025-06-11 15:07:56,448 - __main__ - INFO - Smart Friend 系统运行完成
2025-06-20 11:37:10 - camera_manager - INFO - 服务器端摄像头管理器初始化完成
2025-06-20 11:37:10 - camera_manager - INFO - 收到图像捕获请求: session_id=string, save_path=string, filename=string, apply_transforms=True
2025-06-20 11:57:29 - camera_manager - INFO - 服务器端摄像头管理器初始化完成
2025-06-20 11:57:29 - camera_manager - INFO - 客户端 string 注册了 1 个摄像头
2025-06-20 12:19:31 - test_planning_logs - INFO - 测试规划模块日志记录功能
2025-06-20 12:19:31 - test_planning_logs - WARNING - 这是一条警告信息
2025-06-20 12:19:31 - test_planning_logs - ERROR - 这是一条错误信息
2025-06-20 12:20:17 - test_planning_service - INFO - 开始测试创建计划功能
2025-06-20 12:20:17 - test_planning_service - INFO - ✅ 测试创建计划成功，计划ID: 339dcbb1-1052-4c21-a876-0cfafed266e4
2025-06-20 12:20:17 - test_planning_service - INFO - 开始测试获取计划功能
2025-06-20 12:20:17 - test_planning_service - INFO - ✅ 测试获取计划成功，获取到 31 个计划
2025-06-20 12:20:17 - test_planning_service - INFO - 开始测试获取单个计划功能
2025-06-20 12:20:17 - test_planning_service - INFO - ✅ 测试获取单个计划成功
2025-06-20 12:20:17 - test_planning_service - INFO - 开始测试更新计划功能
2025-06-20 12:20:17 - test_planning_service - INFO - ✅ 测试更新计划成功
2025-06-20 12:20:17 - test_planning_service - INFO - 开始测试获取统计信息功能
2025-06-20 12:20:17 - test_planning_service - INFO - ✅ 测试获取统计信息成功，总计划数: 31
2025-06-20 12:20:17 - test_daily_learning_service - INFO - 开始测试添加学习记录功能
2025-06-20 12:20:17 - test_daily_learning_service - INFO - ✅ 测试添加学习记录成功
2025-06-20 12:20:17 - test_daily_learning_service - INFO - 开始测试获取学习记录功能
2025-06-20 12:20:17 - test_daily_learning_service - INFO - ✅ 测试获取学习记录成功，获取到 13 条记录
2025-06-20 12:20:17 - test_daily_learning_service - INFO - 开始测试获取学习统计信息功能
2025-06-20 12:20:17 - test_daily_learning_service - INFO - ✅ 测试获取学习统计信息成功，总记录数: 1
2025-06-20 12:26:41 - camera_manager - INFO - 服务器端摄像头管理器初始化完成
2025-06-20 12:26:41 - camera_manager - INFO - 客户端 string 注册了 1 个摄像头
2025-06-20 12:47:02 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 12:47:02 - test - INFO - 测试日志系统初始化
2025-06-20 12:47:02 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:47:02 - service.planning_service - INFO - 开始创建学习计划 - 学生ID: 888, 学科: 数学, 任务: 测试任务
2025-06-20 12:47:02 - service.planning_service - INFO - ✅ 成功创建学习计划 - 学生ID: 888, 计划ID: 06ae89c5-a146-49f6-b3ee-73dd14a8f6a5, 学科: 数学, 耗时: 11.31ms
2025-06-20 12:47:28 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 12:47:28 - main - INFO - 🚀 初始化 Smart Friend FastAPI 应用日志系统
2025-06-20 12:47:28 - main - INFO - 📋 Smart Friend FastAPI 应用创建完成
2025-06-20 12:47:28 - main - INFO - 📍 API 文档地址: http://localhost:8015/docs
2025-06-20 12:47:28 - main - INFO - 🔧 日志系统已初始化，开始记录规划模块操作
2025-06-20 12:48:01 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:49:05 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:49:05 - core.planning.endpoints.planning - INFO - 🎯 API请求: 创建学习计划 - 学生ID: 123, 学科: 数学, 任务: API测试任务
2025-06-20 12:49:05 - service.planning_service - INFO - 开始创建学习计划 - 学生ID: 123, 学科: 数学, 任务: API测试任务
2025-06-20 12:49:05 - service.planning_service - INFO - ✅ 成功创建学习计划 - 学生ID: 123, 计划ID: eb457d56-21d0-4c51-bf11-b9de2a1d1488, 学科: 数学, 耗时: 7.63ms
2025-06-20 12:49:05 - core.planning.endpoints.planning - INFO - ✅ API响应: 创建学习计划成功 - 学生ID: 123, 计划ID: eb457d56-21d0-4c51-bf11-b9de2a1d1488
2025-06-20 12:51:03 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:51:03 - core.planning.endpoints.planning - INFO - 📋 API请求: 获取学习计划 - 学生ID: 123, 学科: None, 状态: None, 限制: 5
2025-06-20 12:51:03 - service.planning_service - INFO - 开始获取学习计划 - 学生ID: 123, 学科: None, 状态: None, 限制: 5
2025-06-20 12:51:03 - service.planning_service - INFO - ✅ 成功获取学习计划 - 学生ID: 123, 返回计划数: 2, 耗时: 9.55ms
2025-06-20 12:51:03 - core.planning.endpoints.planning - INFO - ✅ API响应: 获取学习计划成功 - 学生ID: 123, 返回计划数: 2
2025-06-20 12:52:59 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 12:52:59 - main - INFO - 🚀 初始化 Smart Friend FastAPI 应用日志系统
2025-06-20 12:52:59 - main - INFO - 📋 Smart Friend FastAPI 应用创建完成
2025-06-20 12:52:59 - main - INFO - 📍 API 文档地址: http://localhost:8015/docs
2025-06-20 12:52:59 - main - INFO - 🔧 日志系统已初始化，开始记录规划模块操作
2025-06-20 12:53:26 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:53:26 - core.planning.endpoints.planning - INFO - 🎯 API请求: 创建学习计划 - 学生ID: 0, 学科: string, 任务: string
2025-06-20 12:53:26 - service.planning_service - INFO - 开始创建学习计划 - 学生ID: 0, 学科: string, 任务: string
2025-06-20 12:53:26 - service.planning_service - INFO - ✅ 成功创建学习计划 - 学生ID: 0, 计划ID: ed23cd70-0254-444c-b97a-57e6368f1f93, 学科: string, 耗时: 7.18ms
2025-06-20 12:53:26 - core.planning.endpoints.planning - INFO - ✅ API响应: 创建学习计划成功 - 学生ID: 0, 计划ID: ed23cd70-0254-444c-b97a-57e6368f1f93
2025-06-20 12:54:31 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:54:31 - core.planning.endpoints.planning - INFO - 🎯 API请求: 创建学习计划 - 学生ID: 0, 学科: 化学, 任务: 化学
2025-06-20 12:54:31 - service.planning_service - INFO - 开始创建学习计划 - 学生ID: 0, 学科: 化学, 任务: 化学
2025-06-20 12:54:31 - service.planning_service - INFO - ✅ 成功创建学习计划 - 学生ID: 0, 计划ID: ce120d8c-10f8-4e3f-85aa-ae65de5a7ae1, 学科: 化学, 耗时: 6.29ms
2025-06-20 12:54:31 - core.planning.endpoints.planning - INFO - ✅ API响应: 创建学习计划成功 - 学生ID: 0, 计划ID: ce120d8c-10f8-4e3f-85aa-ae65de5a7ae1
2025-06-20 12:55:09 - service.planning_service - INFO - PlanningService 初始化完成
2025-06-20 12:55:09 - core.planning.endpoints.planning - INFO - 📋 API请求: 获取学习计划 - 学生ID: 0, 学科: None, 状态: None, 限制: 100
2025-06-20 12:55:09 - service.planning_service - INFO - 开始获取学习计划 - 学生ID: 0, 学科: None, 状态: None, 限制: 100
2025-06-20 12:55:09 - service.planning_service - INFO - ✅ 成功获取学习计划 - 学生ID: 0, 返回计划数: 5, 耗时: 11.99ms
2025-06-20 12:55:09 - core.planning.endpoints.planning - INFO - ✅ API响应: 获取学习计划成功 - 学生ID: 0, 返回计划数: 5
2025-06-20 15:03:23 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 15:03:23 - main - INFO - 🚀 初始化 Smart Friend FastAPI 应用日志系统
2025-06-20 15:03:23 - main - INFO - 📋 Smart Friend FastAPI 应用创建完成
2025-06-20 15:03:23 - main - INFO - 📍 API 文档地址: http://localhost:8015/docs
2025-06-20 15:03:23 - main - INFO - 🔧 日志系统已初始化，开始记录规划模块操作
2025-06-20 15:08:24 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 15:08:24 - main - INFO - 🚀 初始化 Smart Friend FastAPI 应用日志系统
2025-06-20 15:08:24 - main - INFO - 📋 Smart Friend FastAPI 应用创建完成
2025-06-20 15:08:24 - main - INFO - 📍 API 文档地址: http://localhost:8015/docs
2025-06-20 15:08:24 - main - INFO - 🔧 日志系统已初始化，开始记录规划模块操作
2025-06-20 15:09:48 - root - INFO - 📝 日志系统初始化成功 - 文件: logs/app.log, 级别: INFO
2025-06-20 15:09:48 - main - INFO - 🚀 初始化 Smart Friend FastAPI 应用日志系统
2025-06-20 15:09:48 - main - INFO - 📋 Smart Friend FastAPI 应用创建完成
2025-06-20 15:09:48 - main - INFO - 📍 API 文档地址: http://localhost:8015/docs
2025-06-20 15:09:48 - main - INFO - 🔧 日志系统已初始化，开始记录规划模块操作
