<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能学习伙伴 - Smart Friend</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .input-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e9ecef;
        }

        .input-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
        }

        .input-section h2::before {
            content: "📝";
            margin-right: 10px;
            font-size: 1.2em;
        }

        .input-tabs {
            display: flex;
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: #6c757d;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tab-button:hover {
            background: #5a6268;
        }

        .tab-button.active {
            background: #007bff;
        }

        .tab-button .icon {
            margin-right: 8px;
            font-size: 1.2em;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: block;
            padding: 20px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload-label:hover {
            background: #e9ecef;
            border-color: #007bff;
        }

        .voice-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
        }

        .voice-button {
            padding: 15px 25px;
            border: none;
            border-radius: 50px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .record-button {
            background: #dc3545;
            color: white;
        }

        .record-button:hover {
            background: #c82333;
        }

        .record-button.recording {
            background: #28a745;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .stop-button {
            background: #6c757d;
            color: white;
        }

        .stop-button:hover {
            background: #5a6268;
        }

        .submit-button {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 30px auto 0;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        }

        .submit-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .status-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            display: none;
        }

        .status-section.show {
            display: block;
        }

        .status-section h2 {
            color: #856404;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .status-section h2::before {
            content: "⚡";
            margin-right: 10px;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #dee2e6;
            z-index: 1;
        }

        .progress-line {
            position: absolute;
            top: 20px;
            left: 0;
            height: 2px;
            background: #28a745;
            z-index: 2;
            transition: width 0.5s ease;
            width: 0%;
        }

        .step {
            background: white;
            border: 3px solid #dee2e6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 3;
            transition: all 0.3s ease;
        }

        .step.active {
            border-color: #007bff;
            background: #007bff;
            color: white;
        }

        .step.completed {
            border-color: #28a745;
            background: #28a745;
            color: white;
        }

        .step-label {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.9em;
            white-space: nowrap;
            color: #6c757d;
        }

        .result-section {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 15px;
            padding: 30px;
            display: none;
        }

        .result-section.show {
            display: block;
        }

        .result-section h2 {
            color: #0c5460;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .result-section h2::before {
            content: "📅";
            margin-right: 10px;
        }

        .schedule-content {
            background: white;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #bee5eb;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
            max-height: 400px;
            overflow-y: auto;
        }

        .audio-controls {
            margin-top: 20px;
            text-align: center;
        }

        .play-button {
            background: #17a2b8;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: #138496;
        }

        .error-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 15px;
            padding: 30px;
            display: none;
        }

        .error-section.show {
            display: block;
        }

        .error-section h2 {
            color: #721c24;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .error-section h2::before {
            content: "❌";
            margin-right: 10px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .child-selector {
            margin-bottom: 20px;
        }

        .child-selector select {
            padding: 10px 15px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            font-size: 1em;
            background: white;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .input-tabs {
                flex-direction: column;
            }
            
            .progress-steps {
                flex-wrap: wrap;
                gap: 20px;
            }
            
            .voice-controls {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能学习伙伴</h1>
            <p>多模态输入 → 智能分析 → 个性化计划表生成</p>
        </div>

        <div class="main-content">
            <!-- 学生选择 -->
            <div class="child-selector">
                <label for="childSelect">选择学生:</label>
                <select id="childSelect">
                    <option value="1">学生1 - 小明</option>
                    <option value="2">学生2 - 小红</option>
                    <option value="3">学生3 - 小刚</option>
                </select>
            </div>

            <!-- 输入区域 -->
            <div class="input-section">
                <h2>任务输入</h2>
                
                <div class="input-tabs">
                    <button class="tab-button active" data-tab="text">
                        <span class="icon">📝</span>
                        文本输入
                    </button>
                    <button class="tab-button" data-tab="voice">
                        <span class="icon">🎤</span>
                        语音输入
                    </button>
                    <button class="tab-button" data-tab="image">
                        <span class="icon">📷</span>
                        图像输入
                    </button>
                </div>

                <!-- 文本输入 -->
                <div class="tab-content active" id="text-tab">
                    <div class="form-group">
                        <label for="textInput">请输入今日学习任务:</label>
                        <textarea id="textInput" class="form-control" 
                                placeholder="例如：今天的数学作业是第三章练习题1到10题，语文作业是背诵古诗《静夜思》..."></textarea>
                    </div>
                </div>

                <!-- 语音输入 -->
                <div class="tab-content" id="voice-tab">
                    <div class="voice-controls">
                        <button id="recordButton" class="voice-button record-button">
                            <span>🎤</span>
                            开始录音
                        </button>
                        <button id="stopButton" class="voice-button stop-button" disabled>
                            <span>⏹️</span>
                            停止录音
                        </button>
                    </div>
                    <div id="voiceStatus" style="text-align: center; margin-top: 15px; color: #6c757d;"></div>
                    <div id="voiceResult" style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px; display: none;">
                        <strong>识别结果:</strong>
                        <div id="voiceText"></div>
                    </div>
                </div>

                <!-- 图像输入 -->
                <div class="tab-content" id="image-tab">
                    <div class="form-group">
                        <label>上传作业图片或截图:</label>
                        <div class="file-upload">
                            <input type="file" id="imageInput" accept="image/*">
                            <label for="imageInput" class="file-upload-label">
                                <div>📷 点击选择图片或拖拽图片到此处</div>
                                <div style="font-size: 0.9em; color: #6c757d; margin-top: 10px;">
                                    支持 JPG, PNG, GIF 格式
                                </div>
                            </label>
                        </div>
                    </div>
                    <div id="imagePreview" style="margin-top: 15px; display: none;">
                        <img id="previewImg" style="max-width: 100%; max-height: 300px; border-radius: 8px;">
                    </div>
                    <div class="form-group" style="margin-top: 20px;">
                        <label>
                            <input type="checkbox" id="useMultimodal" checked>
                            使用多模态AI模型识别（推荐）
                        </label>
                    </div>
                </div>

                <button id="submitButton" class="submit-button">
                    🚀 开始处理
                </button>
            </div>

            <!-- 处理状态 -->
            <div id="statusSection" class="status-section">
                <h2>处理进度</h2>
                <div class="progress-steps">
                    <div class="progress-line" id="progressLine"></div>
                    <div class="step" id="step1">1<div class="step-label">输入处理</div></div>
                    <div class="step" id="step2">2<div class="step-label">任务解析</div></div>
                    <div class="step" id="step3">3<div class="step-label">Prompt生成</div></div>
                    <div class="step" id="step4">4<div class="step-label">AI生成计划</div></div>
                    <div class="step" id="step5">5<div class="step-label">完成</div></div>
                </div>
                <div id="statusMessage" style="text-align: center; font-size: 1.1em; color: #495057;">
                    准备开始处理...
                </div>
            </div>

            <!-- 结果展示 -->
            <div id="resultSection" class="result-section">
                <h2>生成的学习计划表</h2>
                <div id="scheduleContent" class="schedule-content"></div>
                <div class="audio-controls">
                    <button id="playButton" class="play-button">
                        🔊 播放语音版计划表
                    </button>
                </div>
            </div>

            <!-- 错误信息 -->
            <div id="errorSection" class="error-section">
                <h2>处理失败</h2>
                <div id="errorMessage"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let mediaRecorder;
        let audioChunks = [];
        let currentStep = 0;
        let isRecording = false;

        // DOM元素
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        const submitButton = document.getElementById('submitButton');
        const statusSection = document.getElementById('statusSection');
        const resultSection = document.getElementById('resultSection');
        const errorSection = document.getElementById('errorSection');
        const recordButton = document.getElementById('recordButton');
        const stopButton = document.getElementById('stopButton');
        const voiceStatus = document.getElementById('voiceStatus');
        const voiceResult = document.getElementById('voiceResult');
        const voiceText = document.getElementById('voiceText');
        const imageInput = document.getElementById('imageInput');
        const imagePreview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabs();
            initializeVoiceRecording();
            initializeImageUpload();
            initializeSubmit();
        });

        // 标签页切换
        function initializeTabs() {
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.dataset.tab;
                    
                    // 更新按钮状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    button.classList.add('active');
                    
                    // 更新内容显示
                    tabContents.forEach(content => content.classList.remove('active'));
                    document.getElementById(tabId + '-tab').classList.add('active');
                });
            });
        }

        // 语音录制功能
        function initializeVoiceRecording() {
            recordButton.addEventListener('click', startRecording);
            stopButton.addEventListener('click', stopRecording);
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = event => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    processAudioBlob(audioBlob);
                };

                mediaRecorder.start();
                isRecording = true;
                
                recordButton.classList.add('recording');
                recordButton.textContent = '🎤 录音中...';
                recordButton.disabled = true;
                stopButton.disabled = false;
                voiceStatus.textContent = '正在录音，请说话...';
                
            } catch (error) {
                console.error('录音失败:', error);
                voiceStatus.textContent = '录音失败，请检查麦克风权限';
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                
                isRecording = false;
                recordButton.classList.remove('recording');
                recordButton.textContent = '🎤 开始录音';
                recordButton.disabled = false;
                stopButton.disabled = true;
                voiceStatus.textContent = '录音已停止，正在处理...';
            }
        }

        async function processAudioBlob(audioBlob) {
            try {
                voiceStatus.textContent = '正在处理音频...';

                // 将音频转换为base64
                const arrayBuffer = await audioBlob.arrayBuffer();
                const uint8Array = new Uint8Array(arrayBuffer);
                const base64Audio = btoa(String.fromCharCode.apply(null, uint8Array));

                // 存储音频数据供后续使用
                window.currentAudioData = base64Audio;

                // 显示识别结果（这里可以调用ASR预览，但为了简化流程，我们直接显示提示）
                voiceText.textContent = "音频已录制完成，点击'开始处理'进行完整的语音识别和任务处理";
                voiceResult.style.display = 'block';
                voiceStatus.textContent = '音频录制完成，准备处理';

            } catch (error) {
                console.error('处理音频失败:', error);
                voiceStatus.textContent = '音频处理失败: ' + error.message;
            }
        }

        // 图像上传功能
        function initializeImageUpload() {
            imageInput.addEventListener('change', handleImageUpload);
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        }

        // 提交处理
        function initializeSubmit() {
            submitButton.addEventListener('click', handleSubmit);
        }

        async function handleSubmit() {
            const childId = document.getElementById('childSelect').value;
            const activeTab = document.querySelector('.tab-button.active').dataset.tab;
            
            let inputData = {
                type: activeTab,
                enable_tts: false
            };

            // 根据输入类型收集数据
            if (activeTab === 'text') {
                const textContent = document.getElementById('textInput').value.trim();
                if (!textContent) {
                    alert('请输入文本内容');
                    return;
                }
                inputData.data = textContent;
            } else if (activeTab === 'voice') {
                if (!window.currentAudioData) {
                    alert('请先录制语音');
                    return;
                }
                inputData.data = window.currentAudioData; // 使用实际的音频数据
            } else if (activeTab === 'image') {
                const imageFile = imageInput.files[0];
                if (!imageFile) {
                    alert('请选择图片');
                    return;
                }
                
                // 转换为base64
                const base64 = await fileToBase64(imageFile);
                inputData.data = base64;
                inputData.use_multimodal = document.getElementById('useMultimodal').checked;
            }

            // 开始处理
            await processWorkflow(inputData, childId);
        }

        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result.split(',')[1]);
                reader.onerror = error => reject(error);
            });
        }

        async function processWorkflow(inputData, childId) {
            // 显示状态区域
            hideAllSections();
            statusSection.classList.add('show');
            submitButton.disabled = true;

            try {
                // 确定API端点
                let apiEndpoint;
                let requestData;

                if (inputData.type === 'text') {
                    apiEndpoint = '/api/process/text';
                    requestData = {
                        child_id: parseInt(childId),
                        text_content: inputData.data,
                        enable_tts: inputData.enable_tts || false
                    };
                } else if (inputData.type === 'voice') {
                    apiEndpoint = '/api/process/voice';
                    requestData = {
                        child_id: parseInt(childId),
                        audio_data: inputData.data,
                        enable_tts: inputData.enable_tts || false
                    };
                } else if (inputData.type === 'image') {
                    apiEndpoint = '/api/process/image';
                    requestData = {
                        child_id: parseInt(childId),
                        image_data: inputData.data,
                        use_multimodal: inputData.use_multimodal || true,
                        enable_tts: inputData.enable_tts || false
                    };
                } else {
                    throw new Error('不支持的输入类型');
                }

                // 显示处理步骤
                updateProgress(1, '正在处理输入数据...');
                await new Promise(resolve => setTimeout(resolve, 500));

                updateProgress(2, '正在解析任务内容...');
                await new Promise(resolve => setTimeout(resolve, 500));

                updateProgress(3, '正在生成AI Prompt...');
                await new Promise(resolve => setTimeout(resolve, 500));

                updateProgress(4, '正在调用AI模型生成计划表...');

                // 调用真实API
                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                updateProgress(5, '处理完成！');
                await new Promise(resolve => setTimeout(resolve, 500));

                // 显示结果
                if (result.success) {
                    showRealResult(result);
                } else {
                    throw new Error(result.error || '处理失败');
                }

            } catch (error) {
                console.error('工作流程处理错误:', error);
                showError(error.message);
            } finally {
                submitButton.disabled = false;
            }
        }

        async function simulateStep(step, message) {
            currentStep = step;
            updateProgress(step, message);
            
            // 模拟处理时间
            await new Promise(resolve => setTimeout(resolve, 1500));
        }

        function updateProgress(step, message) {
            // 更新进度条
            const progressLine = document.getElementById('progressLine');
            const progressPercent = ((step - 1) / 4) * 100;
            progressLine.style.width = progressPercent + '%';

            // 更新步骤状态
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById('step' + i);
                if (i < step) {
                    stepElement.className = 'step completed';
                } else if (i === step) {
                    stepElement.className = 'step active';
                } else {
                    stepElement.className = 'step';
                }
            }

            // 更新状态消息
            document.getElementById('statusMessage').innerHTML = 
                `<div class="loading"></div>${message}`;
        }

        function showResult() {
            hideAllSections();
            resultSection.classList.add('show');

            // 模拟生成的计划表内容
            const mockSchedule = `📅 今日学习计划表 - ${new Date().toLocaleDateString()}

🕐 时间安排：
18:00 - 18:45  数学作业
  ✓ 完成第三章练习题1-10题
  ✓ 重点复习分数运算
  ✓ 准备：数学课本、练习册、计算器

19:00 - 19:30  语文作业
  ✓ 背诵古诗《静夜思》
  ✓ 理解诗意，体会情感
  ✓ 准备：语文课本

19:45 - 20:15  英语作业
  ✓ 复习单词表第5页
  ✓ 完成课后练习
  ✓ 准备：英语课本、单词卡片

20:30 - 21:00  复习总结
  ✓ 整理今日学习要点
  ✓ 预习明日课程
  ✓ 准备明日用品

💡 学习建议：
- 数学题目要仔细审题，注意计算步骤
- 背诵古诗时可以结合理解记忆
- 英语单词要多读多写，加深印象
- 保持良好的学习节奏，注意休息

🎯 完成目标：
- 按时完成所有作业
- 掌握重点知识
- 培养良好学习习惯`;

            document.getElementById('scheduleContent').textContent = mockSchedule;
        }

        function showRealResult(result) {
            hideAllSections();
            resultSection.classList.add('show');

            // 显示真实的API结果
            let scheduleContent = '';

            if (result.steps && result.steps.schedule_generation && result.steps.schedule_generation.schedule_content) {
                scheduleContent = result.steps.schedule_generation.schedule_content;
            } else {
                scheduleContent = `📅 学习计划生成成功 - ${new Date().toLocaleDateString()}

✅ 处理结果：
- 学生ID: ${result.child_id}
- 处理时间: ${result.start_time} - ${result.end_time}
- 状态: ${result.success ? '成功' : '失败'}

📝 处理步骤：
${result.steps ? Object.keys(result.steps).map(step =>
    `- ${step}: ${result.steps[step].success ? '✅ 成功' : '❌ 失败'}`
).join('\n') : '无详细步骤信息'}

💡 说明：
系统已成功处理您的学习任务输入，并生成了个性化的学习计划。
请查看上述处理结果，如有问题请重新尝试。`;
            }

            document.getElementById('scheduleContent').textContent = scheduleContent;

            // 如果有TTS结果，更新播放按钮
            if (result.steps && result.steps.tts_output) {
                const playButton = document.getElementById('playButton');
                if (result.steps.tts_output.success) {
                    playButton.textContent = '🔊 播放语音版计划表 (已生成)';
                    playButton.onclick = function() {
                        alert('TTS音频已生成: ' + result.steps.tts_output.message);
                    };
                } else {
                    playButton.textContent = '🔊 语音生成失败';
                    playButton.disabled = true;
                }
            }
        }

        function showError(message) {
            hideAllSections();
            errorSection.classList.add('show');
            document.getElementById('errorMessage').textContent = message;
        }

        function hideAllSections() {
            statusSection.classList.remove('show');
            resultSection.classList.remove('show');
            errorSection.classList.remove('show');
        }

        // 播放语音功能（模拟）
        document.getElementById('playButton').addEventListener('click', function() {
            alert('语音播放功能需要连接TTS服务');
        });
    </script>
</body>
</html>
