#!/usr/bin/env python3
"""
Jina Embedding v4 Docker Setup Script

This script helps set up and manage Jina Embedding v4 Docker container
for the Enhanced OpenManus Framework.
"""

import subprocess
import time
import requests
import json
import sys
import os
from pathlib import Path

# Configuration
JINA_DOCKER_HOST = os.getenv("JINA_DOCKER_HOST", "localhost")
JINA_DOCKER_PORT = int(os.getenv("JINA_DOCKER_PORT", "8080"))
JINA_CONTAINER_NAME = "jina-embeddings-v4"

# Available Jina Docker images
JINA_IMAGES = {
    "v3": "jinaai/jina-embeddings-v3:latest",
    "v2": "jinaai/jina-embeddings-v2:latest", 
    "v1": "jinaai/jina-embeddings-v1:latest"
}

def run_command(cmd, timeout=30):
    """Run shell command with timeout"""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=True, 
            text=True, timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def check_docker():
    """Check if Docker is installed and running"""
    print("🔍 Checking Docker installation...")
    
    success, stdout, stderr = run_command("docker --version")
    if not success:
        print("❌ Docker is not installed or not in PATH")
        print("Please install Docker: https://docs.docker.com/get-docker/")
        return False
    
    print(f"✅ Docker found: {stdout.strip()}")
    
    # Check if Docker daemon is running
    success, stdout, stderr = run_command("docker info")
    if not success:
        print("❌ Docker daemon is not running")
        print("Please start Docker daemon")
        return False
    
    print("✅ Docker daemon is running")
    return True

def pull_jina_image(version="v3"):
    """Pull Jina embedding Docker image"""
    if version not in JINA_IMAGES:
        print(f"❌ Unknown Jina version: {version}")
        print(f"Available versions: {list(JINA_IMAGES.keys())}")
        return False
    
    image = JINA_IMAGES[version]
    print(f"📥 Pulling Jina image: {image}")
    
    success, stdout, stderr = run_command(f"docker pull {image}", timeout=300)
    if success:
        print(f"✅ Successfully pulled {image}")
        return True
    else:
        print(f"❌ Failed to pull image: {stderr}")
        return False

def stop_existing_container():
    """Stop and remove existing Jina container"""
    print(f"🛑 Stopping existing container: {JINA_CONTAINER_NAME}")
    
    # Stop container
    run_command(f"docker stop {JINA_CONTAINER_NAME}")
    
    # Remove container
    run_command(f"docker rm {JINA_CONTAINER_NAME}")
    
    print("✅ Cleaned up existing container")

def start_jina_container(version="v3", gpu=False):
    """Start Jina embedding Docker container"""
    if version not in JINA_IMAGES:
        print(f"❌ Unknown Jina version: {version}")
        return False
    
    image = JINA_IMAGES[version]
    
    # Stop existing container first
    stop_existing_container()
    
    # Build Docker command
    cmd_parts = [
        "docker run -d",
        f"--name {JINA_CONTAINER_NAME}",
        f"-p {JINA_DOCKER_PORT}:8080",
        "--restart unless-stopped"
    ]
    
    # Add GPU support if requested
    if gpu:
        cmd_parts.append("--gpus all")
    
    # Add memory limits for stability
    cmd_parts.extend([
        "--memory=4g",
        "--memory-swap=4g"
    ])
    
    cmd_parts.append(image)
    
    cmd = " ".join(cmd_parts)
    print(f"🚀 Starting Jina container: {cmd}")
    
    success, stdout, stderr = run_command(cmd, timeout=60)
    if success:
        container_id = stdout.strip()
        print(f"✅ Container started with ID: {container_id}")
        return True
    else:
        print(f"❌ Failed to start container: {stderr}")
        return False

def wait_for_container_ready(max_wait=60):
    """Wait for Jina container to be ready"""
    print("⏳ Waiting for Jina container to be ready...")
    
    health_url = f"http://{JINA_DOCKER_HOST}:{JINA_DOCKER_PORT}/health"
    
    for i in range(max_wait):
        try:
            response = requests.get(health_url, timeout=2)
            if response.status_code == 200:
                print("✅ Jina container is ready!")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 10 == 0:
            print(f"   Still waiting... ({i}/{max_wait}s)")
    
    print("❌ Container did not become ready in time")
    return False

def test_embedding():
    """Test embedding generation"""
    print("🧪 Testing embedding generation...")
    
    test_url = f"http://{JINA_DOCKER_HOST}:{JINA_DOCKER_PORT}/encode"
    
    payload = {
        "data": [
            {"text": "Hello, world!"},
            {"text": "This is a test sentence."}
        ]
    }
    
    try:
        response = requests.post(
            test_url,
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if "data" in data and len(data["data"]) == 2:
                embedding_dim = len(data["data"][0].get("embedding", []))
                print(f"✅ Embedding test successful!")
                print(f"   Generated 2 embeddings with {embedding_dim} dimensions")
                return True
            else:
                print(f"❌ Unexpected response format: {data}")
                return False
        else:
            print(f"❌ HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Embedding test failed: {e}")
        return False

def show_container_status():
    """Show current container status"""
    print("📊 Container Status:")
    
    success, stdout, stderr = run_command(f"docker ps --filter name={JINA_CONTAINER_NAME}")
    if success and JINA_CONTAINER_NAME in stdout:
        print("✅ Container is running")
        print(stdout)
    else:
        print("❌ Container is not running")
    
    # Show logs
    success, stdout, stderr = run_command(f"docker logs --tail 10 {JINA_CONTAINER_NAME}")
    if success:
        print("\n📝 Recent logs:")
        print(stdout)

def main():
    """Main setup function"""
    print("🚀 Jina Embedding v4 Docker Setup")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
    else:
        command = "setup"
    
    if command == "setup":
        # Full setup process
        if not check_docker():
            sys.exit(1)
        
        version = input("Enter Jina version (v1/v2/v3) [v3]: ").strip() or "v3"
        gpu_input = input("Use GPU acceleration? (y/n) [n]: ").strip().lower()
        use_gpu = gpu_input in ['y', 'yes']
        
        if not pull_jina_image(version):
            sys.exit(1)
        
        if not start_jina_container(version, gpu=use_gpu):
            sys.exit(1)
        
        if not wait_for_container_ready():
            print("⚠️ Container may not be fully ready, but continuing...")
        
        if test_embedding():
            print("\n🎉 Jina Embedding Docker setup completed successfully!")
            print(f"   Container: {JINA_CONTAINER_NAME}")
            print(f"   Endpoint: http://{JINA_DOCKER_HOST}:{JINA_DOCKER_PORT}")
            print(f"   Health: http://{JINA_DOCKER_HOST}:{JINA_DOCKER_PORT}/health")
        else:
            print("\n⚠️ Setup completed but embedding test failed")
            print("Check container logs for issues")
    
    elif command == "start":
        version = sys.argv[2] if len(sys.argv) > 2 else "v3"
        start_jina_container(version)
        wait_for_container_ready()
        test_embedding()
    
    elif command == "stop":
        stop_existing_container()
    
    elif command == "status":
        show_container_status()
    
    elif command == "test":
        test_embedding()
    
    elif command == "logs":
        success, stdout, stderr = run_command(f"docker logs {JINA_CONTAINER_NAME}")
        if success:
            print(stdout)
        else:
            print(f"❌ Could not get logs: {stderr}")
    
    else:
        print("Usage:")
        print("  python setup_jina_docker.py setup   - Full setup")
        print("  python setup_jina_docker.py start   - Start container")
        print("  python setup_jina_docker.py stop    - Stop container")
        print("  python setup_jina_docker.py status  - Show status")
        print("  python setup_jina_docker.py test    - Test embedding")
        print("  python setup_jina_docker.py logs    - Show logs")

if __name__ == "__main__":
    main()
