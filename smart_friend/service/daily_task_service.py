# 每日任务服务
import logging
import re
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from sqlalchemy.orm import Session

from core.daily_tasks.models.daily_task_models import DailyTask, TaskItem
from core.user_management.database.connection import get_db_session_context

logger = logging.getLogger(__name__)


class DailyTaskService:
    """每日任务服务类"""

    def __init__(self):
        pass  # 使用上下文管理器，不需要持久化连接
    
    def create_task(self, child_id: int, task_name: str, description: str = "", **kwargs) -> Optional[Dict[str, Any]]:
        """创建每日任务"""
        try:
            with get_db_session_context() as session:
                task = DailyTask.create_task(
                    session=session,
                    child_id=child_id,
                    task_name=task_name,
                    description=description,
                    **kwargs
                )

                if task:
                    return task.to_dict()
                return None

        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return None

    def parse_sub_tasks_from_description(self, description: str) -> List[str]:
        """从描述中解析子任务"""
        if not description:
            return []

        # 常见的分隔符模式
        separators = [
            r'[，,、]',  # 中文逗号、英文逗号、顿号
            r'[；;]',    # 中文分号、英文分号
            r'[。]',     # 中文句号
            r'\s+',      # 空格
        ]

        # 尝试用不同分隔符拆分
        sub_tasks = []
        for separator in separators:
            parts = re.split(separator, description.strip())
            if len(parts) > 1:
                # 过滤空字符串和过短的内容
                sub_tasks = [part.strip() for part in parts if part.strip() and len(part.strip()) > 2]
                if len(sub_tasks) > 1:
                    break

        # 如果没有找到合适的分隔符，返回原描述作为单个任务
        if not sub_tasks:
            sub_tasks = [description.strip()]

        return sub_tasks

    def calculate_sub_task_time_slots(self, main_time_slot: str, sub_task_count: int) -> List[Dict[str, str]]:
        """计算子任务的时间段"""
        if not main_time_slot or sub_task_count <= 0:
            return []

        try:
            # 解析主任务时间段 "18:00 - 18:45"
            time_parts = main_time_slot.replace(' ', '').split('-')
            if len(time_parts) != 2:
                return []

            start_time_str, end_time_str = time_parts
            start_hour, start_minute = map(int, start_time_str.split(':'))
            end_hour, end_minute = map(int, end_time_str.split(':'))

            # 计算总时长（分钟）
            start_total_minutes = start_hour * 60 + start_minute
            end_total_minutes = end_hour * 60 + end_minute
            total_duration = end_total_minutes - start_total_minutes

            if total_duration <= 0:
                return []

            # 为每个子任务分配时间，预留5分钟休息时间
            rest_time = 5 if sub_task_count > 1 else 0
            available_time = total_duration - (sub_task_count - 1) * rest_time
            sub_task_duration = max(10, available_time // sub_task_count)  # 最少10分钟

            time_slots = []
            current_start = start_total_minutes

            for i in range(sub_task_count):
                # 计算当前子任务的开始和结束时间
                current_end = current_start + sub_task_duration

                # 确保不超过主任务结束时间
                if current_end > end_total_minutes:
                    current_end = end_total_minutes

                # 转换回时间格式
                start_h, start_m = divmod(current_start, 60)
                end_h, end_m = divmod(current_end, 60)

                time_slot = f"{start_h:02d}:{start_m:02d}-{end_h:02d}:{end_m:02d}"

                time_slots.append({
                    'time_slot': time_slot,
                    'start_time': f"{start_h:02d}:{start_m:02d}",
                    'end_time': f"{end_h:02d}:{end_m:02d}",
                    'duration_minutes': current_end - current_start,
                    'order_index': i + 1
                })

                # 下一个子任务开始时间（包含休息时间）
                current_start = current_end + rest_time

                # 如果已经到达或超过主任务结束时间，停止
                if current_start >= end_total_minutes:
                    break

            return time_slots

        except (ValueError, IndexError) as e:
            logger.error(f"解析时间段失败: {e}")
            return []

    def calculate_base_points(self, duration_minutes: int) -> int:
        """根据任务用时计算基础积分"""
        if duration_minutes <= 10:
            return 5  # 短任务
        elif duration_minutes <= 20:
            return 10  # 中等任务
        elif duration_minutes <= 30:
            return 15  # 长任务
        else:
            return 20  # 超长任务

    def calculate_total_points(self, sub_tasks_data: List[Dict]) -> Dict[str, int]:
        """计算任务总积分"""
        total_points = sum(task.get('points_earned', 0) for task in sub_tasks_data)
        total_bonus = sum(task.get('bonus_points', 0) for task in sub_tasks_data)

        return {
            'total_points': total_points + total_bonus,
            'bonus_points': total_bonus
        }

    def create_task_with_sub_tasks(self, child_id: int, task_name: str, description: str = "", **kwargs) -> Optional[Dict[str, Any]]:
        """创建带子任务的每日任务"""
        try:
            with get_db_session_context() as session:
                # 创建主任务
                task = DailyTask.create_task(
                    session=session,
                    child_id=child_id,
                    task_name=task_name,
                    description=description,
                    **kwargs
                )

                if not task:
                    return None

                # 解析子任务
                sub_tasks = self.parse_sub_tasks_from_description(description)

                if len(sub_tasks) > 1:
                    # 计算子任务时间段
                    main_time_slot = kwargs.get('time_slot', '')
                    time_slots = self.calculate_sub_task_time_slots(main_time_slot, len(sub_tasks))

                    # 收集子任务数据用于计算总积分
                    sub_tasks_data = []

                    # 创建子任务
                    for i, sub_task_content in enumerate(sub_tasks):
                        time_info = time_slots[i] if i < len(time_slots) else {}
                        duration = time_info.get('duration_minutes', 15)
                        base_points = self.calculate_base_points(duration)

                        # 创建子任务
                        sub_task_data = {
                            'task_content': sub_task_content,
                            'task_source': 'auto_parsed',
                            'time_slot': time_info.get('time_slot'),
                            'order_index': time_info.get('order_index', i + 1),
                            'estimated_minutes': duration,
                            'points_earned': base_points,
                            'bonus_points': 0,  # 初始奖励积分为0
                            'points_reason': f"完成子任务基础积分({duration}分钟)"
                        }

                        sub_tasks_data.append(sub_task_data)

                        # 如果有具体的开始和结束时间，转换为datetime
                        if 'start_time' in time_info and 'end_time' in time_info:
                            task_date = task.task_date or datetime.now()
                            base_date = task_date.date()

                            start_time_str = time_info['start_time']
                            end_time_str = time_info['end_time']

                            start_hour, start_minute = map(int, start_time_str.split(':'))
                            end_hour, end_minute = map(int, end_time_str.split(':'))

                            sub_task_data['start_time'] = datetime.combine(base_date, datetime.min.time().replace(hour=start_hour, minute=start_minute))
                            sub_task_data['end_time'] = datetime.combine(base_date, datetime.min.time().replace(hour=end_hour, minute=end_minute))

                        TaskItem.create_item(session, task.id, **sub_task_data)

                    # 计算并更新任务总积分
                    points_info = self.calculate_total_points(sub_tasks_data)
                    task.total_points = points_info['total_points']
                    task.bonus_points = points_info['bonus_points']
                    task.points_reason = f"完成{len(sub_tasks_data)}个子任务，总积分{points_info['total_points']}分"
                    session.commit()

                # 返回完整的任务信息（包含子任务）
                task_dict = task.to_dict()
                task_dict['task_items'] = [item.to_dict() for item in task.task_items]

                return task_dict

        except Exception as e:
            logger.error(f"创建带子任务的任务失败: {e}")
            return None

    def get_task_by_id(self, task_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        try:
            with get_db_session_context() as session:
                task = DailyTask.get_by_id(session, task_id)
                return task.to_dict() if task else None
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None

    def get_tasks_by_child_and_date(self, child_id: int, task_date: date) -> List[Dict[str, Any]]:
        """获取指定学生指定日期的任务"""
        try:
            with get_db_session_context() as session:
                tasks = DailyTask.get_by_child_and_date(session, child_id, task_date)
                return [task.to_dict() for task in tasks]
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def get_tasks_by_date_range(self, child_id: int, start_date: date, end_date: date,
                               subject: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取指定日期范围的任务"""
        try:
            with get_db_session_context() as session:
                query = session.query(DailyTask).filter(
                    DailyTask.child_id == child_id,
                    DailyTask.task_date >= datetime.combine(start_date, datetime.min.time()),
                    DailyTask.task_date <= datetime.combine(end_date, datetime.max.time()),
                    DailyTask.is_active == True
                )

                if subject:
                    query = query.filter(DailyTask.subject == subject)
                if status:
                    query = query.filter(DailyTask.status == status)

                tasks = query.order_by(DailyTask.task_date.desc(), DailyTask.time_slot).all()
                return [task.to_dict() for task in tasks]
        except Exception as e:
            logger.error(f"获取任务列表失败: {e}")
            return []
    
    def get_yesterday_tasks(self, child_id: int) -> List[Dict[str, Any]]:
        """获取昨日任务情况"""
        try:
            yesterday = date.today() - timedelta(days=1)
            return self.get_tasks_by_child_and_date(child_id, yesterday)
        except Exception as e:
            logger.error(f"获取昨日任务失败: {e}")
            return []

    def get_today_tasks(self, child_id: int) -> List[Dict[str, Any]]:
        """获取今日任务"""
        try:
            today = date.today()
            return self.get_tasks_by_child_and_date(child_id, today)
        except Exception as e:
            logger.error(f"获取今日任务失败: {e}")
            return []
    
    def create_sample_tasks(self, child_id: int) -> bool:
        """为指定儿童创建示例任务数据"""
        try:
            today = date.today()
            yesterday = today - timedelta(days=1)
            
            # 创建昨日任务
            yesterday_tasks = [
                {
                    "task_name": "数学作业",
                    "subject": "数学",
                    "time_slot": "18:00 - 18:45",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "练习册第8页，口算题卡15题",
                    "customization": "针对应用题理解困难，采用图解方式",
                    "difficulty": "应用题理解",
                    "solution": "先画图理解题意，再列式计算",
                    "confidence_index": 4,
                    "status": "completed",
                    "completion_percentage": 85.0,
                    "difficulty_rating": 3,
                    "satisfaction_rating": 4,
                    "notes": "应用题有进步，但计算速度还需提高"
                },
                {
                    "task_name": "语文作业",
                    "subject": "语文",
                    "time_slot": "19:00 - 19:30",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "背诵古诗《静夜思》，练字20个",
                    "customization": "结合听觉学习特点，先听朗读再背诵",
                    "difficulty": "古诗理解和记忆",
                    "solution": "通过音频朗读加深理解，分段背诵",
                    "confidence_index": 3,
                    "status": "completed",
                    "completion_percentage": 70.0,
                    "difficulty_rating": 4,
                    "satisfaction_rating": 3,
                    "notes": "背诵有困难，需要多次重复"
                },
                {
                    "task_name": "英语作业",
                    "subject": "英语",
                    "time_slot": "20:00 - 20:30",
                    "task_date": datetime.combine(yesterday, datetime.min.time()),
                    "description": "听读Unit 2单词，完成练习册第5页",
                    "customization": "利用听觉优势，多听多读",
                    "difficulty": "单词发音",
                    "solution": "跟读音频，家长纠正发音",
                    "confidence_index": 4,
                    "status": "completed",
                    "completion_percentage": 90.0,
                    "difficulty_rating": 2,
                    "satisfaction_rating": 5,
                    "notes": "英语学习兴趣很高，发音进步明显"
                }
            ]
            
            # 创建今日任务（使用新的子任务结构）
            today_tasks = [
                {
                    "task_name": "数学作业",
                    "subject": "数学",
                    "time_slot": "18:00 - 18:45",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "练习册第9页，口算题卡20题，复习乘法表",
                    "customization": "重点练习乘法运算，使用口诀辅助记忆",
                    "difficulty": "乘法表记忆",
                    "solution": "通过儿歌和游戏方式记忆乘法表",
                    "confidence_index": 3,
                    "status": "pending"
                },
                {
                    "task_name": "语文作业",
                    "subject": "语文",
                    "time_slot": "19:00 - 19:40",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "阅读《小红帽》故事，完成阅读理解题，练字30个",
                    "customization": "先听故事音频，再阅读文字，提高理解能力",
                    "difficulty": "阅读理解",
                    "solution": "分段阅读，每段提问帮助理解",
                    "confidence_index": 3,
                    "status": "pending"
                },
                {
                    "task_name": "英语作业",
                    "subject": "英语",
                    "time_slot": "20:00 - 20:30",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "学习Unit 3新单词，听读课文，完成练习册第6页",
                    "customization": "重点练习听力，多次跟读提高发音",
                    "difficulty": "新单词记忆",
                    "solution": "制作单词卡片，通过游戏方式记忆",
                    "confidence_index": 4,
                    "status": "pending"
                }
            ]
            
            # 创建昨日任务和今日任务
            # 创建昨日任务（使用传统方法，因为已经完成）
            with get_db_session_context() as session:
                for task_data in yesterday_tasks:
                    task = DailyTask.create_task(session, child_id, **task_data)
                    if not task:
                        logger.error(f"创建昨日任务失败: {task_data['task_name']}")
                        return False

            # 创建今日任务（使用新的子任务拆分方法）
            for task_data in today_tasks:
                task_name = task_data.pop('task_name')
                description = task_data.pop('description', '')
                task = self.create_task_with_sub_tasks(child_id, task_name, description, **task_data)
                if not task:
                    logger.error(f"创建今日任务失败: {task_name}")
                    return False

            logger.info(f"成功为儿童{child_id}创建示例任务数据")
            return True
            
        except Exception as e:
            logger.error(f"创建示例任务数据失败: {e}")
            return False

    def create_today_sample_tasks(self, child_id: int) -> bool:
        """为指定儿童创建今日示例任务数据"""
        try:
            today = date.today()

            # 创建今日任务
            today_tasks = [
                {
                    "task_name": "数学作业",
                    "subject": "数学",
                    "time_slot": "18:00 - 18:45",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "练习册第9页，口算题卡20题，复习乘法表",
                    "customization": "重点练习乘法运算，使用口诀辅助记忆",
                    "difficulty": "乘法表记忆",
                    "solution": "通过儿歌和游戏方式记忆乘法表",
                    "confidence_index": 3,
                    "status": "pending"
                },
                {
                    "task_name": "语文作业",
                    "subject": "语文",
                    "time_slot": "19:00 - 19:40",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "阅读《小红帽》故事，完成阅读理解题，练字30个",
                    "customization": "先听故事音频，再阅读文字，提高理解能力",
                    "difficulty": "阅读理解",
                    "solution": "分段阅读，每段提问帮助理解",
                    "confidence_index": 3,
                    "status": "pending"
                },
                {
                    "task_name": "英语作业",
                    "subject": "英语",
                    "time_slot": "20:00 - 20:30",
                    "task_date": datetime.combine(today, datetime.min.time()),
                    "description": "学习Unit 3新单词，听读课文，完成练习册第6页",
                    "customization": "重点练习听力，多次跟读提高发音",
                    "difficulty": "新单词记忆",
                    "solution": "制作单词卡片，通过游戏方式记忆",
                    "confidence_index": 4,
                    "status": "pending"
                }
            ]

            # 创建今日任务（使用新的子任务拆分方法）
            for task_data in today_tasks:
                task_name = task_data.pop('task_name')
                description = task_data.pop('description', '')
                task = self.create_task_with_sub_tasks(child_id, task_name, description, **task_data)
                if not task:
                    logger.error(f"创建今日任务失败: {task_name}")
                    return False

            logger.info(f"成功为儿童{child_id}创建今日示例任务数据")
            return True

        except Exception as e:
            logger.error(f"创建今日示例任务数据失败: {e}")
            return False
