# -*- coding: utf-8 -*-
"""
任务输入处理服务
处理语音、图片、文本三种输入源，将它们转换为结构化的今日任务数据
"""

import logging
import json
import base64
from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timezone
from io import BytesIO

# 可选依赖处理
try:
    from PIL import Image
    import pytesseract
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

from core.prompt_generation.prompt_template.task_input_template import (
    TASK_INPUT_PARSING_TEMPLATE, 
    TASK_INPUT_TYPES,
    SUBJECT_MAPPING
)
from service.doubao_service import get_doubao_service
from service.daily_task_service import DailyTaskService

logger = logging.getLogger(__name__)


class TaskInputService:
    """任务输入处理服务"""
    
    def __init__(self):
        """初始化服务"""
        self.doubao_service = get_doubao_service()
        self.daily_task_service = DailyTaskService()
    
    async def process_text_input(self, child_id: int, text_content: str) -> Dict[str, Any]:
        """
        处理文本输入
        
        Args:
            child_id: 学生ID
            text_content: 文本内容
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的文本输入")
            
            return await self._process_input(
                child_id=child_id,
                input_type="text",
                input_content=text_content
            )
            
        except Exception as e:
            logger.error(f"处理文本输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理文本输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "text",
                "tasks": []
            }
    
    async def process_voice_input(self, child_id: int, voice_text: str) -> Dict[str, Any]:
        """
        处理语音输入（已转换为文本）
        
        Args:
            child_id: 学生ID
            voice_text: 语音转文字结果
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的语音输入")
            
            return await self._process_input(
                child_id=child_id,
                input_type="voice",
                input_content=voice_text
            )
            
        except Exception as e:
            logger.error(f"处理语音输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理语音输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "voice",
                "tasks": []
            }
    
    async def process_image_input(self, child_id: int, image_data: Union[str, bytes]) -> Dict[str, Any]:
        """
        处理图片输入
        
        Args:
            child_id: 学生ID
            image_data: 图片数据（base64字符串或字节数据）
            
        Returns:
            Dict: 处理结果
        """
        try:
            logger.info(f"开始处理学生{child_id}的图片输入")
            
            # 提取图片中的文字
            ocr_text = self._extract_text_from_image(image_data)
            
            if not ocr_text.strip():
                return {
                    "success": False,
                    "message": "图片中未识别到文字内容",
                    "child_id": child_id,
                    "input_type": "image",
                    "tasks": []
                }
            
            logger.info(f"从图片中识别到文字: {ocr_text[:100]}...")
            
            return await self._process_input(
                child_id=child_id,
                input_type="image",
                input_content=ocr_text
            )
            
        except Exception as e:
            logger.error(f"处理图片输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理图片输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": "image",
                "tasks": []
            }
    
    def _extract_text_from_image(self, image_data: Union[str, bytes]) -> str:
        """
        从图片中提取文字

        Args:
            image_data: 图片数据

        Returns:
            str: 提取的文字
        """
        if not OCR_AVAILABLE:
            logger.error("OCR功能不可用，请安装PIL和pytesseract")
            return ""

        try:
            # 处理base64编码的图片
            if isinstance(image_data, str):
                # 移除data:image前缀（如果有）
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                image_bytes = base64.b64decode(image_data)
            else:
                image_bytes = image_data

            # 打开图片
            image = Image.open(BytesIO(image_bytes))

            # 使用OCR提取文字
            # 配置OCR参数，支持中英文
            try:
                custom_config = r'--oem 3 --psm 6 -l chi_sim+eng'
                text = pytesseract.image_to_string(image, config=custom_config)
                return text.strip()
            except:
                # 如果中文OCR失败，尝试英文OCR
                try:
                    text = pytesseract.image_to_string(image, lang='eng')
                    return text.strip()
                except:
                    # 如果都失败，使用默认配置
                    text = pytesseract.image_to_string(image)
                    return text.strip()

        except Exception as e:
            logger.error(f"图片文字提取失败: {e}")
            return ""
    
    async def _process_input(self, child_id: int, input_type: str, input_content: str) -> Dict[str, Any]:
        """
        处理输入内容的通用方法
        
        Args:
            child_id: 学生ID
            input_type: 输入类型
            input_content: 输入内容
            
        Returns:
            Dict: 处理结果
        """
        try:
            # 生成解析prompt
            parsing_prompt = TASK_INPUT_PARSING_TEMPLATE.format(
                input_type=TASK_INPUT_TYPES.get(input_type, input_type),
                input_content=input_content
            )
            
            logger.info(f"生成任务解析prompt，输入类型: {input_type}")
            
            # 调用豆包模型解析
            messages = [
                {
                    "role": "user",
                    "content": parsing_prompt
                }
            ]
            
            ai_result = self.doubao_service.chat_completion(
                messages=messages,
                temperature=0.3,  # 较低的温度确保结果稳定
                max_tokens=3000
            )
            
            if not ai_result.get("success", False):
                return {
                    "success": False,
                    "message": f"AI模型调用失败: {ai_result.get('error', '未知错误')}",
                    "child_id": child_id,
                    "input_type": input_type,
                    "input_content": input_content,
                    "tasks": []
                }
            
            logger.info("AI模型成功解析任务输入")
            
            # 解析AI响应
            ai_response = ai_result.get("response_text", "")
            parsed_tasks = self._parse_ai_response(ai_response)
            
            if not parsed_tasks:
                return {
                    "success": False,
                    "message": "解析AI响应失败，无法提取有效的任务数据",
                    "child_id": child_id,
                    "input_type": input_type,
                    "input_content": input_content,
                    "ai_response": ai_response,
                    "tasks": []
                }
            
            logger.info(f"成功解析出{len(parsed_tasks)}个任务")
            
            # 存储到今日任务表
            stored_task_ids = []
            for task in parsed_tasks:
                task_id = await self._store_daily_task(child_id, task, input_type)
                if task_id:
                    stored_task_ids.append(task_id)
                    logger.info(f"成功存储任务: {task.get('task_name', '')} (ID: {task_id})")
                else:
                    logger.error(f"存储任务失败: {task.get('task_name', '')}")
            
            logger.info(f"成功存储{len(stored_task_ids)}个任务到今日任务表")
            
            return {
                "success": True,
                "message": f"成功处理{input_type}输入，解析并存储{len(stored_task_ids)}个任务",
                "child_id": child_id,
                "input_type": input_type,
                "input_content": input_content,
                "tasks": parsed_tasks,
                "stored_task_ids": stored_task_ids,
                "total_tasks": len(parsed_tasks),
                "stored_tasks": len(stored_task_ids),
                "parsing_prompt": parsing_prompt,
                "ai_response": ai_response,
                "processed_at": datetime.now(timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"处理输入时发生错误: {e}", exc_info=True)
            return {
                "success": False,
                "message": f"处理输入失败: {str(e)}",
                "child_id": child_id,
                "input_type": input_type,
                "input_content": input_content,
                "tasks": []
            }

    def _parse_ai_response(self, ai_response: str) -> List[Dict[str, Any]]:
        """
        解析AI响应，提取任务数据

        Args:
            ai_response: AI响应文本

        Returns:
            List[Dict]: 解析后的任务列表
        """
        try:
            logger.info("开始解析AI响应")

            # 尝试提取JSON部分
            json_start = ai_response.find('[')
            json_end = ai_response.rfind(']') + 1

            if json_start == -1 or json_end == 0:
                # 如果没有找到数组格式，尝试查找对象格式
                json_start = ai_response.find('{')
                json_end = ai_response.rfind('}') + 1

                if json_start == -1 or json_end == 0:
                    logger.error("AI响应中未找到有效的JSON格式")
                    return []

            json_str = ai_response[json_start:json_end]

            # 清理JSON字符串
            json_str = self._clean_json_string(json_str)

            # 解析JSON
            parsed_data = json.loads(json_str)

            # 如果是单个对象，转换为列表
            if isinstance(parsed_data, dict):
                parsed_data = [parsed_data]

            # 验证和标准化任务数据
            validated_tasks = []
            for task in parsed_data:
                validated_task = self._validate_task_data(task)
                if validated_task:
                    validated_tasks.append(validated_task)

            logger.info(f"成功解析出{len(validated_tasks)}个有效任务")
            return validated_tasks

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"原始响应: {ai_response}")
            return []
        except Exception as e:
            logger.error(f"解析AI响应时发生错误: {e}")
            return []

    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串，移除可能的格式问题"""
        # 移除markdown代码块标记
        json_str = json_str.replace('```json', '').replace('```', '')

        # 移除多余的空白字符
        json_str = json_str.strip()

        # 替换可能的中文标点
        json_str = json_str.replace('"', '"').replace('"', '"')
        json_str = json_str.replace(''', "'").replace(''', "'")

        return json_str

    def _validate_task_data(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        验证和标准化任务数据

        Args:
            task: 原始任务数据

        Returns:
            Dict: 验证后的任务数据，无效则返回None
        """
        try:
            # 必需字段检查
            if not task.get("task_name") or not task.get("subject"):
                logger.warning(f"任务缺少必需字段: {task}")
                return None

            # 标准化学科名称
            subject = task.get("subject", "")
            standardized_subject = SUBJECT_MAPPING.get(subject, subject)

            # 标准化任务数据
            validated_task = {
                "task_name": str(task.get("task_name", "")).strip(),
                "subject": standardized_subject,
                "description": str(task.get("description", "")).strip(),
                "estimated_duration": max(task.get("estimated_duration", 30), 5),  # 最少5分钟
                "difficulty_level": min(max(task.get("difficulty_level", 3), 1), 5),  # 1-5级
                "materials_needed": str(task.get("materials_needed", "")).strip(),
                "time_requirement": str(task.get("time_requirement", "")).strip(),
                "priority": task.get("priority", "中"),
                "notes": str(task.get("notes", "")).strip(),
                "sub_tasks": []
            }

            # 处理子任务
            sub_tasks = task.get("sub_tasks", [])
            if isinstance(sub_tasks, list):
                for sub_task in sub_tasks:
                    if isinstance(sub_task, str):
                        validated_task["sub_tasks"].append({
                            "task": sub_task.strip(),
                            "source": "用户输入"
                        })
                    elif isinstance(sub_task, dict):
                        validated_task["sub_tasks"].append({
                            "task": str(sub_task.get("task", "")).strip(),
                            "source": str(sub_task.get("source", "用户输入"))
                        })

            return validated_task

        except Exception as e:
            logger.error(f"验证任务数据时发生错误: {e}")
            return None

    async def _store_daily_task(self, child_id: int, task: Dict[str, Any], input_type: str) -> Optional[int]:
        """
        存储任务到今日任务表

        Args:
            child_id: 学生ID
            task: 任务数据
            input_type: 输入类型

        Returns:
            Optional[int]: 任务ID，失败返回None
        """
        try:
            # 构建今日任务数据，匹配DailyTaskService.create_task的参数格式
            task_name = task["task_name"]
            description = task["description"]

            # 构建额外参数
            kwargs = {
                "subject": task["subject"],
                "estimated_duration": task["estimated_duration"],
                "task_date": datetime.now(timezone.utc),
                "status": "pending",  # 默认状态为待完成
                "notes": f"来源: {input_type}输入. {task.get('notes', '')}",
                "task_type": "homework",  # 默认为作业类型
                "confidence_index": 3,  # 默认信心指数
                "customization": f"优先级: {task.get('priority', '中')}. 材料: {task.get('materials_needed', '')}",
                "difficulty": f"难度等级: {task.get('difficulty_level', 3)}/5",
                "solution": task.get("time_requirement", ""),
            }

            # 调用今日任务服务存储
            result = self.daily_task_service.create_task(
                child_id=child_id,
                task_name=task_name,
                description=description,
                **kwargs
            )

            if result and isinstance(result, dict):
                task_id = result.get("id")
                if task_id:
                    logger.info(f"成功存储今日任务: {task['task_name']} (ID: {task_id})")

                    # 如果有子任务，创建子任务记录
                    if task.get("sub_tasks"):
                        await self._create_sub_tasks(task_id, task["sub_tasks"])

                    return task_id
                else:
                    logger.error(f"存储今日任务失败，未获取到任务ID: {task['task_name']}")
                    return None
            else:
                logger.error(f"存储今日任务失败: {task['task_name']}")
                return None

        except Exception as e:
            logger.error(f"存储今日任务时发生错误: {e}")
            return None

    async def _create_sub_tasks(self, daily_task_id: int, sub_tasks: List[Dict[str, Any]]) -> None:
        """
        创建子任务记录

        Args:
            daily_task_id: 主任务ID
            sub_tasks: 子任务列表
        """
        try:
            from core.daily_tasks.models.daily_task_models import TaskItem
            from core.user_management.database.connection import get_db_session_context

            with get_db_session_context() as session:
                for i, sub_task in enumerate(sub_tasks):
                    task_content = sub_task.get("task", "")
                    task_source = sub_task.get("source", "用户输入")

                    if task_content.strip():
                        item_data = {
                            "task_content": task_content,
                            "task_source": task_source,
                            "order_index": i + 1,
                            "estimated_minutes": 15,  # 默认15分钟
                            "points_earned": 5,  # 默认5积分
                            "points_reason": "完成子任务基础积分"
                        }

                        item = TaskItem.create_item(session, daily_task_id, task_content, **item_data)
                        if item:
                            logger.info(f"成功创建子任务: {task_content}")
                        else:
                            logger.error(f"创建子任务失败: {task_content}")

                session.commit()

        except Exception as e:
            logger.error(f"创建子任务时发生错误: {e}")


# 全局服务实例
_task_input_service_instance = None


def get_task_input_service() -> TaskInputService:
    """
    获取任务输入服务实例（单例模式）

    Returns:
        TaskInputService: 任务输入服务实例
    """
    global _task_input_service_instance
    if _task_input_service_instance is None:
        _task_input_service_instance = TaskInputService()
    return _task_input_service_instance
