# FastAPI主入口
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import socketio
import threading
import time
import base64
import numpy as np

from config.config import settings
from backend.utils.logging import setup_logger
from core.planning.endpoints import daily_learning, planning, plan_modification_api
from core.planning.endpoints.task_input_api import router as task_input_router
# from core.file_upload import endpoints as file_upload
from core.user_management.api import user_management_router
from core.prompt_generation.api import prompt_generation_router
from core.task_input.api import router as task_inputs_router
from api.v1.endpoints.doubao import router as doubao_router
from api.v1.endpoints.asr_api import router as asr_router
from api.v1.endpoints.asr_socketio_api import router as asr_socketio_router, create_socketio_handlers, connect_asr_service
from api.v1.endpoints.tts_api import router as tts_router
from api.v1.endpoints.voice_interaction_api import router as voice_interaction_router
from api.v1.endpoints.daily_tasks_api import router as daily_tasks_router
from api.v1.endpoints.multimodal_task_input_api import router as multimodal_task_input_router
from api.v1.endpoints.user_plan_actions_api import router as user_plan_actions_router
from api.v1.endpoints.detection_api import router as detection_router
from backend.utils.camera_api import router as camera_router
# from backend.utils.image_processing_api import router as image_processing_router
from service.socketio_service import init_socketio_service
import socketio

# 初始化日志配置
logger = setup_logger('main')
logger.info("🚀 初始化 Smart Friend FastAPI 应用日志系统")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="AI智能小孩学习助手API - 包含用户信息管理、学习数据分析和计划管理",
    version="1.0.0",
    docs_url="/docs",  # 确保docs页面在/docs路径
    redoc_url="/redoc",  # 确保redoc页面在/redoc路径
    openapi_url="/openapi.json"  # 简化openapi路径
)

logger.info("📋 Smart Friend FastAPI 应用创建完成")
logger.info("🔧 日志系统已初始化，开始记录规划模块操作")

# Socket.IO将在create_app函数中初始化

# 全局变量用于ASR
asr_client = None
asr_connected = False
is_recording = False
last_speech_time = time.time()
silence_timeout = 1.5

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由
# 用户信息管理API路由
app.include_router(
    user_management_router,
    prefix=f"{settings.API_V1_STR}/user-management",
)

# InfluxDB每日学习数据路由
app.include_router(
    daily_learning.router,
    prefix=f"{settings.API_V1_STR}/daily-learning",
    tags=["daily-learning"]
)

# InfluxDB学习计划数据路由
app.include_router(
    planning.router,
    prefix=f"{settings.API_V1_STR}/planning",
    tags=["planning"]
)

# 文件上传路由
# app.include_router(
#     file_upload.router,
#     prefix=f"{settings.API_V1_STR}/files",
#     tags=["file-upload"]
# )

# Prompt生成API路由
app.include_router(
    prompt_generation_router,
    prefix=f"{settings.API_V1_STR}/prompt-generation",
    tags=["prompt-generation"]
)

# 豆包模型API路由
app.include_router(
    doubao_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["doubao"]
)

# ASR语音识别API路由
app.include_router(
    asr_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["asr"]
)

# ASR Socket.IO API路由
app.include_router(
    asr_socketio_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["asr-socketio"]
)

# TTS文本转语音API路由
app.include_router(
    tts_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["tts"]
)

# 语音交互API路由
app.include_router(
    voice_interaction_router,
    prefix=f"{settings.API_V1_STR}",
    tags=["voice-interaction"]
)

# 计划表修改API路由
app.include_router(
    plan_modification_api.router,
    prefix=f"{settings.API_V1_STR}/planning",
    tags=["plan-modification"]
)

# 多模态任务输入API路由（规划模块）
app.include_router(
    task_input_router,
    prefix=f"{settings.API_V1_STR}/planning/task-input",
    tags=["planning-task-input"]
)

# 当日任务管理API路由
app.include_router(
    daily_tasks_router,
    prefix=f"{settings.API_V1_STR}/daily-tasks",
    tags=["daily-tasks"]
)

# 摄像头管理API路由
app.include_router(
    camera_router,
    prefix=f"{settings.API_V1_STR}/camera",
    tags=["camera"]
)

# 检测API路由
app.include_router(
    detection_router,
    prefix=f"{settings.API_V1_STR}/detection",
    tags=["detection"]
)

# 图像预处理API路由
# app.include_router(
#     image_processing_router,
#     prefix=f"{settings.API_V1_STR}/image-processing",
#     tags=["image-processing"]
# )

# 多模态任务输入API路由
app.include_router(
    multimodal_task_input_router,
    prefix=f"{settings.API_V1_STR}/multimodal-task-input",
    tags=["multimodal-task-input"]
)

# 任务计划API路由
from api.v1.endpoints.task_plan_api import router as task_plan_router
app.include_router(
    task_plan_router,
    tags=["task-plan"]
)

# 任务确认API路由
from api.task_confirm_api import router as task_confirm_router
app.include_router(
    task_confirm_router,
    prefix="/api",
    tags=["task-confirm"]
)

# 子任务操作API路由
from api.subtask_api import router as subtask_router
app.include_router(
    subtask_router,
    prefix="/api",
    tags=["subtask"]
)

# 用户计划表操作记录API路由
app.include_router(
    user_plan_actions_router,
    prefix=f"{settings.API_V1_STR}/user-plan-actions",
    tags=["user-plan-actions"]
)

# 用户任务输入记录API路由
app.include_router(
    task_inputs_router,
    # prefix已在router中定义，无需重复添加
    tags=["用户任务输入记录"]
)

# 任务计划管理API路由
from backend.api.task_plans import router as task_plans_router
app.include_router(
    task_plans_router,
    tags=["task-plans"]
)

# 静态文件服务
app.mount("/static", StaticFiles(directory="templates"), name="static")  # 将templates目录挂载到/static路径
app.mount("/templates", StaticFiles(directory="templates"), name="templates")

@app.get("/demo")
async def demo_page():
    """豆包API演示页面"""
    return FileResponse("static/doubao_demo.html")

@app.get("/")
async def root():
    """根路径 - 返回AI Child页面"""
    return FileResponse("templates/aiChild.html")

@app.get("/aiChild")
async def ai_child_page():
    """返回AI Child页面"""
    return FileResponse("templates/aiChild.html")

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy"}

def main(port):
    """主函数 - 用于调试和启动应用"""
    print("🚀 启动 Smart Friend FastAPI 应用...")
    print(f"📋 应用标题: {app.title}")
    print(f"📋 应用版本: 1.0.0")
    print(f"📍 API 文档地址: http://localhost:{port}/docs")
    print(f"📍 前端页面地址: http://localhost:{port}/static/aiChild.html")

    # 初始化Socket.IO服务
    socketio_service = init_socketio_service()

    # 集成ASR Socket.IO处理器
    create_socketio_handlers(socketio_service.sio)

    # 自动连接ASR服务（同步调用）
    import asyncio
    try:
        asr_result = asyncio.run(connect_asr_service())
        if asr_result["success"]:
            print(f"✅ ASR服务自动连接成功: {asr_result['message']}")
        else:
            print(f"❌ ASR服务自动连接失败: {asr_result['message']}")
    except Exception as e:
        print(f"❌ ASR服务连接出错: {e}")

    socket_app = socketio_service.get_asgi_app(app)
    print("🔌 Socket.IO服务已初始化（包含ASR功能）")

    # # 打印所有注册的路由
    # print("\n📡 已注册的路由:")
    # route_count = 0
    # file_route_count = 0

    # for route in app.routes:
    #     if hasattr(route, 'path'):
    #         route_count += 1
    #         print(f"  - {route.path}")
    #         if '/files' in route.path:
    #             file_route_count += 1
    #             print(f"    ✅ 文件上传路由: {route.path}")

    # print(f"\n📊 路由统计:")
    # print(f"  - 总路由数: {route_count}")
    # print(f"  - 文件上传路由数: {file_route_count}")

    print(f"\n🌐 启动服务器...")
  
    print(f"📍 访问地址: http://localhost:{port}")
    print(f"📖 API文档: http://localhost:{port}/docs")
    print(f"📖 API文档: http://**************:{port}/docs")
    print(f"🔧 文件上传API: http://localhost:{port}{settings.API_V1_STR}/files/")
    print(f"🌐 前端页面: http://localhost:{port}/static/aiChild.html")

    import os
    import webbrowser
    # 构建 HTML 文件的绝对路径
    html_path = os.path.abspath(os.path.join('templates', 'aiChild.html'))
    # 检查文件是否存在
    if os.path.exists(html_path):
        # 在默认浏览器中打开 HTML 文件
        webbrowser.open_new_tab(f'http://localhost:{port}/static/aiChild.html')
    else:
        print(f"❌ 未找到 HTML 文件: {html_path}")

    return socket_app


if __name__ == "__main__":
    import uvicorn
    mainport=8002
    # 调用main函数进行初始化检查
    app_instance = main(mainport)

    # 启动服务器
    uvicorn.run(app_instance, host="0.0.0.0", port=mainport)