#!/usr/bin/env python3
"""
Test script to check Doubao API endpoints
"""
import requests
import json

# Configuration
DOUBAO_API_KEY = "945a7413-a966-4215-bdbc-80ed84e92555"

# Different possible endpoints
ENDPOINTS = [
    "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
    "https://open.volcengineapi.com/api/v3/chat/completions", 
    "https://ark.cn-beijing.volces.com/api/v1/chat/completions",
    "https://api.volcengineapi.com/api/v3/chat/completions"
]

def test_endpoint(endpoint_url):
    """Test a single endpoint"""
    print(f"\n{'='*60}")
    print(f"Testing endpoint: {endpoint_url}")
    print(f"{'='*60}")
    
    headers = {
        "Authorization": f"Bearer {DOUBAO_API_KEY}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "doubao-pro-4k",
        "messages": [{"role": "user", "content": "Hello, this is a test message."}],
        "temperature": 0.7,
        "max_tokens": 100
    }
    
    try:
        print("Making request...")
        response = requests.post(endpoint_url, headers=headers, json=payload, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SUCCESS!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print("❌ FAILED!")
            print(f"Error Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ CONNECTION ERROR: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ TIMEOUT ERROR: {e}")
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def main():
    print("Testing Doubao API Endpoints...")
    print(f"API Key: {DOUBAO_API_KEY[:10]}...{DOUBAO_API_KEY[-10:]}")
    
    working_endpoints = []
    
    for endpoint in ENDPOINTS:
        if test_endpoint(endpoint):
            working_endpoints.append(endpoint)
    
    print(f"\n{'='*60}")
    print("SUMMARY")
    print(f"{'='*60}")
    
    if working_endpoints:
        print("✅ Working endpoints:")
        for endpoint in working_endpoints:
            print(f"  - {endpoint}")
    else:
        print("❌ No working endpoints found!")
        print("\nPossible issues:")
        print("1. Invalid API key")
        print("2. API key doesn't have access to these endpoints")
        print("3. Different authentication method required")
        print("4. Network/firewall issues")
        print("5. Service temporarily unavailable")

if __name__ == "__main__":
    main()
