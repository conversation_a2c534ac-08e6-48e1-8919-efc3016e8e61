import requests  # For API calls
from typing import Dict, Any, List
import json

# Configuration - replace with your actual credentials and endpoints
#OPENMANUS_API_KEY = ""
DOUBAO_API_KEY = "945a7413-a966-4215-bdbc-80ed84e92555"
OPENMANUS_BASE_URL = "https://api.openmanus.com/v1"
DOUBAO_API_URL = "https://api.doubao.com/v1/chat/completions"  # Example endpoint, adjust as needed

class OpenManusPlanner:
    def __init__(self):
        self.tools = {
            "retriever": self.retrieve_information,
            "summarizer": self.summarize_content,
            "response_generator": self.generate_response
        }
        self.context = {}
        self.headers = {
            "Authorization": f"Bearer {DOUBAO_API_KEY}",
            "Content-Type": "application/json"
        }
    
    def call_doubao_api(self, prompt: str) -> str:
        """
        Call Doubao's API to get a completion
        """
        payload = {
            "model": "doubao-pro",  # Adjust model name as needed
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2000
        }
        
        try:
            response = requests.post(
                DOUBAO_API_URL,
                headers=self.headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            print(f"Error calling Doubao API: {e}")
            return f"API Error: {str(e)}"
    
    def call_openmanus(self, task: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Call OpenManus API to get a plan for the given task
        """
        # This is a mock implementation - replace with actual OpenManus API call
        print(f"Calling OpenManus with task: {task}")
        
        # Example mock response from OpenManus
        if task == "research_and_summarize":
            return {
                "plan": [
                    {"tool": "retriever", "parameters": {"query": parameters["query"]}},
                    {"tool": "summarizer", "parameters": {"content": "{{retriever.output}}", "length": "medium"}},
                    {"tool": "response_generator", "parameters": {"summary": "{{summarizer.output}}", "question": parameters["query"]}}
                ]
            }
        else:
            return {"plan": [{"tool": "response_generator", "parameters": parameters}]}
    
    def retrieve_information(self, query: str) -> Dict[str, Any]:
        """
        Retrieve relevant information based on a query
        """
        print(f"Retrieving information for: {query}")
        # Replace with actual retrieval logic (could be vector DB, search API, etc.)
        retrieved_content = f"Information about {query}: This is a detailed explanation of {query} covering all important aspects."
        return {"output": retrieved_content, "status": "success"}
    
    def summarize_content(self, content: str, length: str = "medium") -> Dict[str, Any]:
        """
        Summarize the given content using Doubao
        """
        print(f"Summarizing content with {length} length")
        prompt = f"Summarize the following content in {length} length:\n\n{content}"
        summary = self.call_doubao_api(prompt)
        return {"output": summary, "status": "success"}
    
    def generate_response(self, question: str, summary: str = None) -> Dict[str, Any]:
        """
        Generate a final response to the user's question using Doubao
        """
        print("Generating final response")
        if summary:
            prompt = f"Based on the following summary:\n{summary}\n\nAnswer this question: {question}"
        else:
            prompt = f"Answer this question: {question}"
        
        response = self.call_doubao_api(prompt)
        return {"output": response, "status": "success"}
    
    def execute_plan(self, plan: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Execute a plan step by step, passing outputs between steps
        """
        results = {}
        for step in plan:
            tool_name = step["tool"]
            parameters = step["parameters"]
            
            # Replace template variables with actual values from previous steps
            resolved_params = {}
            for key, value in parameters.items():
                if isinstance(value, str) and value.startswith("{{") and value.endswith("}}"):
                    var_name = value[2:-2].split(".")[0]  # Extract variable name from {{var.output}}
                    if var_name in results:
                        resolved_params[key] = results[var_name]["output"]
                else:
                    resolved_params[key] = value
            
            # Execute the tool
            if tool_name in self.tools:
                print(f"Executing {tool_name} with params: {resolved_params}")
                result = self.tools[tool_name](**resolved_params)
                results[tool_name] = result
            else:
                raise ValueError(f"Unknown tool: {tool_name}")
        
        return results
    
    def process_task(self, task: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main method to process a task using OpenManus as planner
        """
        # Get plan from OpenManus
        plan_response = self.call_openmanus(task, parameters)
        
        if "plan" not in plan_response:
            raise ValueError("OpenManus did not return a valid plan")
        
        # Execute the plan
        execution_results = self.execute_plan(plan_response["plan"])
        
        # Return final output (assuming last step is the response generator)
        return execution_results.get("response_generator", {}).get("output", "No output generated")


# Example usage
if __name__ == "__main__":
    planner = OpenManusPlanner()
    
    # Example loop for continuous processing
    while True:
        user_input = input("Enter your query (or 'quit' to exit): ")
        if user_input.lower() == 'quit':
            break
        
        # Process the task using OpenManus as planner
        result = planner.process_task(
            task="research_and_summarize",
            parameters={"query": user_input}
        )
        
        print("\nFinal Response:")
        print(result)
        print("\n" + "="*50 + "\n")